Remove user access
---
tags:
  - Strategy Detailing and Expansion
description:
  Revoke access of a particular strategy from a selected user. <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> Yes (all strategies)<br><br>
  <b>MANAGER:</b> Yes (own strategies and underlying developers)<br><br>
  <b>DEVELOPER:</b> No<br><br>

parameters:
  - in: formData
    name: strategy_name
    type: string
    required: True
  - in: formData
    name: username
    type: string
    required: True

responses:
  200:
    description:
      When request is successful
      <center><b>review_strategy_dashboard.html</b></center>
    schema:
      $ref: '#/definitions/TemplateResponse'
  404:
    description:
      When strategy name does not exist in the database
    schema:
      $ref: '#/definitions/ErrorTemplateResponse'
  403:
    description:
      When authorization fails
    schema:
      $ref: '#/definitions/ForbiddenTemplateResponse'
