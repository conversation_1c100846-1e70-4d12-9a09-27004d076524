Checks if the cluster performance files are present in minio
---

tags:
  - Strategy Detailing and Expansion
description:
  Checks if the cluster performance file for the given strategy is present in minio or not. <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> Yes (all strategies)<br><br>
  <b>MANAGER:</b> Yes (own strategies and underlying developers)<br><br>
  <b>DEVELOPER:</b> No<br><br>

parameters:
  - in: formData
    name: strategy
    type: string
    required: True
  - in: formData
    name: cluster
    type: string
    required: True

responses:
  200:
    description:
      After request is processed
    schema:
      $ref: '#/definitions/ApiResponse'