Display the next function profiler of a strategy
---
tags:
  - Strategy Detailing and Expansion
description:
  Display the next function profiler of a strategy. <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> Yes (all strategies)<br><br>
  <b>MANAGER:</b> Yes (own strategies and underlying developers)<br><br>
  <b>DEVELOPER:</b> Yes (own strategies)<br><br>

parameters:
  - in: formData
    name: strategy
    type: string
    required: True

responses:
  200:
    description:
      When request is processed <br>
      Response is either an empty string or the next function profiler html code
