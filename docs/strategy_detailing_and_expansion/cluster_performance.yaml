Fetch the cluster performance for a given strategy and cluster
---
tags:
  - Strategy Detailing and Expansion
description:
  Fetch the cluster performance for a given strategy and cluster. <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> Yes (all strategies)<br><br>
  <b>MANAGER:</b> Yes (own strategies and underlying developers)<br><br>
  <b>DEVELOPER:</b> No<br><br>

parameters:
  - in: path
    name: strategy_name
    type: string
    required: True
  - in: path
    name: cluster_mapping
    type: string
    required: True
responses:
  404:
    description:
      When strategy name does not exist in the database
    schema:
      $ref: '#/definitions/ErrorTemplateResponse'
  403:
    description:
      When authorization fails
    schema:
      $ref: '#/definitions/ForbiddenTemplateResponse'
  200:
    description:
      When cluster performance is fetched
      <center><b>cluster_performance.html</b></center>
    schema:
      $ref: '#/definitions/TemplateResponse'
