Checks if the performance metric file is present in minio
---

tags:
  - Strategy Detailing and Expansion
description:
  Checks if the performance metrics for the given strategy is present in minio or not. <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> Yes (all strategies)<br><br>
  <b>MANAGER:</b> Yes (own strategies and underlying developers)<br><br>
  <b>DEVELOPER:</b> Yes (own live, dead and strategies)<br><br>

parameters:
  - in: formData
    name: strategy
    type: string
    required: True
  - in: formData
    name: week
    type: string
    required: True


responses:
  200:
    description:
      After request is processed
    schema:
      $ref: '#/definitions/ApiResponse'