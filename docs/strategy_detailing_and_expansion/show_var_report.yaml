Display the VaR report of a strategy
---
tags:
  - Strategy Detailing and Expansion
description:
  Display the VaR report of a strategy. <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> Yes (all strategies)<br><br>
  <b>MANAGER:</b> Yes (own strategies and underlying developers)<br><br>
  <b>DEVELOPER:</b> Yes(own live, dead and rejected strategies) <br><br>

parameters:
  - in: path
    name: strategy_name
    type: string
    required: True
  - in: path
    name: week
    type: string
    required: True

responses:
  404:
    description:
      When strategy name does not exist in the database
    schema:
      $ref: '#/definitions/ErrorTemplateResponse'
  403:
    description:
      When authorization fails
    schema:
      $ref: '#/definitions/ForbiddenTemplateResponse'
  200:
    description:
      When VaR report is fetched
      <center><b>report.html</b></center>
    schema:
      $ref: '#/definitions/TemplateResponse'
