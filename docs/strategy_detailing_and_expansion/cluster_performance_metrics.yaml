Display the performance metrics of a cluster
---
tags:
  - Strategy Detailing and Expansion
description:
  Display the performance metric of a cluster based on week <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> Yes (all live clusters)<br><br>
  <b>MANAGER:</b> Yes (own and underlying developers live clusters)<br><br>
  <b>DEVELOPER:</b> Yes (own live clusters)<br><br>

parameters:
  - in: path
    name: cluster_name
    type: string
    required: True
  - in: path
    name: week
    type: string
    required: True

responses:
  404:
    description:
      When strategy name does not exist in the database
    schema:
      $ref: '#/definitions/ErrorTemplateResponse'
  403:
    description:
      When authorization fails
    schema:
      $ref: '#/definitions/ForbiddenTemplateResponse'
  200:
    description:
      When cluster performance metric is fetched
      <center><b>performance_metric.html</b></center>
    schema:
      $ref: '#/definitions/TemplateResponse'