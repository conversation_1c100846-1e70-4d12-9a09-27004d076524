Strategy review portal
---

tags:
  - Strategy Review
description:
  Fetches the strategy review portal. <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> Yes (all strategies)<br><br>
  <b>MANAGER:</b> Yes (own strategies and underlying developers)<br><br>
  <b>DEVELOPER:</b> No<br><br>

responses:
  200:
    description:
      When the user is authenticated
      <center><b>review_strategy_dashboard.html</b></center>
    schema:
      $ref: '#/definitions/TemplateResponse'
  302:
    description:
      When a developer tries to access this page
      <center><b>home.html</b></center>
    schema:
      $ref: '#/definitions/TemplateResponse'
