Strategy review portal
---

tags:
  - Strategy Review
description:
  Validates if the given review request consists of atleast 1 valid strategy or not. <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> Yes (all strategies)<br><br>
  <b>MANAGER:</b> Yes (own strategies and underlying developers)<br><br>
  <b>DEVELOPER:</b> No<br><br>

parameters:
  - in: formData
    name: strategies
    schema:
      type: array
      items:
        type: string
      minItems: 1
    required: True
responses:
  200:
    description:
      When there is at least 1 valid strategy
      <center><b>review_strategy_dashboard.html</b></center>
    schema:
      $ref: '#/definitions/ApiResponse'
  302:
    description:
      When a developer tries to access this page
      <center><b>home.html</b></center>
    schema:
      $ref: '#/definitions/TemplateResponse'
