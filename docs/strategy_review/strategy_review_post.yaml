Review strategy
---

tags:
  - Strategy Review
description:
  Validates the information submitted and adds review to a strategy on success. <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> Yes (all strategies)<br><br>
  <b>MANAGER:</b> Yes (own strategies and underlying developers)<br><br>
  <b>DEVELOPER:</b> No<br><br>

parameters:
  - in: path
    name: strategy_name
    type: string
  - in: formData
    name: timecheck
    type: string
  - in: formData
    name: correlation_check
    type: string
  - in: formData
    name: trade_distribution_check
    type: string
  - in: formData
    name: risk_analysis
    type: string
  - in: formData
    name: num_days_trading
    type: string
  - in: formData
    name: comments
    type: string
  - in: formData
    name: to_change
    type: string
  - in: formData
    name: action
    type: string
    required: True
responses:
  200:
    description:
      After processing request
      <center><b>strategy_review.html</b></center>
    schema:
      $ref: '#/definitions/TemplateResponse'
  
  404:
    description:
      When strategy name entered does not exist in the database
    schema:
      $ref: '#/definitions/ErrorTemplateResponse'
  403:
    description:
      When authorization fails
    schema:
      $ref: '#/definitions/ForbiddenTemplateResponse'