Display the review of a strategy
---
tags:
  - Strategy Review
description:
  Display the review of a strategy. <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> Yes (all strategies)<br><br>
  <b>MANAGER:</b> Yes (own strategies and underlying developers)<br><br>
  <b>DEVELOPER:</b> Yes (own strategies)<br><br>

parameters:
  - in: path
    name: strategy_name
    type: string
    required: True

responses:
  200:
    description:
      When strategy review is fetched
      <center><b>strategy_review_display.html</b></center>
    schema:
      $ref: '#/definitions/TemplateResponse'
  
  404:
    description:
      When strategy name entered does not exist in the database
    schema:
      $ref: '#/definitions/ErrorTemplateResponse'
  403:
    description:
      When authorization fails
    schema:
      $ref: '#/definitions/ForbiddenTemplateResponse'