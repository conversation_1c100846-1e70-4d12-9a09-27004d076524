Control if users are allowed to modify the pending strategy
---

tags:
  - control modification
description:
  Toggle state for control modification field. <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> Yes (all strategies)<br><br>
  <b>MANAGER:</b> Yes (own strategies and underlying developers)<br><br>
  <b>DEVELOPER:</b> No<br><br>
parameters:
  - in: path
    name: strategy_name
    type: string
    required: True
responses:
  200:
    description:
      When state is successfully toggled
  403:
    description:
      When authorization fails
    schema:
      $ref: '#/definitions/ForbiddenTemplateResponse'