Review strategy
---

tags:
  - Strategy Review
description:
  Fetches strategy review form. <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> Yes (all strategies)<br><br>
  <b>MANAGER:</b> Yes (own strategies and underlying developers)<br><br>
  <b>DEVELOPER:</b> No<br><br>
parameters:
  - in: path
    name: strategy_name
    type: string
    required: True
responses:
  200:
    description:
      When strategy review form is successfully fetched
      <center><b>strategy_review.html</b></center>
    schema:
      $ref: '#/definitions/TemplateResponse'
  404:
    description:
      When strategy name does not exist in the database
    schema:
      $ref: '#/definitions/ErrorTemplateResponse'
  403:
    description:
      When authorization fails
    schema:
      $ref: '#/definitions/ForbiddenTemplateResponse'