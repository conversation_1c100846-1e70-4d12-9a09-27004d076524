# **Samba Test Mode Documentation**

---

## **What comes in new with this change?**

- Allowing pending strategies to be pushed to a **“Test-only mode”** before pushing them directly to “Live mode”.
- Allowing **strategy reworks** to run in the Test environment without affecting the original strategy’s presence in the Live environment.
- Maintaining **separate and independent strategy cluster mappings** for the two Balte environments: Test and Live.

---

## **How will the strategy lifecycle be affected?**
![Strategy Lifecyle Diagram](img/stratLifecycleFlowchart.png)

1. Accepting a Pending strategy to “Live mode” (same as earlier)
2. **Accepting a Pending strategy to “Test-only mode” (new)**
3. **Accepting a “Test-only mode” strategy into “Live mode” (new)**
4. **Rejecting a “Test-only mode” strategy (new)**
5. Rejecting/Deleting a Pending strategy (same as earlier)
6. Killing a “Live mode” strategy (same as earlier)

Newly allowed actions (2), (3), and (4) will be accessible through the **strategy review form**. The strategy review form is accessed through the strategy expand page.

<div style="text-align: center;">
  <b>Strategy review form for a “Pending” strategy</b>
  <img src="/img/stratReviewFormPending.png" alt="Strategy review form for a “Pending” strategy" width="500">
</div>

<div style="text-align: center;">
  <b>Strategy review form for a “Test-only mode” strategy</b>
  <img src="/img/stratReviewFormTest.png" alt="Strategy review form for a “Test-only mode” strategy" width="500">
</div>

 **Important:**  
⭐ A “Live mode” strategy cannot be pushed to “Test-only mode.” It can only be moved to the Dead state.  
⭐ “Test-only mode” strategies will not be run by the backtest service. The backtest service will only run Pending strategies, as before.

---

## **How will Modes and Environments be maintained?**
![Modes and Balte environments](img/modeEnv.png)

Internally, the two Balte environments will run strategies as shown in the figure above. 
The Live Balte environment will run the strategies which are in “Live mode” whereas the Test Balte environment will run strategies which are in “Test-only mode” plus the “Live mode” strategies which do not have a reworked version in “Test-only mode”. 
The “Test-only mode” strategies can be viewed by manager/admin on the new **Test Strategies page.** 

<div style="text-align: center;">
  <b>Test Strategies Page</b>
  <img src="/img/testStrats.png" alt="Test Strategies Page" width="800">
</div>

Strategy developers can see their strategies which are in “Test-only mode” on the **Pending strategies page** itself. Such strategies will be shown in the same table with rows differently color coded. 

<div style="text-align: center;">
  <b>Pending Strategies Page</b>
  <img src="/img/pendingStrats.png" alt="Pending Strategies Page" width="800">
</div>

 **Important:**  
⭐ “Test-only mode” strategies **cannot** be modified or deleted directly from the Pending strategies page. Deletion/ Rejection can be done via the strategy review form.

⭐ The comments dashboard will also display comments for “Test-only mode” strategies.  
⭐ A “Live mode” strategy, when made dead, will stop running in both Balte environments.

---

## **How will strategy cluster mappings be maintained?**
![Cluster Mapping Diagram](img/clusterMappingDiagram.png)

Users can manage cluster mappings separately for both the Test and Live Balte environments. This will be done via the **Manage Cluster Mapping** page, which now contains two tabs for strategies in Live and Test Balte environments. The usage will be the same as before. 

<div style="text-align: center;">
  <b>Manage Cluster Mapping Page</b>
  <img src="/img/manageClusterMappingPage.png" alt="Manage Cluster Mapping Page" width="800">
</div>

 **Important:**  
⭐ The Live strategies tab will only allow modification for strategies running in the Live Balte environment.  
⭐ The Test strategies tab will only allow modification for strategies running in the Test Balte environment.

⭐ The cluster mapping of a strategy being used in the Test Balte environment can be modified as soon as the strategy is moved out of the Pending state to the “Live mode” or the “Test-only mode”. The initial cluster mapping being used in the Test Balte environment is set the same as what was submitted in the Strategy submission form.

⭐ The cluster mapping of a strategy being used in the Live Balte environment can be modified only when it is pushed to the “Live mode” (either from the Pending state or from the “Test-only mode”). The initial cluster mapping being used in the Live Balte environment is set the same as what was submitted in the Strategy submission form.


The strategy’s cluster mapping can be viewed on the **Strategy Expand** page. For Pending strategies, only the cluster mapping submitted during strategy addition will be displayed. For all other modes, separate cluster mappings will be shown for the live and test environments.

<div style="text-align: center;">
  <b>Strategy Expand Page (For a Test-only mode strategy)</b>
  <img src="/img/stratExpandPage.png" alt="Strategy Expand Page (For a Test-only mode strategy)" width="800">
</div>

---

## **What’s new for strategy reworks?**
Reworked strategies can now be accepted into “Test-only mode” without affecting the original strategy’s run in the Live Balte environment. The original strategy will no longer run in the Test environment, but its rework will run in the Test environment.
As before, Reworked strategies can also be pushed to “Live mode,” replacing the original strategy in both the Test and Live environments, and moving the original strategy to the Dead state.

![Rework Diagram](img/reworkDiagram.png)

 **Important:**  
⭐ A strategy rework can be pushed to “Test-only mode” or “Live mode” only when the original strategy is not in “Test-only mode.”

---
