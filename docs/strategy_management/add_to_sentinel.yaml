Add strategy to the sentinel backtest list
---
tags:
  - Strategy Management
description:
  Add strategy to the sentinel backtest list in minio <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> Yes (all strategies)<br><br>
  <b>MANAGER:</b> Yes (own strategies and underlying developers)<br><br>
  <b>DEVELOPER:</b> Yes (own strategies)<br><br>

parameters:
  - in: formData
    name: strategy
    type: string
    required: True

  - in: formData
    name: action
    type: string
    enum:
      - Add
      - Delete
    required: True

  - in: formData
    name: service
    type: string
    enum:
      - dead_backtest
      - rejected_backtest
      - kivifolio
    required: True

responses:
  200:
    description:
      After request is processed
    schema:
      $ref: '#/definitions/SentinelResponse'