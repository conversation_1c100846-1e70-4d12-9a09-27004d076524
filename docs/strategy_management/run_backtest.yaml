Trigger a backtest for the given strategy
---
tags:
  - Strategy Management
description:
  Trigger a backtest for the given strategy. <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> Yes (all strategies)<br><br>
  <b>MANAGER:</b> Yes (own strategies and underlying developers)<br><br>
  <b>DEVELOPER:</b> No<br><br>

parameters:
  - in: formData
    name: strategy
    type: string
    required: True

  - in: formData
    name: service_index
    type: string
    required: True

responses:
  200:
    description:
      After request is processed
    schema:
      $ref: '#/definitions/ApiResponse'
