Add cluster to the cluster backtest list
---
tags:
  - Strategy Management
description:
  Add cluster to the cluster backtest list <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> Yes (all clusters)<br><br>
  <b>MANAGER:</b> Yes (own clusters and underlying developers)<br><br>
  <b>DEVELOPER:</b> Yes (own clusters)<br><br>

parameters:
  - in: formData
    name: cluster
    type: string
    required: True

  - in: formData
    name: slaves
    type: string
    required: True
  
responses:
  302:
    description:
      After the request is processed
      <center><b>cluster_backtest.html</b></center>
    schema:
      $ref: '#/definitions/TemplateResponse'