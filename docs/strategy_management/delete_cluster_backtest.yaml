Delete cluster submitted for cluster backtest
---
tags:
  - Strategy Management
description:
  Delete cluster submitted for cluster backtest <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> Yes (all clusters)<br><br>
  <b>MANAGER:</b> Yes (own clusters and underlying developers)<br><br>
  <b>DEVELOPER:</b> Yes (own clusters)<br><br>

parameters:
  - in: formData
    name: cluster
    type: string
    required: True
    
responses:
  200:
    description:
      After the request is processed
    schema:
      $ref: '#/definitions/ApiResponse'