ApiResponse:
  type: string
  example: ["success", "failed"]

SentinelResponse:
  type: string
  example: ["status_failed", "invalid_strategy", "duplicate_strategy", "exceeded_limit", "empty_strategy", "failed", "success"]

GetSlavesResponse:
  type: string
  example: ["failed", "<SLAVE LIST SEPARATED BY COLON>"]

TemplateResponse:
  type: template
  example: | 
    <head>
    </head>
    <body>
    </body>

ErrorTemplateResponse:
  type: template
  example: |
    <!doctype html>
    <html lang=en>
    <title>404 Not Found</title>
    <h1>Not Found</h1>
    <p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>

ForbiddenTemplateResponse:
  type: template
  example: |
    <!doctype html>
    <html lang=en>
    <title>403 Forbidden</title>
    <h1>Forbidden</h1>
    <p>You don&#39;t have the permission to access the requested resource. It is either read-protected or not readable by the server.</p>