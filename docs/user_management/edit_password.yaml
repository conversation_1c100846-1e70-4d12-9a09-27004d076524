Change password for a user
---
tags:
  - User Management
description:
  Changes password for a user. <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> Yes (all users)<br><br>
  <b>MANAGER:</b> Yes (underlying developers)<br><br>
  <b>DEVELOPER:</b> No<br><br>
parameters:
  - in: formData
    name: username
    type: string
    required: True
  - in: formData
    name: password
    schema:
      type: string
      format: password
    required: True
  - in: formData
    name: confirm_password
    schema:
      type: string
      format: password
    required: True
responses:
  200:
    description:
      After request is processed successfully
    schema:
      $ref: '#/definitions/ApiResponse'