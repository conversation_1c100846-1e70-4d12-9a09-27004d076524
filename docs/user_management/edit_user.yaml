Change role for a user
---
tags:
  - User Management
description:
  Change role of a user. <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> Yes (all users)<br><br>
  <b>MANAGER:</b> Yes (underlying developers)<br><br>
  <b>DEVELOPER:</b> No<br><br>

parameters:
  - in: formData
    name: username
    type: string
    required: True
  - in: formData
    name: role
    type: string
    required: True
  - in: formData
    name: manager
    description: required only when new role is a developer
    type: string
responses:
  200:
    description:
      After request is processed successfully
    schema:
      $ref: '#/definitions/ApiResponse'

definitions:
  import: "/docs/definitions.yaml"
  import: "/docs/models.yaml"