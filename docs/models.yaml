User:
  type: object
  properties:
    id:
      type: int
      description: PRIMARY KEY
    username:
      type: string
    email:
      type: string
    password_enc:
      type: password
    role_id:
      type: int
    manager:
      type: string

Role:
  type: object
  properties:
    id:
      type: int
      description: PRIMARY KEY
    name:
      type: string

PendingBacktests:
  type: object
  properties:
    id:
      type: int
      description: PRIMARY KEY
    request_time:
      type: date
    strategy_name:
      type: string
    service_state:
      type: string

Strategy:
  type: object
  properties:
    strategy_name:
      type: string
      description: PRIMARY_KEY
    live_start_day:
      type: date
    last_run_day:
      type: date
    developer:
      type: string
    backtest_start_date:
      type: date
    segment:
      type: string
    exchange_name:
      type: string
    long_short:
      type: int
    book_long:
      type: string
    book_short:
      type: string
    strat_state:
      type: int
    submission_day:
      type: date
    trigger_coeff:
      type: float
    limit_coeff:
      type: float
    expiration_time:
      type: time
    cluster_mapping:
      type: string
    comments:
      type: string
    reworked_strategy:
      type: string

Status:
  type: object
  properties:
    id:
      type: int
      description: PRIMARY KEY
    state_description:
      type: string
    
StrategyMetaData:
  type: object
  properties:
    strategy_name:
      type: string
      description: PRIMARY KEY
    max_dd_submit:
      type: float
    monthly_sharpe_submit:
      type: float
    ret_dd_submit:
      type: float
    max_dd_monte:
      type: float
    percentile_dd:
      type: float
    curr_backtest_dd:
      type: float
    last_sentinel_run:
      type: float
    monthly_ret_submit:
      type: float
    monthly_ret_post:
      type: float
    monthly_sharpe_post:
      type: float

StrategyReview:
  type: object
  properties:
    strategy_name:
      type: string
      description: PRIMARY KEY
    timecheck:
      type: string
    correlation_check:
      type: string
    trade_distribution_check:
      type: string
    risk_analysis:
      type: string
    num_days_trading:
      type: string
    comments:
      type: string
    to_change:
      type: string