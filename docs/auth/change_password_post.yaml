Change user password
---
tags:
  - Authentication
description:
  Validates the submitted information and changes the password on success. <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> Yes<br><br>
  <b>MANAGER:</b> Yes<br><br>
  <b>DEVELOPER:</b> Yes<br><br>
parameters:
  - in: formData
    name: new_password
    schema:
      type: string
      format: password
    required: True
  - in: formData
    name: confirm_new_password
    schema:
      type: string
      format: password
    required: True

responses:
  200:
    description:
      After request is successfully processed
      <center><b>profile.html</b></center>
    schema:
      $ref: '#/definitions/TemplateResponse'