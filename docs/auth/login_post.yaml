User Login
---
tags:
  - Authentication
description:
  Validates the submitted information and logs in as a user on success. <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> Yes<br><br>
  <b>MANAGER:</b> Yes<br><br>
  <b>DEVELOPER:</b> Yes<br><br>
parameters:
  - in: formData
    name: email
    type: string
    required: True
  - in: formData
    name: password
    schema:
      type: string
      format: password
    required: True

responses:
  200:
    description:
      When the user login fails for some reason
      <center><b>login.html</b></center>
    schema:
      $ref: '#/definitions/TemplateResponse'

  302:
    description:
      When validation is successful and user is logged in
      <center><b>home.html</b></center>
    schema:
      $ref: '#/definitions/TemplateResponse'
