User Registration
---
tags:
  - Authentication
description:
  Validates the submitted information and registers a new user on success. <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> Yes<br><br>
  <b>MANAGER:</b> Yes (register a new manager or developer)<br><br>
  <b>DEVELOPER:</b> No<br><br>
parameters:
  - in: formData
    name: username
    type: string
    required: True
  - in: formData
    name: email
    type: string
    required: True
  - in: formData
    name: password
    schema:
      type: string
      format: password
    required: True
  - in: formData
    name: confirm_password
    schema:
      type: string
      format: password
    required: True
  - in: formData
    name: roles
    type: string
    required: True
  - in: formData
    name: manager
    type: string

responses:
  200:
    description:
      When the registration fails for some reason
      <center><b>register.html</b></center>
    schema:
      $ref: '#/definitions/TemplateResponse'

  302:
    description:
      When validation is successful and new user is created or the developer tries to access the page
      <center><b>home.html</b></center>
    schema:
      $ref: '#/definitions/TemplateResponse'
