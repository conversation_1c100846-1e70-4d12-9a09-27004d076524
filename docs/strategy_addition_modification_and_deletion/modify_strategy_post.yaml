Modify strategy
---

tags:
  - Strategy Addition, Modification and Deletion
description:
  Validates the submitted information and modifies an existing strategy on success. <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> If developer of strategy<br><br>
  <b>MANAGER:</b> If developer of strategy<br><br>
  <b>DEVELOPER:</b> If developer of strategy<br><br>

parameters:
  - in: path
    name: strat_name
    type: string
    required: True
  - in: formData
    name: segment
    type: string
    required: True
  - in: formData
    name: exchange_name
    type: string
    required: True
  - in: formData
    name: backtest_date
    schema:
      type: string
      format: date
    required: True
  - in: formData
    name: trigger_coeff
    type: int
    required: True
  - in: formData
    name: limit_coeff
    type: int
    required: True
  - in: formData
    name: expiration_time
    type: time
    required: True
  - in: formData
    name: long_short
    type: int
    required: True
  - in: formData
    name: long_book_mapping
    type: string
    required: True
  - in: formData
    name: short_book_mapping
    type: string
    required: True
  - in: formData
    name: cluster_mapping
    type: string
    required: True
  - in: formData
    name: comments
    type: string
    required: True
  - in: formData
    name: python_file
    type: file
    required: True
  - in: formData
    name: ipynb_file
    type: file
    required: True
  - in: formData
    name: is_rework
    type: boolean
    required: True
  - in: formData
    name: old_strategy
    type: string
    required: True
responses:
  200:
    description:
      When there is some error in form validation
      <center><b>submission.html</b></center>
    schema:
      $ref: '#/definitions/TemplateResponse'
  
  302:
    description:
      When form is validated and strategy is modified
      <center><b>home.html</b></center>
    schema:
      $ref: '#/definitions/TemplateResponse'
  404:
    description:
      When strategy name entered does not exist in the database
    schema:
      $ref: '#/definitions/ErrorTemplateResponse'
  403:
    description:
      When anyone other than the developer tries to access the strategy
    schema:
      $ref: '#/definitions/ForbiddenTemplateResponse'