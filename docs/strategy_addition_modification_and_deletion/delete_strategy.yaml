Delete a strategy
---

tags:
  - Strategy Addition, Modification and Deletion
description: 
  Delete a strategy. <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> If developer of strategy<br><br>
  <b>MANAGER:</b> If developer of strategy<br><br>
  <b>DEVELOPER:</b> If developer of strategy<br><br>
parameters:
  - in: path
    name: strategy_name
    type: string
    required: True

responses:
  302:
    description:
      When strategy is deleted <br>
      <center><b>home.html</b></center>
    schema:
      $ref: '#/definitions/TemplateResponse'
  404:
    description:
      When strategy name does not exist in the database
    schema:
      $ref: '#/definitions/ErrorTemplateResponse'
  403:
    description:
      When anyone other than the developer tries to delete the strategy
    schema:
      $ref: '#/definitions/ForbiddenTemplateResponse'