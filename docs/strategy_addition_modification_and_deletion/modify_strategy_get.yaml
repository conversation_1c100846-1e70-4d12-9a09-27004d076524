Modify strategy
---

tags:
  - Strategy Addition, Modification and Deletion
description:
  Fetches strategy modification form. <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> If developer of strategy<br><br>
  <b>MANAGER:</b> If developer of strategy<br><br>
  <b>DEVELOPER:</b> If developer of strategy<br><br>
parameters:
  - in: path
    name: strat_name
    type: string
    required: True
responses:
  200:
    description:
      When strategy modification form is successfully fetched
      <center><b>submission.html</b></center>
    schema:
      $ref: '#/definitions/TemplateResponse'
  404:
    description:
      When strategy name entered does not exist in the database
    schema:
      $ref: '#/definitions/ErrorTemplateResponse'
  403:
    description:
      When anyone other than the developer tries to access the strategy
    schema:
      $ref: '#/definitions/ForbiddenTemplateResponse'