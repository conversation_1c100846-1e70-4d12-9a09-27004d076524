Dashboard Page of Slippage
---

tags:
  - Slippage Dashboard
description:
  Displays different kind of slippage information. <br><br>
  <b>Accessibility:</b> <br><br>
  <b>ADMIN:</b> Yes<br><br>
  <b>MANAGER:</b> Yes<br><br>
  <b>DEVELOPER:</b> Yes <br><br>

parameters:
  - in: path
    name: segment
    type: string
    required: True

responses:
  200:
    description:
      When slippage information is successfully fetched from clickhouse database <br>
      <center><b>slip_home.html</b></center>
    schema:
      $ref: '#/definitions/TemplateResponse'