FROM continuumio/miniconda3:latest

ENV TZ="Asia/Calcutta"

WORKDIR /samba

# Update aptitude with new repo
RUN apt-get update

# Install software 
RUN apt-get install -y \
    git \
    curl \
    libpq-dev \
    build-essential

COPY . .
RUN conda init bash
RUN conda env create -f analytics_dashboard_python311.yml
RUN conda env create -f analytics_dashboard.yml
RUN echo "conda activate dashboard_env" >> ~/.bashrc
SHELL ["/bin/bash", "--login", "-c"]
RUN conda activate dashboard_env

# Clone the conf files into the docker container
RUN git clone https://github.com/soroco/pyce.git
RUN sed -i '/python_requires/d' pyce/setup.py
RUN pip install pyce/.

EXPOSE 9005

ENTRYPOINT ["conda", "run", "-n", "dashboard_env", "python", "run.py"]
CMD [ "DEVELOPMENT" ]