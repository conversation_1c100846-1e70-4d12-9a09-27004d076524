# Official language image. Look for the different tagged releases at:
# https://hub.docker.com/r/library/python/tags/
image: "continuumio/miniconda:latest"

variables:
  MY_PATH: "$HOME"
  PYTHONPATH: "."
  DOCKER_IMAGE_NAME: "*************:15050/devops/commonlibs/samba_server/${CI_COMMIT_REF_NAME}"

before_script:
  - apt-get --allow-releaseinfo-change update && apt-get -y install build-essential libpq-dev
  - conda env create -f analytics_dashboard.yml
  - source activate dashboard_env
  - pip install retrying
  - pip install minio
  - rm -rf pyce
  - git clone https://github.com/soroco/pyce.git
  - sed -i '/python_requires/d' pyce/setup.py
  - pip install pyce/.


stages:
  - Linting
  - Test
  - Coverage
  - docker_build

linting:
  tags:
   - "155"
  stage: Linting
  script:
    - pip install black==22.3.0
    - pip install flake8==3.9.0
    - black --check ./*.py
    - flake8 ./*.py

test:
  tags:
   - "155"
  stage : Test
  script:
   - COVERAGE_FILE=.coverage.unit coverage run --rcfile=.coveragerc -m unittest discover
  
  artifacts:
    paths:
      - .coverage.unit

# Your coverage job, which will combine the coverage data from the two tests jobs and generate a report
coverage:
  tags:
   - "155"
  stage: Coverage
  script:
    - coverage combine --rcfile=.coveragerc
    - coverage report
    - coverage xml -o coverage.xml
  coverage: '/\d+\%\s*$/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml


build_docker_image:
  before_script:
    - |
      # Check for conflicts header in commit message
      if git log -1 --pretty=%B | grep -q "^# Conflicts:"; then
        echo "Conflicts header detected in commit message. Skipping pipeline."
        exit 0
      fi
  tags:
    - "container_registry_build_runner"
  stage: docker_build
  rules:
    # Skip pre-merge pipeline for merge requests
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    # Run job **only** after merge or direct commits on master/production
    - if: '$CI_COMMIT_BRANCH == "master"'
      variables:
        ENV_FILE_CONTENT: "$ENV_CONTENT_DEV"
      when: on_success
    - if: '$CI_COMMIT_BRANCH == "production"'
      variables:
        ENV_FILE_CONTENT: "$ENV_CONTENT"
      when: on_success
  script:
    - |
      # Fetch changed files
      CHANGED_FILES=$(git diff --name-only HEAD^ HEAD || echo "")

      echo "Changed files: $CHANGED_FILES"
      # Skip build if only CI/CD config files changed
      if [[ -z "$(echo "$CHANGED_FILES" | grep -vE '^(jenkinsfile|.gitlab-ci.yml)$')" ]]; then
        echo "Only CI configuration files changed. Skipping docker build."
        exit 0
      fi
    - echo "$ENV_FILE_CONTENT" > .env
    - export DATE_TAG=$(date +%Y%m%d)
    - docker build --label version=v$DATE_TAG -t $DOCKER_IMAGE_NAME:v$DATE_TAG .
    - docker tag $DOCKER_IMAGE_NAME:v$DATE_TAG $DOCKER_IMAGE_NAME:latest
    - docker push $DOCKER_IMAGE_NAME:v$DATE_TAG
    - docker push $DOCKER_IMAGE_NAME:latest
    - docker image rm $DOCKER_IMAGE_NAME:v$DATE_TAG $DOCKER_IMAGE_NAME:latest
