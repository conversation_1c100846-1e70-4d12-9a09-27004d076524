from app import create_app
from config import DevelopmentConfig, ProductionConfig
import sys


ENVIRONMENT = DevelopmentConfig
if __name__ == "__main__":
    if (len(sys.argv) > 1) and (sys.argv[1] in ["live", "production"]):
        ENVIRONMENT = ProductionConfig
        ENVIRONMENT.init_app()
    app = create_app(config=ENVIRONMENT)
    app.logger.info(f"App created. Starting service in {ENVIRONMENT.NAME} mode")
    app.run(host="0.0.0.0", port=app.config["PORT"])
