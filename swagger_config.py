template = {
    "swagger": "2.0",
    "info": {
        "title": "SAMBA",
        "description": "<b>Strategy Addition Management and Business Analytics</b><br><br>Dashboard for adding, reviewing and managing strategies",
        "version": "1.0",
    },
    # "basePath": "/",  # base bash for blueprint registration
    # "host": "*************:5000",
    "schemes": ["http"],
}

swagger_config = {
    "headers": [],
    "specs": [
        {
            "endpoint": "apispec",
            "route": "/apispec.json",
            "rule_filter": lambda rule: True,
            "model_filter": lambda tag: True,
        }
    ],
    "static_url_path": "/flassger_static",
    "swagger_ui": True,
    "specs_route": "/apidocs/",
}
