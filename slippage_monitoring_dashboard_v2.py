import sys
import datetime
import pandas as pd
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np

sys.path.append("/home/<USER>/repos/balte")
sys.path.append("/home/<USER>/repos/analytics-api")
# sys.path.append("/home/<USER>/sentinel_clean")
# sys.path.insert(0, "/home/<USER>/repos/balte/kivifolio")
# sys.path.append('/home/<USER>/custom_code')
# sys.path.append('/home/<USER>')
# sys.path.insert(0, '/home/<USER>/repos/alphalens-kivi')

import balte.balte_config as bc
from analytics_api import AnalyticsAPI

api = AnalyticsAPI()

def enrich_dte(df):        
    df['entry_date'] = pd.to_datetime(df.timestamp).dt.date
    df['balte_expiry_date'] = pd.to_datetime(df['expiry_date'], dayfirst=True, format='mixed', errors='coerce').dt.normalize()
    near_week=df.groupby(['entry_date','symbol'])['balte_expiry_date'].min().reset_index()

    all_dates = bc.ALL_DATES

    all_dates_dict = {date: index for index, date in enumerate(all_dates)}
 
    df['dte'] = df.apply(lambda x: all_dates_dict[pd.Timestamp(x['balte_expiry_date'])] - all_dates_dict[pd.Timestamp(x['entry_date'])], axis=1)

    return df
 
    
def load_data(segment, exchange, start_date, end_date, slave_name='', strategy_name=''):    

    tl = pd.DataFrame()
    if (segment == "OPTIDX") and (exchange == "IND"):
        tl1 = api.get_slippage_data("OPTIDX", start_date, end_date)
        tl2 = api.get_slippage_data("OPTIDX_BSE", start_date, end_date)
        tl = pd.concat([tl1, tl2], ignore_index=True)
        
    elif (segment == "OPTIDX") and (exchange == "NSE"):
        tl = api.get_slippage_data("OPTIDX", start_date, end_date)
    
    elif (segment == "OPTIDX_BSE") and (exchange == "BSE"):
        tl = api.get_slippage_data("OPTIDX_BSE", start_date, end_date)
        
    elif (segment == "OPTIDX_US") and (exchange == "US"):
        tl = api.get_slippage_data("OPTIDX_US", start_date, end_date)
        
    else:
        st.write("Incorrect exchange selected for the segment")
        return tl
      
    if 'US' not in segment:
        tl = enrich_dte(tl)
    else:
        tl['dte'] = 0
    
    if 'US' in segment:
        tl['quantity'] = tl['quantity']*100
    tl = tl.drop(columns=['id'])
    if tl.empty:
        return pd.DataFrame()

    tl['total_slippage'] = (tl['executed_price'] - tl['signal_price']) * tl['quantity'] * tl['buysell']
    tl['execution_signal_slippage'] = (tl['execution_signal_price'] - tl['signal_price']) * tl['quantity'] * tl['buysell']
    tl['execution_slippage'] = (tl['executed_price'] - tl['execution_signal_price']) * tl['quantity'] * tl['buysell']    
    tl['turnover'] = (tl['signal_price'] * tl['quantity']).abs()
    tl['timestamp_floor'] = tl['timestamp'].dt.floor('min')
    tl['signal_second'] = (tl['timestamp'] - tl['timestamp_floor']).dt.total_seconds().astype(int)
    tl['executed_second'] = (tl['executed_timestamp'] - tl['timestamp_floor']).dt.total_seconds().astype(int)
    tl['tot.slip-turnover_ratio'] = tl['total_slippage']/tl['turnover']
    tl['exec.slip-turnover_ratio'] = tl['execution_slippage']/tl['turnover']
    tl['sig.slip-turnover_ratio'] = tl['execution_signal_price']/tl['turnover']
    tl['order_type'] = np.where(tl['buysell'] == 1, 'Buy', 'Sell')
    allowed_slaves = api.get_slaves('cluster_izmir')+ api.get_slaves('cluster_izmir_directional')+ api.get_slaves('cluster_kari')+ ['cluster_izmir']+['cluster_izmir_directional'] +['cluster_kari']
    tl = tl[(tl.slave_name.isin(allowed_slaves)) | (tl.strategy_name.isin(allowed_slaves))]
    slave_list = [s.strip() for s in slave_name.split(",") if s.strip()] if slave_name else []
    slave_list = [s.strip() for s in slave_name.split(",") if s.strip()] if slave_name else []

    if not strategy_name and not slave_list:
        return tl
    if not strategy_name:
        return tl[tl['slave_name'].isin(slave_list)]
    if not slave_list:
        return tl[tl['strategy_name'] == strategy_name] 
    return tl[(tl['strategy_name'] == strategy_name) & (tl['slave_name'].isin(slave_list))]


import plotly.colors as pc

def plot_slippage_interactive(tl):
   
    tl_agg = tl.groupby(["timestamp", "slave_name"])["total_slippage"].sum().reset_index()
    
    tl_agg['timestamp'] = pd.to_datetime(tl_agg['timestamp'])
    tl_agg = tl_agg.sort_values(by=['timestamp', 'slave_name'])
    
    tl_agg['slave_name'] = tl_agg['slave_name'].astype(str)
    
    colors = pc.qualitative.Alphabet
    all_slaves = sorted(tl_agg['slave_name'].unique())
    color_map = {name: colors[i % len(colors)] for i, name in enumerate(all_slaves)}
    
    fig = px.line(
        tl_agg, 
        x='timestamp', 
        y='total_slippage', 
        color='slave_name',
        title="Total slippages trend for the entire range",
        line_shape="linear",
        labels={"slave_name": "Slave Name"},
        color_discrete_map=color_map
    )
    
    fig.update_layout(
        xaxis_title="Timestamp",
        yaxis_title="Total Slippage",
        legend_title="Slave Name",
        xaxis=dict(showgrid=True, rangeslider=dict(visible=True)),
        yaxis=dict(showgrid=True),
        hovermode="x unified",
        template="plotly_white"
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    tl['timestamp'] = pd.to_datetime(tl['timestamp'])
    
    tl["date"] = tl["timestamp"].dt.date
    tl_day = tl.groupby(["date", "slave_name"])["total_slippage"].sum().reset_index()
    
    tl_day = tl_day.sort_values(by=['date', 'slave_name'])
    
    tl_day['slave_name'] = tl_day['slave_name'].astype(str)
    
    colors = pc.qualitative.Alphabet
    all_slaves = sorted(tl_day['slave_name'].unique())
    color_map = {name: colors[i % len(colors)] for i, name in enumerate(all_slaves)}
    
    fig = px.line(
        tl_day, 
        x='date', 
        y='total_slippage', 
        color='slave_name',
        title="Total slippages on the date",
        line_shape="linear",
        labels={"slave_name": "Slave Name"},
        color_discrete_map=color_map
    )
    
    fig.update_layout(
        xaxis_title="Date",
        yaxis_title="Total Slippage",
        legend_title="Slave Name",
        xaxis=dict(showgrid=True, rangeslider=dict(visible=True)),
        yaxis=dict(showgrid=True),
        hovermode="x unified",
        template="plotly_white"
    )
    
    st.plotly_chart(fig, use_container_width=True)   
    
    tl_agg["time_group"] = tl_agg["timestamp"].dt.strftime("%H:%M")
    tl_agg = tl_agg.groupby(["time_group", "slave_name"])["total_slippage"].mean().reset_index()
    
    tl_agg['slave_name'] = tl_agg['slave_name'].astype(str)
    
    colors = pc.qualitative.Alphabet
    all_slaves = sorted(tl_agg['slave_name'].unique())
    color_map = {name: colors[i % len(colors)] for i, name in enumerate(all_slaves)}
    
    fig = px.line(
        tl_agg, 
        x='time_group', 
        y='total_slippage', 
        color='slave_name',
        title="Per minute distribution of mean slippage",
        line_shape="linear",
        labels={"slave_name": "Slave Name"},
        color_discrete_map=color_map
    )
    
    fig.update_layout(
        xaxis_title="Time",
        yaxis_title="Mean Total Slippage",
        legend_title="Slave Name",
        xaxis=dict(showgrid=True, rangeslider=dict(visible=True)),
        yaxis=dict(showgrid=True),
        hovermode="x unified",
        template="plotly_white"
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
def display_stats(df, group_cols, value_col, label):
    st.write(label)
    
    desc_stats = df.groupby(group_cols)[[value_col]].describe()
    desc_stats.columns = desc_stats.columns.droplevel(0)
    
    percentiles = df.groupby(group_cols)[[value_col]].quantile([0.95, 0.99]).unstack()
    percentiles.columns = ['95%', '99%']
    
    stats_summary = desc_stats.merge(percentiles, left_index=True, right_index=True, how='left')
    
    cols = ['count', 'mean', 'std', 'min', '25%', '50%', '75%', '95%', '99%', 'max']
    stats_summary = stats_summary[[col for col in cols if col in stats_summary.columns]]
    
    st.dataframe(stats_summary)


def plot_histograms(df, column1, column2, column3):
    """Plots histograms for execution timing distributions with mean, median, and std deviation."""
    
    fig, axes = plt.subplots(2, 2, figsize=(18, 12), gridspec_kw={'height_ratios': [1, 0.8]})  
    axes = axes.flatten()  

    for ax, column in zip(axes[:2], [column1, column2]):  # Top two plots
        mean_val = df[column].mean()
        median_val = df[column].median()
        std_dev = df[column].std()

        sns.histplot(df[column], bins=50, kde=True, ax=ax, color="blue", alpha=0.6)

        ax.axvline(mean_val, color='red', linestyle='dashed', linewidth=2, label=f'Mean: {mean_val:.2f}')
        ax.axvline(median_val, color='green', linestyle='dashed', linewidth=2, label=f'Median: {median_val:.2f}')
        ax.axvline(mean_val + std_dev, color='purple', linestyle='dotted', linewidth=2, label=f'Std Dev: {std_dev:.2f}')
        ax.axvline(mean_val - std_dev, color='purple', linestyle='dotted', linewidth=2)

        ax.set_title(f"Distribution of {column}", fontsize=14)
        ax.set_xlabel(column, fontsize=12)
        ax.set_ylabel("Frequency", fontsize=12)
        ax.grid(True, linestyle='--', alpha=0.6)
        ax.legend(loc="upper right", fontsize=10)

    ax = axes[2]
    mean_val = df[column3].mean()
    median_val = df[column3].median()
    std_dev = df[column3].std()

    sns.histplot(df[column3], bins=50, kde=True, ax=ax, color="blue", alpha=0.6)

    ax.axvline(mean_val, color='red', linestyle='dashed', linewidth=2, label=f'Mean: {mean_val:.2f}')
    ax.axvline(median_val, color='green', linestyle='dashed', linewidth=2, label=f'Median: {median_val:.2f}')
    ax.axvline(mean_val + std_dev, color='purple', linestyle='dotted', linewidth=2, label=f'Std Dev: {std_dev:.2f}')
    ax.axvline(mean_val - std_dev, color='purple', linestyle='dotted', linewidth=2)

    ax.set_title(f"Distribution of {column3}", fontsize=14)
    ax.set_xlabel(column3, fontsize=12)
    ax.set_ylabel("Frequency", fontsize=12)
    ax.grid(True, linestyle='--', alpha=0.6)
    ax.legend(loc="upper right", fontsize=10)

    fig.delaxes(axes[3])

    plt.tight_layout()
    st.pyplot(fig)

def generate_plots(segment, exchange, start_date, end_date, slave_name='', strategy_name=''):
    tl = load_data(segment, exchange, start_date, end_date, slave_name, strategy_name)
    if tl.empty:
        st.write("No data found for the selected criteria.")
        return
    
    st.subheader("Strategy-Level Slippage Summary")
    plot_slippage_interactive(tl)
    
    slippage_summary = tl.groupby('strategy_name')[['total_slippage', 'execution_signal_slippage', 'execution_slippage', 'turnover']].sum().reset_index().round(0)
    slippage_summary['tot.slip-to-turnover'] = slippage_summary['total_slippage']/slippage_summary['turnover']
    slippage_summary['sig.slip-to-turnover'] = slippage_summary['execution_signal_slippage']/slippage_summary['turnover']
    slippage_summary['exec.slip-to-turnover'] = slippage_summary['execution_slippage']/slippage_summary['turnover']
    st.dataframe(slippage_summary)
    strategy_dte = tl.groupby(['strategy_name','dte', 'order_type'])[['total_slippage', 'execution_signal_slippage', 'execution_slippage', 'turnover']].sum().reset_index().round(0)
    strategy_dte['tot.slip-to-turnover'] = strategy_dte['total_slippage']/strategy_dte['turnover']
    strategy_dte['sig.slip-to-turnover'] = strategy_dte['execution_signal_slippage']/strategy_dte['turnover']
    strategy_dte['exec.slip-to-turnover'] = strategy_dte['execution_slippage']/strategy_dte['turnover']
    st.dataframe(strategy_dte)  
    st.write('Non-0DTE trades')
    strategy_dte = tl[tl.dte>0].groupby(['strategy_name','order_type'])[['total_slippage', 'execution_signal_slippage', 'execution_slippage', 'turnover']].sum().reset_index().round(0)
    strategy_dte['tot.slip-bps-turnover'] = strategy_dte['total_slippage']/strategy_dte['turnover']*10000
    strategy_dte['sig.slip-bps-turnover'] = strategy_dte['execution_signal_slippage']/strategy_dte['turnover']*10000
    strategy_dte['exec.slip-bps-turnover'] = strategy_dte['execution_slippage']/strategy_dte['turnover']*10000
    st.dataframe(strategy_dte)
    
    fig = go.Figure()
    metrics = {
        'total_slippage': ('Total Slippage', 'blue'),
        'execution_signal_slippage': ('Execution Signal Slippage', 'green'),
        'execution_slippage': ('Execution Slippage', 'red')
    }
    for key, (label, color) in metrics.items():
        fig.add_trace(go.Bar(
            x=slippage_summary['strategy_name'], y=slippage_summary[key], name=label, marker_color=color
        ))
    fig.update_layout(barmode='group', title='Slippage Comparison by Strategy', xaxis_title='Strategy Name', yaxis_title='Slippage Value')
    st.plotly_chart(fig)
    
    st.subheader("Slave-Level Slippage Summary")
    slave_summary = tl.groupby('slave_name')[['total_slippage', 'execution_signal_slippage', 'execution_slippage', 'turnover']].sum().reset_index().round(0)
    slave_summary['tot.slip-to-turnover'] = slave_summary['total_slippage']/slave_summary['turnover']
    slave_summary['sig.slip-to-turnover'] = slave_summary['execution_signal_slippage']/slave_summary['turnover']
    slave_summary['exec.slip-to-turnover'] = slave_summary['execution_slippage']/slave_summary['turnover']
    st.dataframe(slave_summary)
    slave_dte = tl.groupby(['slave_name','dte', 'order_type'])[['total_slippage', 'execution_signal_slippage', 'execution_slippage', 'turnover']].sum().reset_index().round(0)
    slave_dte['tot.slip-to-turnover'] = slave_dte['total_slippage']/slave_dte['turnover']
    slave_dte['sig.slip-to-turnover'] = slave_dte['execution_signal_slippage']/slave_dte['turnover']
    slave_dte['exec.slip-to-turnover'] = slave_dte['execution_slippage']/slave_dte['turnover']
    st.dataframe(slave_dte)
    st.write('Non-0DTE trades')      
    slave_dte = tl[tl.dte>0].groupby(['slave_name','order_type'])[['total_slippage', 'execution_signal_slippage', 'execution_slippage', 'turnover']].sum().reset_index().round(0)
    slave_dte['tot.slip-bps-turnover'] = slave_dte['total_slippage']/slave_dte['turnover']*10000
    slave_dte['sig.slip-bps-turnover'] = slave_dte['execution_signal_slippage']/slave_dte['turnover']*10000
    slave_dte['exec.slip-bps-turnover'] = slave_dte['execution_slippage']/slave_dte['turnover']*10000
    st.dataframe(slave_dte)  
    
    fig = go.Figure()
    metrics = {
        'total_slippage': ('Total Slippage', 'blue'),
        'execution_signal_slippage': ('Execution Signal Slippage', 'green'),
        'execution_slippage': ('Execution Slippage', 'red')
    }
    for key, (label, color) in metrics.items():
        fig.add_trace(go.Bar(
            x=slave_summary['slave_name'], y=slave_summary[key], name=label, marker_color=color
        ))
    fig.update_layout(barmode='group', title='Slippage Comparison by Slave', xaxis_title='Slave Name', yaxis_title='Slippage Value')
    st.plotly_chart(fig)
    
    st.subheader("Strategy Timing Distribution")
    tl['e-s_diff_second'] = tl['executed_second'] - tl['signal_second']
    plot_histograms(tl, 'signal_second', 'executed_second', 'e-s_diff_second')
    
    st.subheader("Slave-Level Timing Stats")
    display_stats(tl, 'slave_name', 'signal_second', 'Signal Stats')
    display_stats(tl, 'slave_name', 'executed_second', 'Executed Stats')
    display_stats(tl, 'slave_name', 'e-s_diff_second', 'Signal to execution Stats')
    
    st.subheader("Symbol Timing Stats")
    display_stats(tl, ['symbol'], 'signal_second', 'Signal Stats')
    display_stats(tl, ['symbol'], 'executed_second', 'Executed Stats')
    display_stats(tl, ['symbol'], 'e-s_diff_second', 'Signal to execution Stats')

    st.subheader("Symbol-Level Timing Stats for Strategy")
    display_stats(tl, ['symbol', 'strategy_name'], 'signal_second', 'Signal Stats')
    display_stats(tl, ['symbol', 'strategy_name'], 'executed_second', 'Executed Stats')
    display_stats(tl, ['symbol', 'strategy_name'], 'e-s_diff_second', 'Signal to execution Stats')
    
    st.subheader("Symbol-Level Hedge vs Non-Hedge stats")
    hedges = [name for name in tl.slave_name.unique() if 'hedge' in name.lower()]
    
    tl['trade_type'] = np.where(tl.slave_name.isin(hedges), 'Hedge', 'Normal')
    tl['entry_exit'] = np.where(tl['trade_id'].astype(str).str[4].astype(int) == 1, 'Entry', 'Exit')
    
    if segment == 'OPTIDX_US':
        
        tl['trade_type'] = np.where(
            (tl.buysell > 0) & (tl.entry_exit == 'Entry') & (~tl.slave_name.str.lower().str.contains('god_of_war')),
            'Hedge',
            'Normal'
        )
    
#     st.write('Hedges')
    hedges_summary = tl.groupby(['symbol', 'trade_type'])[['total_slippage', 'execution_signal_slippage', 'execution_slippage', 'turnover']].sum().reset_index().round(0)
    hedges_summary['tot.slip-to-turnover'] = hedges_summary['total_slippage']/hedges_summary['turnover']
    hedges_summary['sig.slip-to-turnover'] = hedges_summary['execution_signal_slippage']/hedges_summary['turnover']
    hedges_summary['exec.slip-to-turnover'] = hedges_summary['execution_slippage']/hedges_summary['turnover']
    st.dataframe(hedges_summary)
    
    display_stats(tl, ['symbol', 'trade_type'], 'signal_second', 'Signal Stats')
    display_stats(tl, ['symbol', 'trade_type'], 'executed_second', 'Executed Stats')
    display_stats(tl, ['symbol', 'trade_type'], 'e-s_diff_second', 'Signal to Execution Stats')
    
    ################# MASTER TABLE
    st.write(" ")
    st.write(" ")
    
    st.subheader("---------Master Table -------------")
    
    st.write(" ")
    st.write(" ")
    
    st.write("Slippages Stats")
    master_table = tl.groupby(['slave_name', 'symbol', 'dte', 'trade_type', 'order_type', 'entry_exit'])[['total_slippage', 'execution_signal_slippage', 'execution_slippage', 'turnover']].sum().reset_index().round(0)
    master_table['tot.slip-to-turnover'] = master_table['total_slippage']/master_table['turnover']
    master_table['sig.slip-to-turnover'] = master_table['execution_signal_slippage']/master_table['turnover']
    master_table['exec.slip-to-turnover'] = master_table['execution_slippage']/master_table['turnover']
    st.dataframe(master_table)
    
    display_stats(tl, ['slave_name', 'symbol', 'dte', 'trade_type', 'order_type', 'entry_exit'], 'signal_second', 'Signal Stats')
    display_stats(tl, ['slave_name', 'symbol', 'dte', 'trade_type', 'order_type', 'entry_exit'], 'executed_second', 'Executed Stats')
    display_stats(tl, ['slave_name', 'symbol', 'dte', 'trade_type', 'order_type', 'entry_exit'], 'e-s_diff_second', 'Signal to Execution Stats')

import datetime
import pandas as pd
import streamlit as st
import plotly.express as px

import datetime

USER_CREDENTIALS = {"aryash": "smd_aryash", "shivansh": "smd_shivansh","jain":"smd_jain"}

def authenticate_user(username, password):
    return USER_CREDENTIALS.get(username) == password

import streamlit as st

def authenticate_user(username, password):
    return username in USER_CREDENTIALS and USER_CREDENTIALS[username] == password

def login_page():
    st.title("Welcome to Slippage Monitoring Dashboard")
    
    with st.form(key="login_form", clear_on_submit=False):
        username = st.text_input("Username")
        password = st.text_input("Password", type="password")
        
        submitted = st.form_submit_button("Login")

    if submitted:
        if authenticate_user(username, password):
            st.session_state["authenticated"] = True
            st.session_state["user"] = username
            st.rerun()
        else:
            st.error("Invalid credentials. Try again.")



def dashboard():
    st.set_page_config(layout="wide")
    st.title('Slippage Monitoring Dashboard')
    st.write('**Note:** Negative slippage is in favor of us, while positive slippage is against us.')

    today = datetime.date.today()

    with st.sidebar.form(key="input_form"):
        start_date = st.date_input("Start Date", today)
        end_date = st.date_input("End Date", today)
        segment = st.selectbox("Select Segment", ['OPTIDX','OPTIDX_BSE', 'OPTIDX_US', 'OPTSTK', 'FUTSTK'])
        exchange_name = st.selectbox("Select Exchange", ['IND', 'NSE', 'BSE', 'US'])
        strategy_name = st.text_input("Cluster/Strategy Name (optional):", key="strategy_input")

        if 'strategy_slaves' not in st.session_state:
            st.session_state.strategy_slaves = ""

        load_slaves_button = st.form_submit_button("Load Slaves for Strategy")

        if load_slaves_button:
            if strategy_name:
                data = load_data(segment, exchange_name, start_date, end_date)
                strategy_slaves = sorted(data[data['strategy_name'] == strategy_name]['slave_name'].unique())

                if strategy_slaves:
                    st.session_state.strategy_slaves = ", ".join(strategy_slaves)
                else:
                    st.session_state.strategy_slaves = "No slaves found for the entered strategy."
            else:
                st.session_state.strategy_slaves = "Enter a strategy name first."

        st.session_state.strategy_slaves = st.text_area(
            "Slaves for Strategy (Copy the relevant slaves in Slave Name(s)):", 
            value=st.session_state.strategy_slaves, height=100
        )

        slave_name = st.text_input("Slave Name(s) (comma-separated):", key="slave_input")
        run_button = st.form_submit_button("Run")  # Pressing Enter submits the entire form

    if run_button:
        generate_plots(segment, exchange_name, start_date, end_date, slave_name, strategy_name)

    if st.sidebar.button("Logout"):
        st.session_state["authenticated"] = False
        st.session_state["user"] = None
        st.rerun()

def main():

    if "authenticated" not in st.session_state:
        st.session_state["authenticated"] = False

    if not st.session_state["authenticated"]:
        login_page()
    else:
        dashboard()

if __name__ == "__main__":
    main()