#!/usr/bin/env python3
"""
Test script for Advanced Slippage Dashboard API endpoints
"""

import requests
import json
import sys

BASE_URL = "http://127.0.0.1:5000"
DASHBOARD_URL = f"{BASE_URL}/slip_dashboard"

# Test credentials from base_test_fixture.py
TEST_CREDENTIALS = {
    'email': '<EMAIL>',
    'password': 'test@123'
}

def login_session():
    """Create a session and login"""
    session = requests.Session()

    # Login
    login_response = session.post(f"{BASE_URL}/login", data=TEST_CREDENTIALS)
    if login_response.status_code == 302:  # Redirect after successful login
        print("✓ Login successful")
        return session
    else:
        print(f"✗ Login failed: {login_response.status_code}")
        return None

def test_filter_options(session):
    """Test the filter options API endpoint"""
    print("Testing filter options endpoint...")
    try:
        response = session.get(f"{DASHBOARD_URL}/api/filter_options")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Filter options loaded successfully")
            print(f"  - Segments: {len(data.get('options', {}).get('segments', []))}")
            print(f"  - Strategies: {len(data.get('options', {}).get('strategies', []))}")
            print(f"  - Slaves: {len(data.get('options', {}).get('slaves', []))}")
            return True
        else:
            print(f"✗ Filter options failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Filter options error: {e}")
        return False

def test_slippage_data(session):
    """Test the slippage data API endpoint"""
    print("\nTesting slippage data endpoint...")
    try:
        payload = {
            "start_date": "2024-01-01",
            "end_date": "2024-01-31",
            "segments": ["OPTIDX"],
            "exchanges": ["IND"],
            "strategies": [],
            "slaves": []
        }

        response = session.post(
            f"{DASHBOARD_URL}/api/slippage_data",
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✓ Slippage data loaded successfully")
                summary = data.get('data', {}).get('summary', {})
                print(f"  - Total trades: {summary.get('total_trades', 0)}")
                print(f"  - Total turnover: {summary.get('total_turnover', 0)}")
                print(f"  - Total slippage: {summary.get('total_slippage', 0)}")
                return True
            else:
                print(f"✗ Slippage data failed: {data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"✗ Slippage data failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Slippage data error: {e}")
        return False

def test_chart_data(session):
    """Test the chart data API endpoints"""
    print("\nTesting chart data endpoints...")
    chart_types = ['slippage_trend', 'strategy_comparison', 'timing_histogram']

    payload = {
        "start_date": "2024-01-01",
        "end_date": "2024-01-31",
        "segments": ["OPTIDX"]
    }

    success_count = 0
    for chart_type in chart_types:
        try:
            response = session.post(
                f"{DASHBOARD_URL}/api/chart_data/{chart_type}",
                json=payload,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"  ✓ {chart_type} chart data loaded")
                    success_count += 1
                else:
                    print(f"  ✗ {chart_type} chart failed: {data.get('error', 'Unknown error')}")
            else:
                print(f"  ✗ {chart_type} chart failed: {response.status_code}")
        except Exception as e:
            print(f"  ✗ {chart_type} chart error: {e}")
    
    return success_count == len(chart_types)

def test_trade_comparison(session):
    """Test the trade comparison API endpoint"""
    print("\nTesting trade comparison endpoint...")
    try:
        payload = {
            "start_date": "2024-01-01",
            "end_date": "2024-01-31",
            "slave_strategy": "test_strategy",
            "segment": "OPTIDX",
            "exchange": "IND"
        }

        response = session.post(
            f"{DASHBOARD_URL}/api/trade_comparison",
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✓ Trade comparison loaded successfully")
                comparison_data = data.get('data', {})
                print(f"  - Strategy vs Cluster data: {'✓' if 'strategy_vs_cluster' in comparison_data else '✗'}")
                print(f"  - Strategy vs Backtest data: {'✓' if 'strategy_vs_backtest' in comparison_data else '✗'}")
                print(f"  - Charts available: {'✓' if 'charts' in comparison_data else '✗'}")
                return True
            else:
                print(f"✗ Trade comparison failed: {data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"✗ Trade comparison failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Trade comparison error: {e}")
        return False

def test_dashboard_page(session):
    """Test the main dashboard page"""
    print("\nTesting dashboard page...")
    try:
        response = session.get(f"{DASHBOARD_URL}/advanced_slippage")
        if response.status_code == 200:
            content = response.text
            # Check for key elements
            checks = [
                ('Advanced Slippage Analysis' in content, 'Page title'),
                ('multi-select-container' in content, 'Multi-select dropdowns'),
                ('chart-container' in content, 'Chart containers'),
                ('comparison-results' in content, 'Comparison results section'),
                ('advanced_slippage.js' in content, 'JavaScript file'),
                ('advanced_slippage.css' in content, 'CSS file')
            ]
            
            all_passed = True
            for check, description in checks:
                if check:
                    print(f"  ✓ {description}")
                else:
                    print(f"  ✗ {description}")
                    all_passed = False
            
            return all_passed
        else:
            print(f"✗ Dashboard page failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Dashboard page error: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("ADVANCED SLIPPAGE DASHBOARD API TESTS")
    print("=" * 60)

    # Login first
    session = login_session()
    if not session:
        print("✗ Failed to login. Cannot proceed with tests.")
        return 1

    tests = [
        (test_dashboard_page, "Dashboard Page"),
        (test_filter_options, "Filter Options API"),
        (test_slippage_data, "Slippage Data API"),
        (test_chart_data, "Chart Data API"),
        (test_trade_comparison, "Trade Comparison API")
    ]

    passed = 0
    total = len(tests)

    for test_func, test_name in tests:
        try:
            if test_func(session):
                passed += 1
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            passed += 0
    
    print("\n" + "=" * 60)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All tests passed! Dashboard is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
