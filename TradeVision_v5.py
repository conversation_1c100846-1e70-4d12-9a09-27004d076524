import sys
from datetime import datetime, date
import pandas as pd
import streamlit as st
import plotly.graph_objects as go
import json
import numpy as np

sys.path.append("/home/<USER>/repos/balte")
sys.path.append("/home/<USER>/sentinel_clean")
sys.path.insert(0, "/home/<USER>/repos/balte/kivifolio")
sys.path.append('/home/<USER>/custom_code')
sys.path.insert(0, '/home/<USER>/repos/alphalens-kivi')

import balte.balte_config as bc
from analytics_api import AnalyticsAPI
api = AnalyticsAPI()

pd.options.display.float_format = "{:,.2f}".format

def fmt(x):
    try:
        x = float(x)
        if x == float('inf'):
            return "∞ "
        elif x == float('-inf'):
            return "-"
        whole, dot, frac = f"{x:.1f}".partition(".")
        whole_with_commas = "{:,}".format(int(whole))
        return f"{whole_with_commas}.{frac}"
    except:
        return "0.0"

def authenticate(username, password):
    return (username == "aryash" and password == "") | (username == "shivansh" and password == "")

def login_page():
    st.set_page_config(layout="centered")
    st.markdown(
        """
        <style>
        .main, body {
            background-color: #ADD8E6 !important;
        }
        /* Style the title */
        .stTitle {
            color: #ADD8E6;
            font-weight: bold;
        }
        /* Style the form container */
        div[data-testid="stForm"] {
            background: #ADD8E6;
            box-shadow: 2px 2px 8px rgba(0,0,0,0.2);
        }
        </style>
        """,
        unsafe_allow_html=True
    )

    st.title("TradeVision")
    st.markdown("###### *Dead trades comparison tool*")
    with st.form("login_form"):
        username = st.text_input("Username")
        password = st.text_input("Password", type="password")
        submitted = st.form_submit_button("Login")
        if submitted:
            if authenticate(username, password):
                st.session_state["authenticated"] = True
                st.session_state["user"] = username
                st.rerun()
            else:
                st.error("Invalid username or password")


def logout_button():
    if st.sidebar.button("Logout"):
        st.session_state["authenticated"] = False
        st.session_state["user"] = None
        st.rerun()

                    #################### COMPARISON ##########################

def show_exit_on_1min_flags(start_date, end_date, segment, slave_strat):
    start_dt = datetime.combine(start_date, datetime.min.time())
    end_dt = datetime.combine(end_date, datetime.max.time())

    df = api.get_balte_dead_sheet(start_dt, end_dt)
    temp = df[(df["SEGMENT"] == segment) & (df["SLAVE_NAME"] == slave_strat)].copy()

    def extract_exit_flag(x):
        if isinstance(x, str):
            try:
                parsed = json.loads(x)
                if isinstance(parsed, dict):
                    stop_info = parsed.get("order_stoploss_info", {})
                    if isinstance(stop_info, dict):
                        return stop_info.get("exit_on_1min", False)
            except Exception:
                return False
        return False

    temp["exit_on_1min_flag"] = temp["EXTENDED_INFO"].apply(extract_exit_flag)

    st.dataframe(temp[temp["exit_on_1min_flag"] == True])
    
def enrich_dte_for_clustertest(df):
    
    df['entry_date'] = pd.to_datetime(df['exit_timestamp']).dt.normalize()
    df['balte_expiry_date'] = pd.to_datetime(df['balte_expiry_date'], dayfirst=True, format='mixed', errors='coerce').dt.normalize()

    all_dates = bc.ALL_DATES
    all_dates_dict = {pd.Timestamp(date).normalize(): index for index, date in enumerate(all_dates)}

    df['dte'] = df.apply(
        lambda x: all_dates_dict.get(x['balte_expiry_date'], -1) - all_dates_dict.get(x['entry_date'], -1),
        axis=1
    )
    return df
        
def enrich_dte(df):
    
    df['ENTRY_DATE'] = pd.to_datetime(df['EXIT_TIMESTAMP']).dt.normalize()
    df['EXPIRY'] = pd.to_datetime(df['EXPIRY'], dayfirst=True, format='mixed', errors='coerce').dt.normalize()

    all_dates = bc.ALL_DATES
    all_dates_dict = {pd.Timestamp(date).normalize(): index for index, date in enumerate(all_dates)}

    df['DTE'] = df.apply(
        lambda x: all_dates_dict.get(x['EXPIRY'], -1) - all_dates_dict.get(x['ENTRY_DATE'], -1),
        axis=1
    )

    return df
        
def load_slaves(segment, exchange, start_date, end_date, strategy_name=''):    
    
#     start_date_ts = pd.Timestamp(start_date)
#     end_date_ts = pd.Timestamp(end_date)
    
    tl = pd.read_parquet('balte_dead_sheet.parquet')
    
    try:
        if (segment == "OPTIDX") and (exchange == "IND"):
#             tl = api.get_balte_Cluster(start_date_ts, end_date_ts)
            tl1 = tl[tl['SEGMENT']=="OPTIDX"]
            tl2 = tl[tl['SEGMENT']=="OPTIDX_BSE"]
            tl = pd.concat([tl1, tl2], ignore_index=True)

        elif (segment == "OPTIDX") and (exchange == "NSE"):
#             tl = api.get_Cluster(start_date_ts, end_date_ts)
            tl = tl[tl['SEGMENT']=="OPTIDX"]

        elif (segment == "OPTIDX_BSE") and (exchange == "BSE"):
#             tl = api.get_Cluster(start_date_ts, end_date_ts)
            tl = tl[tl['SEGMENT']=="OPTIDX_BSE"]

        elif (segment == "OPTIDX_US") and (exchange == "US"):
#             tl = api.get_Cluster(start_date_ts, end_date_ts)
            tl = tl[tl['SEGMENT']=="OPTIDX_US"]
        
        else:
            st.write("Incorrect exchange selected for the segment")
            return pd.DataFrame()
        
        if tl.empty:
            st.write("No data found")
            return pd.DataFrame()
        
        if strategy_name:
            tl = tl[tl['STRATEGY']==strategy_name]
        
        return tl
    except:
        st.write("Error, try checking on other dates")
        pass
        
def load_data(start_date, end_date, slave_strat, segment='OPTIDX', exchange='IND'):
    
#     start_dt = datetime.combine(start_date, datetime.min.time())
#     end_dt = datetime.combine(end_date, datetime.max.time())
    
    #################### LOADING BALTE OMS DATA #########################
    
#     tl = api.get_balte_dead_sheet(start_date, end_date)

    tl = pd.read_parquet('balte_dead_sheet.parquet')
    
    try:
        bt = api.get_tradelog(slave_strat)
    except:
        st.write('Strategy not found!')
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame()
    
    bt = bt[bt.exit_timestamp.dt.date>=start_date]
    bt = bt[bt.exit_timestamp.dt.date<=end_date]
    bt = bt[~(
            (bt.entry_timestamp.dt.date == bt.exit_timestamp.dt.date) & 
            (bt.exit_timestamp.dt.time == pd.to_datetime('15:25').time()) & 
            (bt.age > 1)
         )] 
    
    if (segment == "OPTIDX") and (exchange == "IND"):
#             tl = api.get_balte_Cluster(start_date, end_date)
        tl1 = tl[tl['SEGMENT']=="OPTIDX"]
        tl2 = tl[tl['SEGMENT']=="OPTIDX_BSE"]
        tl = pd.concat([tl1, tl2], ignore_index=True)

        bt1 = bt[bt['balte_segment']=="OPTIDX"]
        bt2 = bt[bt['balte_segment']=="OPTIDX_BSE"]
        bt = pd.concat([bt1, bt2], ignore_index=True)

    elif (segment == "OPTIDX") and (exchange == "NSE"):
#             tl = api.get_Cluster(start_date, end_date)
        tl = tl[tl['SEGMENT']=="OPTIDX"]
        bt = bt[bt['balte_segment']=="OPTIDX"]

    elif (segment == "OPTIDX_BSE") and (exchange == "BSE"):
#             tl = api.get_Cluster(start_date, end_date)
        tl = tl[tl['SEGMENT']=="OPTIDX_BSE"]
        bt = bt[bt['balte_segment']=="OPTIDX_BSE"]

    elif (segment == "OPTIDX_US") and (exchange == "US"):
#             tl = api.get_Cluster(start_date, end_date)
        tl = tl[tl['SEGMENT']=="OPTIDX_US"]
        bt = bt[bt['balte_segment']=="OPTIDX_US"]

    else:
        st.write("Incorrect exchange selected for the segment")
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

    if tl.empty:
        st.write("No data found in BaLTE dead sheet")
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

#     st.write("Error, try checking on other dates")
#     return pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

    if 'US' not in segment:
        tl = enrich_dte(tl)
        bt = enrich_dte_for_clustertest(bt)
    else:
        tl['DTE'] = 0
        bt['dte'] = 0

    tl['ORDER_TYPE'] = tl['TYPE']
    bt['order_type'] = bt['balte_option_type']
    bt['type'] = bt['balte_option_type']
    
    tl['ACTUAL_SLAVE'] = np.where(
        tl['STRATEGY'].str.startswith('BaLTE_'),
        tl['STRATEGY'].str.replace('^BaLTE_', '', regex=True),
        tl['SLAVE_NAME']
    )
    
    tl = tl[tl.ACTUAL_SLAVE == slave_strat]
    
    stratSent = tl[tl.STRATEGY.str.startswith('BaLTE_')]
    clusterSent = tl[~tl.STRATEGY.str.startswith('BaLTE_')]
    backtest = bt.copy()
    
    if len(stratSent) == 0:
        st.write('Strategy trades not found, perhaps check access or change dates!')
        return pd.DataFrame(),pd.DataFrame(), pd.DataFrame()
    elif len(clusterSent) == 0:
        st.write('Cluster trades not found, perhaps check access or change dates!')
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

    stratSent['ENTRY_TIMESTAMP'] = pd.to_datetime(stratSent['ENTRY_TIMESTAMP']).dt.floor('T')
    stratSent['EXIT_TIMESTAMP'] = pd.to_datetime(stratSent['EXIT_TIMESTAMP']).dt.floor('T')
    stratSent['EXIT_DATE'] = pd.to_datetime(stratSent['EXIT_TIMESTAMP']).dt.date

    clusterSent['ENTRY_TIMESTAMP'] = pd.to_datetime(clusterSent['ENTRY_TIMESTAMP']).dt.floor('T')
    clusterSent['EXIT_TIMESTAMP'] = pd.to_datetime(clusterSent['EXIT_TIMESTAMP']).dt.floor('T')
    clusterSent['EXIT_DATE'] = pd.to_datetime(clusterSent['EXIT_TIMESTAMP']).dt.date
    
    backtest['entry_timestamp'] = pd.to_datetime(backtest['entry_timestamp']).dt.floor('T')
    backtest['exit_timestamp'] = pd.to_datetime(backtest['exit_timestamp']).dt.floor('T')
    backtest['exit_date'] = pd.to_datetime(backtest['exit_timestamp']).dt.date

    stratSent['HOLDING_TIME'] = (stratSent['EXIT_TIMESTAMP'] - stratSent['ENTRY_TIMESTAMP']).dt.total_seconds() / 60
    clusterSent['HOLDING_TIME'] = (clusterSent['EXIT_TIMESTAMP'] - clusterSent['ENTRY_TIMESTAMP']).dt.total_seconds() / 60
    backtest['holding_time'] = (backtest['exit_timestamp'] - backtest['entry_timestamp']).dt.total_seconds() / 60
    
#     stratSent = stratSent[(stratSent['EXIT_DATE'] >= start_date) & (stratSent['EXIT_DATE'] <= end_date)]
#     clusterSent = clusterSent[(clusterSent['EXIT_DATE'] >= start_date) & (clusterSent['EXIT_DATE'] <= end_date)]
    
    stratSent['PNL'] = (stratSent['EXIT_PRICE'] - stratSent['ENTRY_PRICE']) * stratSent['QUANTITY']
    clusterSent['PNL'] = (clusterSent['EXIT_PRICE'] - clusterSent['ENTRY_PRICE']) * clusterSent['QUANTITY']

    return stratSent, clusterSent, backtest

@st.cache_data
def search(start_date, end_date, slave_strat , segment = 'OPTIDX', exchange='IND', analyse_till_last_run_sentinel = False, short_holding_threshold = 5):
    
    stratSent, clusterSent, backtest = load_data(start_date, end_date, slave_strat, segment, exchange)

    if len(stratSent) == 0:
        st.write('No trades sent by strategy')
        return
    if len(clusterSent) == 0:
        st.write('No trades sent by cluster')
        return
    if len(backtest) == 0:
        st.write('No trades in backtest tradelog')
        if (analyse_till_last_run_sentinel == True):
            return
        
    balte_strategy_name = stratSent.STRATEGY.unique()
    cluster_name = clusterSent.STRATEGY.unique()
    
    st.success(f"""
    **Note:**
    - **Strategy** refers to `{balte_strategy_name}`  
    - **Cluster** refers to `{cluster_name}`
    """)
    
    st.write("")
    st.write("")
    
    min_date_backtest = backtest['exit_date'].min()
    max_date_backtest = backtest['exit_date'].max()
    
    min_date_strat = stratSent['EXIT_DATE'].min()
    min_date_cluster = clusterSent['EXIT_DATE'].min()   
    min_date = max(min_date_strat, min_date_cluster)
    
    max_date_strat = stratSent['EXIT_DATE'].max()
    max_date_cluster = clusterSent['EXIT_DATE'].max()
    max_date = min(max_date_strat, max_date_cluster)
    
    if analyse_till_last_run_sentinel == True:
        stratSent = stratSent[stratSent['EXIT_DATE'] >= min_date_backtest]
        stratSent = stratSent[stratSent['EXIT_DATE'] <= max_date_backtest]
        clusterSent = clusterSent[clusterSent['EXIT_DATE'] >= min_date_backtest]
        clusterSent = clusterSent[clusterSent['EXIT_DATE'] <= max_date_backtest]
        min_date = max(min_date, min_date_backtest)
        max_date = min(max_date, max_date_backtest)
    
    st.markdown('###### Data availability range')
    st.write(f"- Strategy: `{min_date_strat}` → `{max_date_strat}`")
    st.write(f"- Cluster: `{min_date_cluster}` → `{max_date_cluster}`")
    st.write(f"- Backtest: `{min_date_backtest}` → `{max_date_backtest}`")

    
    stratSent = stratSent[stratSent['EXIT_DATE'] >= min_date]
    stratSent = stratSent[stratSent['EXIT_DATE'] <= max_date]
    clusterSent = clusterSent[clusterSent['EXIT_DATE'] >= min_date]
    clusterSent = clusterSent[clusterSent['EXIT_DATE'] <= max_date]
    backtest = backtest[backtest['exit_date'] >= min_date]
    backtest = backtest[backtest['exit_date'] <= max_date]
    
    st.write("")
    st.write("")
    
    st.markdown(f"##### **Analysis from `{min_date}` to `{max_date}`:**")   
    
        # ---------- SUMMARY: Strategy vs Cluster -------
    st.markdown("#### **Summary: Strategy vs Cluster**")

    with st.expander("click to compact/elaborate", expanded=True):
        
        st.markdown("##### **Overall**")
        st.markdown("###### **(Strategy - Cluster) performance**")
       
        count_diff = (stratSent.shape[0] - clusterSent.shape[0])
        pct_rejected = (count_diff / stratSent.shape[0]) if stratSent.shape[0] != 0 else float('inf')
        pnl_diff = (stratSent['PNL'].sum() - clusterSent['PNL'].sum())
        pct_pnl_diff = (pnl_diff / stratSent['PNL'].sum()) if stratSent['PNL'].sum() != 0 else float('inf')
        short_duration_diff = (
            stratSent[stratSent['HOLDING_TIME'] < short_holding_threshold].shape[0] -
            clusterSent[clusterSent['HOLDING_TIME'] < short_holding_threshold].shape[0]
        )

        # Display in 3 columns
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Exit Count Difference", f"{count_diff} ({fmt(pct_rejected)}%)")
        with col2:
            st.metric("PnL Difference", f"{fmt(pnl_diff)} ({fmt(pct_pnl_diff)}%)")
        with col3:
            st.metric(f"Short Duration Strategy Trades(<{short_holding_threshold}min)", stratSent[stratSent['HOLDING_TIME'] < short_holding_threshold].shape[0])
        with col4:
            st.metric(f"Short Duration Cluster Trades(<{short_holding_threshold}min)", clusterSent[clusterSent['HOLDING_TIME'] < short_holding_threshold].shape[0])

#         min_date = stratSent['EXIT_DATE'].min()
#         clusterSent_filtered = clusterSent[clusterSent['exit_date'] >= min_date]

        # --- Exit Count Stats ---


        act_exit = stratSent.groupby('EXIT_DATE').size()
        back_exit = clusterSent.groupby('EXIT_DATE').size()
        count_diff = (act_exit - back_exit).fillna(0)
        max_diff_date = count_diff.abs().idxmax()

        st.markdown("##### Exit Count Per Day Stats")
        st.markdown(f"- **Max Diff Date**: `{max_diff_date}`")
        st.markdown(f"- **(Strategy)**: {fmt(act_exit.get(max_diff_date, 0))}, **(Cluster)**: {fmt(back_exit.get(max_diff_date, 0))}, **(Diff)**: {fmt(count_diff[max_diff_date])}")
        st.markdown("###### Overall")
        st.markdown(f"- **Mean (Strategy)**: {fmt(act_exit.mean())}, **Mean (Cluster)**: {fmt(back_exit.mean())}, **Mean (Diff)**: {fmt(count_diff.mean())}")
        
        st.write("")
        st.write("")

        # --- PnL Stats ---
        act_pnl = stratSent.groupby('EXIT_DATE')['PNL'].sum()
        back_pnl = clusterSent.groupby('EXIT_DATE')['PNL'].sum()
        pnl_diff = (act_pnl - back_pnl).fillna(0)
        max_pnl_diff_date = pnl_diff.abs().idxmax()

        st.markdown("##### Cumulative PnL Per Day Stats")
        st.markdown(f"- **Max Diff Date**: `{max_pnl_diff_date}`")
        st.markdown(f"- **(Strategy)**: {fmt(act_pnl.get(max_pnl_diff_date, 0))}, **(Cluster)**: {fmt(back_pnl.get(max_pnl_diff_date, 0))}, **(Diff)**: {fmt(pnl_diff[max_pnl_diff_date])}")

        st.markdown("###### Overall")
        st.markdown(f"- **Mean (Strategy)**: {fmt(act_pnl.mean())}, **Mean (Cluster)**: {fmt(back_pnl.mean())}, **Mean (Diff)**: {fmt(pnl_diff.mean())}")

        
        st.write("")
        st.write("")

#         # --- Holding Time Stats ---
#         act_hold = stratSent.groupby('exit_date')['holding_time'].mean()//60
#         back_hold = clusterSent_filtered.groupby('exit_date')['holding_time'].mean()//60
#         hold_diff = (act_hold - back_hold).fillna(0)
#         max_hold_diff_date = hold_diff.abs().idxmax()
#         st.markdown("#### Avg Holding Time (in hours)")
#         st.markdown(f"- **Max Diff Date**: `{max_hold_diff_date}`")
#         st.markdown(f"- **Strategy**: {act_hold.get(max_hold_diff_date, 0)//60:.2f}, **Cluster**: {back_hold.get(max_hold_diff_date, 0)//60:.2f}, **Diff**: {hold_diff[max_hold_diff_date]//60:.2f}")
#         st.markdown("###### Overall")
#         st.markdown(f"- **Mean Strategy**: {act_hold.mean()//60:.2f}, **Mean Cluster**: {back_hold.mean()//60:.2f}, **Mean Diff**: {hold_diff.mean()//60:.2f}")

#         --- Short Duration Trade Count Stats ---
#         short_Strategy_count = stratSent[stratSent['HOLDING_TIME'] < 5].groupby('EXIT_DATE').size()
#         short_Cluster_count = clusterSent_filtered[clusterSent_filtered['holding_time'] < 5].groupby('exit_date').size()
#         short_diff = (short_Strategy_count - short_Cluster_count).fillna(0)
#         max_short_diff_date = short_diff.abs().idxmax()
#         st.markdown("##### Short Duration Trades (<5min) Per Day Stats")
#         st.markdown(f"- **Max Diff Date**: `{max_short_diff_date}`")
#         st.markdown(f"- **(Strategy)**: {short_Strategy_count.get(max_short_diff_date, 0)}, **(Cluster)**: {short_Cluster_count.get(max_short_diff_date, 0)}, **Diff**: {short_diff[max_short_diff_date]}")
#         st.markdown(f"- **Mean (Strategy)**: {short_Strategy_count.mean():.2f}, **Mean (Cluster)**: {short_Cluster_count.mean():.2f}, **Mean (Diff)**: {short_diff.mean():.2f}")

     # ---------- SUMMARY: Strategy vs Backtest -------

    st.markdown("#### **Summary: Backtest vs Strategy**")

    with st.expander("click to compact/elaborate", expanded=True):

        st.markdown("##### **Overall**")
        st.markdown("###### **(Backtest - Strategy) performance**")

        count_diff = (backtest.shape[0] - stratSent.shape[0])
        pct_rejected = (count_diff / backtest.shape[0]) if backtest.shape[0] != 0 else float('inf')
        pnl_diff = (backtest['pnl'].sum() - stratSent['PNL'].sum())
        pct_pnl_diff = (pnl_diff / backtest['pnl'].sum()) if backtest['pnl'].sum() != 0 else float('inf')
        short_duration_diff = (
            stratSent[stratSent['HOLDING_TIME'] < short_holding_threshold].shape[0] -
            backtest[backtest['holding_time'] < short_holding_threshold].shape[0]
        )

        # Display in 3 columns
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Exit Count Difference", f"{count_diff} ({fmt(pct_rejected)}%)")
        with col2:
            st.metric("PnL Difference", f"{fmt(pnl_diff)} ({fmt(pct_pnl_diff)}%)")
        with col3:
            st.metric(f"Short Duration Strategy Trades(<{short_holding_threshold}min)", stratSent[stratSent['HOLDING_TIME'] < short_holding_threshold].shape[0])
        with col4:
            st.metric(f"Short Duration Backtest Trades(<{short_holding_threshold}min)", backtest[backtest['holding_time'] < short_holding_threshold].shape[0])

#         min_date = stratSent['EXIT_DATE'].min()
#         backtest_filtered = backtest[backtest['exit_date'] >= min_date]

        # --- Exit Count Stats ---
        act_exit = backtest.groupby('exit_date').size()
        back_exit = stratSent.groupby('EXIT_DATE').size()
        count_diff = (act_exit - back_exit).fillna(0)
        max_diff_date = count_diff.abs().idxmax()

        st.markdown("##### Exit Count Per Day Stats")
        st.markdown(f"- **Max Diff Date**: `{max_diff_date}`")
        st.markdown(f"- **(Backtest)**: {fmt(act_exit.get(max_diff_date, 0))}, **(Strategy)**: {fmt(back_exit.get(max_diff_date, 0))}, **(Diff)**: {fmt(count_diff[max_diff_date])}")

        st.markdown("###### Overall")
        st.markdown(f"- **Mean (Backtest)**: {fmt(act_exit.mean())}, **Mean (Strategy)**: {fmt(back_exit.mean())}, **Mean (Diff)**: {fmt(count_diff.mean())}")


        st.write("")
        st.write("")

        # --- PnL Stats ---
        back_pnl = stratSent.groupby('EXIT_DATE')['PNL'].sum()
        act_pnl = backtest.groupby('exit_date')['pnl'].sum()
        pnl_diff = (act_pnl - back_pnl).fillna(0)
        max_pnl_diff_date = pnl_diff.abs().idxmax()

        st.markdown("##### Cumulative PnL Per Day Stats")
        st.markdown(f"- **Max Diff Date**: `{max_pnl_diff_date}`")
        st.markdown(f"- **(Backtest)**: {fmt(act_pnl.get(max_pnl_diff_date, 0))}, **(Strategy)**: {fmt(back_pnl.get(max_pnl_diff_date, 0))}, **(Diff)**: {fmt(pnl_diff[max_pnl_diff_date])}")

        st.markdown("###### Overall")
        st.markdown(f"- **Mean (Backtest)**: {fmt(act_pnl.mean())}, **Mean (Strategy)**: {fmt(back_pnl.mean())}, **Mean (Diff)**: {fmt(pnl_diff.mean())}")


        st.write("")
        st.write("")

#         # --- Holding Time Stats ---
#         act_hold = stratSent.groupby('exit_date')['holding_time'].mean()//60
#         back_hold = backtest_filtered.groupby('exit_date')['holding_time'].mean()//60
#         hold_diff = (act_hold - back_hold).fillna(0)
#         max_hold_diff_date = hold_diff.abs().idxmax()
#         st.markdown("#### Avg Holding Time (in hours)")
#         st.markdown(f"- **Max Diff Date**: `{max_hold_diff_date}`")
#         st.markdown(f"- **Strategy**: {act_hold.get(max_hold_diff_date, 0)//60:.2f}, **Cluster**: {back_hold.get(max_hold_diff_date, 0)//60:.2f}, **Diff**: {hold_diff[max_hold_diff_date]//60:.2f}")
#         st.markdown("###### Overall")
#         st.markdown(f"- **Mean Strategy**: {act_hold.mean()//60:.2f}, **Mean Cluster**: {back_hold.mean()//60:.2f}, **Mean Diff**: {hold_diff.mean()//60:.2f}")

#         --- Short Duration Trade Count Stats ---
#         short_Strategy_count = stratSent[stratSent['HOLDING_TIME'] < 5].groupby('EXIT_DATE').size()
#         short_Cluster_count = backtest_filtered[backtest_filtered['holding_time'] < 5].groupby('exit_date').size()
#         short_diff = (short_Strategy_count - short_Cluster_count).fillna(0)
#         max_short_diff_date = short_diff.abs().idxmax()
#         st.markdown("##### Short Duration Trades (<5min) Per Day Stats")
#         st.markdown(f"- **Max Diff Date**: `{max_short_diff_date}`")
#         st.markdown(f"- **(Strategy)**: {short_Strategy_count.get(max_short_diff_date, 0)}, **(Cluster)**: {short_Cluster_count.get(max_short_diff_date, 0)}, **Diff**: {short_diff[max_short_diff_date]}")
#         st.markdown(f"- **Mean (Strategy)**: {short_Strategy_count.mean():.2f}, **Mean (Cluster)**: {short_Cluster_count.mean():.2f}, **Mean (Diff)**: {short_diff.mean():.2f}")
        
        # --- Exit Count Comparison ---
    st.markdown("##### **Trade Exit Count**")
    with st.expander("click to compact/elaborate", expanded=True):

        # Group by exit_date
        stratSent_exit_count = stratSent.groupby('EXIT_DATE').size()
        clusterSent_exit_count = clusterSent.groupby('EXIT_DATE').size()
        backtest_exit_count = backtest.groupby('exit_date').size()

        # Combine and compute diff
        exit_counts = pd.DataFrame({
            'stratSent_exit_count': stratSent_exit_count,
            'clusterSent_exit_count': clusterSent_exit_count,
            'backtest_exit_count': backtest_exit_count
        }).fillna(0)

        exit_counts['count_diff_sc'] = exit_counts['stratSent_exit_count'] - exit_counts['clusterSent_exit_count']
        exit_counts['count_diff_sb'] = exit_counts['stratSent_exit_count'] - exit_counts['backtest_exit_count']

        # Plotting
        fig1 = go.Figure()
        
        fig1.add_trace(go.Scatter(
            x=exit_counts.index, 
            y=exit_counts['stratSent_exit_count'], 
            name='Strategy', 
            mode='lines+markers',
            line=dict(color='blue')
        ))

        fig1.add_trace(go.Scatter(
            x=exit_counts.index, 
            y=exit_counts['clusterSent_exit_count'], 
            name='Cluster', 
            mode='lines+markers',
            line=dict(color='deeppink')
        ))
        
        fig1.add_trace(go.Scatter(
            x=exit_counts.index, 
            y=exit_counts['backtest_exit_count'], 
            name='backtest', 
            mode='lines+markers',
            line=dict(color='green')
        ))
        fig1.update_layout(title="Daily Exit Count", xaxis_title="Date", yaxis_title="Count")
        st.plotly_chart(fig1, use_container_width=True)

        fig2 = go.Figure()
        fig2.add_trace(go.Bar(
            x=exit_counts.index, 
            y=exit_counts['count_diff_sc'], 
            name='Strategy - Cluster', 
            marker_color='red'
        ))
        fig2.update_layout(title="Exit Count Difference (Strategy - Cluster)", xaxis_title="Date", yaxis_title="Count Difference")
        st.plotly_chart(fig2, use_container_width=True)
        
        fig3 = go.Figure()
        fig3.add_trace(go.Bar(
            x=exit_counts.index, 
            y=exit_counts['count_diff_sb'], 
            name='Strategy - Backtest', 
            marker_color='brown'
        ))
        fig3.update_layout(title="Exit Count Difference (Strategy - Backtest)", xaxis_title="Date", yaxis_title="Count Difference")
        st.plotly_chart(fig3, use_container_width=True)

    # --- PnL Comparison ---
    st.markdown("##### **Cumulative PnL**")
    with st.expander("click to compact/elaborate", expanded=True):
        
        stratSent_pnl = stratSent.groupby('EXIT_DATE')['PNL'].sum().cumsum()
        clusterSent_pnl = clusterSent.groupby('EXIT_DATE')['PNL'].sum().cumsum()
        backtest_pnl = backtest.groupby('exit_date')['pnl'].sum().cumsum()

        pnl_df = pd.DataFrame({
            'Strategy': stratSent_pnl,
            'Cluster': clusterSent_pnl,
            'Backtest': backtest_pnl
        }).fillna(method='ffill').fillna(0)

        pnl_diff = pnl_df['Strategy'] - pnl_df['Cluster']

        fig3 = go.Figure()
        
        fig3.add_trace(go.Scatter(
            x=pnl_df.index,
            y=pnl_df['Strategy'],
            name='Strategy',
            mode='lines+markers',
            line=dict(color='blue')
        ))

        fig3.add_trace(go.Scatter(
            x=pnl_df.index,
            y=pnl_df['Cluster'],
            name='Cluster',
            mode='lines+markers',
            line=dict(color='deeppink')
        ))
        
        fig3.add_trace(go.Scatter(
            x=pnl_df.index,
            y=pnl_df['Backtest'],
            name='Backtest',
            mode='lines+markers',
            line=dict(color='green')
        ))

        fig3.update_layout(title="Cumulative PnL", xaxis_title="Date", yaxis_title="pnl")
        st.plotly_chart(fig3, use_container_width=True)

        # Compute cumulative PnL
        stratSent_pnl = stratSent.groupby('EXIT_DATE')['PNL'].sum().cumsum()
        clusterSent_pnl = clusterSent.groupby('EXIT_DATE')['PNL'].sum().cumsum()
        backtest_pnl = backtest.groupby('exit_date')['pnl'].sum().cumsum()

        # Align on index for fair comparison (optional forward fill if gaps matter)
        pnl_diff_sc = stratSent_pnl - clusterSent_pnl.reindex(stratSent_pnl.index, fill_value=0)
        pnl_diff_sb = stratSent_pnl - backtest_pnl.reindex(stratSent_pnl.index, fill_value=0)

        # Plot PnL Difference
        fig4 = go.Figure()
        fig4.add_trace(go.Scatter(
            x=pnl_diff_sc.index, 
            y=pnl_diff_sc, 
            name='Strategy - Cluster', 
            mode='lines+markers', 
            line=dict(color='red')
        ))
        fig4.update_layout(title="Cumulative PnL Difference (Strategy - Cluster)", xaxis_title="Date", yaxis_title="PnL Diff")
        st.plotly_chart(fig4, use_container_width=True)
        
         # Plot PnL Difference
        fig5 = go.Figure()
        fig5.add_trace(go.Scatter(
            x=pnl_diff_sb.index, 
            y=pnl_diff_sb, 
            name='Strategy - Backtest', 
            mode='lines+markers', 
            line=dict(color='brown')
        ))
        fig5.update_layout(title="Cumulative PnL Difference (Strategy - Backtest)", xaxis_title="Date", yaxis_title="PnL Diff")
        st.plotly_chart(fig5, use_container_width=True)

    # --- Holding Time ---
    st.markdown("##### **Holding Time Analysis**")
    with st.expander("click to compact/elaborate", expanded=True):
        from scipy.stats import gaussian_kde
        import numpy as np

        strat_minutes = stratSent['HOLDING_TIME']
        cluster_minutes = clusterSent['HOLDING_TIME']
        backtest_minutes = backtest['holding_time']

#         Fit KDEs
        try:
            kde_strat = gaussian_kde(strat_minutes)
        except:
            kde_strat = lambda x: np.zeros_like(x)
        try:
            kde_cluster = gaussian_kde(cluster_minutes)
        except:
            kde_cluster = lambda x: np.zeros_like(x)
        try:
            kde_backtest = gaussian_kde(backtest_minutes)
        except:
            kde_backtest = lambda x: np.zeros_like(x)
    
        kde_x = np.linspace(0, max(max(strat_minutes.max(), cluster_minutes.max()), backtest_minutes.max()), 500)

        fig = go.Figure()

        fig.add_trace(go.Histogram(
            x=cluster_minutes, nbinsx=60, name='Cluster',
            opacity=0.5, marker_color='#FF1493', histnorm='probability density'
        ))
        fig.add_trace(go.Histogram(
            x=backtest_minutes, nbinsx=60, name='Backtest',
            opacity=0.4, marker_color='brown', histnorm='probability density'
        ))
        fig.add_trace(go.Histogram(
            x=strat_minutes, nbinsx=60, name='Strategy',
            opacity=0.6, marker_color='#1f77b4', histnorm='probability density'
        ))

        fig.update_layout(
            barmode='overlay',  # makes bars overlap with transparency
            title='Histogram Comparison',
            xaxis_title='Minutes',
            yaxis_title='Density',
            template='plotly_white'
        )


        # KDE lines
        
        fig.add_trace(go.Scatter(
            x=kde_x, y=kde_cluster(kde_x), mode='lines',
            name='Cluster (KDE)', line=dict(color='#FF1493', width=1.5)
        ))
        fig.add_trace(go.Scatter(
            x=kde_x, y=kde_backtest(kde_x), mode='lines',
            name='Backtest (KDE)', line=dict(color='brown', width=1.5)
        ))
        fig.add_trace(go.Scatter(
            x=kde_x, y=kde_strat(kde_x), mode='lines',
            name='Strategy (KDE)', line=dict(color='#1f77b4', width=1.5)
        ))

        # Mean/Median values
        stats = {
            "Cluster Mean": (cluster_minutes.mean(), '#FF1493', 0.65),
            "Cluster Median": (cluster_minutes.median(), '#FF1493', 0.6),
            "Backtest Mean": (backtest_minutes.mean(), 'brown', 0.40),
            "Backtest Median": (backtest_minutes.median(), 'brown', 0.35),
            "Strategy Mean": (strat_minutes.mean(), '#1f77b4', 0.9),
            "Strategy Median": (strat_minutes.median(), '#1f77b4', 0.85),
        }

        from matplotlib.colors import to_rgb  # Required once

        for label, (x_val, color, y_frac) in stats.items():
            rgba_line = f'rgba{(*to_rgb(color), 0.5)}'  # 50% transparent line
            rgba_text = f'rgba{(*to_rgb("black"), 1)}'  # 70% transparent text

            fig.add_vline(x=x_val, line=dict(color=rgba_line, dash='dash'))
            fig.add_annotation(
                x=x_val,
                y=y_frac * max(max(kde_strat(kde_x).max(), kde_cluster(kde_x).max()), kde_backtest(kde_x).max()),
                text=label,
                showarrow=False,
                arrowhead=0,
                ax=0, ay=-30,
                font=dict(color=rgba_text)
            )

        fig.update_layout(
            title="Holding Time Distribution (Minutes)",
            xaxis_title="Holding Time (min)",
            yaxis_title="Density",
            barmode='group'
        )

        st.plotly_chart(fig, use_container_width=True)

        # --- Holding Time Metrics in 3x2 Grid ---
        col1, col2 = st.columns(2)
        with col1:
            st.metric("Avg Holding Time (Strategy)", f"{strat_minutes.mean():.2f} min")
        with col2:
            st.metric("Median Holding Time (Strategy)", f"{strat_minutes.median():.2f} min")

        col3, col4 = st.columns(2)
        with col3:
            st.metric("Avg Holding Time (Cluster)", f"{cluster_minutes.mean():.2f} min")
        with col4:
            st.metric("Median Holding Time (Cluster)", f"{cluster_minutes.median():.2f} min")
            
        col5, col6 = st.columns(2)
        with col5:
            st.metric("Avg Holding Time (Backtest)", f"{backtest_minutes.mean():.2f} min")
        with col6:
            st.metric("Median Holding Time (Backtest)", f"{backtest_minutes.median():.2f} min")

        # Holding time differences
#         merged = pd.merge(
#             stratSent[['EXIT_DATE', 'HOLDING_TIME']],
#             clusterSent_filtered[['exit_date', 'holding_time']],
#             on='EXIT_DATE',
#             suffixes=('_Strategy', '_Cluster')
#         )

#         significant_diff = (merged['holding_time_Strategy'] - merged['holding_time_Cluster']).abs() > 60
#         st.write(f"**Total trades with >60min holding time difference:** {significant_diff.sum()}")

    # --- Short Duration Trades (< x mins) ---
    st.markdown(f"##### **Short Duration Trades (<{short_holding_threshold}mins)**")
    with st.expander("click to compact/elaborate", expanded=True):
        short_Strategy = stratSent[stratSent['HOLDING_TIME'] < short_holding_threshold]
        grouped_Strategy = (
            short_Strategy
            .groupby(['SYMBOL', 'TYPE', 'TRADEID'])[['ENTRY_TIMESTAMP', 'EXIT_TIMESTAMP', 'PNL']]
            .apply(lambda df: df.reset_index(drop=True))
            .reset_index(level=[0,1,2])
        )
        st.markdown("###### Strategy")
        st.dataframe(grouped_Strategy)
        
        short_Cluster = clusterSent[clusterSent['HOLDING_TIME'] < short_holding_threshold]
        grouped_Cluster = (
            short_Cluster
            .groupby(['SYMBOL', 'TYPE', 'TRADEID'])[['ENTRY_TIMESTAMP', 'EXIT_TIMESTAMP', 'PNL']]
            .apply(lambda df: df.reset_index(drop=True))
            .reset_index(level=[0,1,2])
        )
        ("###### Cluster")
        st.dataframe(grouped_Cluster)
        
        short_Cluster = backtest[backtest['holding_time'] < short_holding_threshold]
        grouped_Cluster = (
            short_Cluster
            .groupby(['symbol', 'type', 'TradeID'])[['entry_timestamp', 'exit_timestamp', 'pnl']]
            .apply(lambda df: df.reset_index(drop=True))
            .reset_index(level=[0,1,2])
        )
        ("###### Backtest")
        st.dataframe(grouped_Cluster)

    # --- clusterSent-only Timestamps ---
#     st.markdown("##### **Entry times where Strategy sent trades but Cluster did not**")
#     with st.expander("click to compact/elaborate", expanded=True):
#         clusterSent['ENTRY_DATE'] = clusterSent['ENTRY_TIMESTAMP'].dt.date
#         clusterSent['ENTRY_TIME'] = clusterSent['ENTRY_TIMESTAMP'].dt.strftime('%H:%M')
#         stratSent['ENTRY_DATE'] = stratSent['ENTRY_TIMESTAMP'].dt.date
#         stratSent['ENTRY_TIME'] = stratSent['ENTRY_TIMESTAMP'].dt.strftime('%H:%M')

#         clusterSent_grouped = clusterSent.groupby(['ENTRY_DATE', 'ENTRY_TIME'])['TRADEID'].agg(['count'])
#         stratSent_grouped = stratSent.groupby(['ENTRY_DATE', 'ENTRY_TIME'])['TRADEID'].agg(['count'])

#         stratSent_only = stratSent_grouped[~stratSent_grouped.index.isin(clusterSent_grouped.index)]
#         if not stratSent_only.empty:
#             stratSent_only = stratSent_only.reset_index()
#             stratSent_only.columns = ['ENTRY_DATE', 'ENTRY_TIME', 'COUNT', 'TRADEID']
#             st.dataframe(stratSent_only)
#         else:
#             st.info("No timestamps where Strategy sent trades but Cluster did not.")

    # --- stratSent-only Timestamps ---
#     st.markdown("##### **Entry times where Backtest sent trades but Strategy did not**")
#     with st.expander("click to compact/elaborate", expanded=True):
        
#         backtest['entry_date'] = backtest['entry_timestamp'].dt.date
#         backtest['entry_time'] = backtest['entry_timestamp'].dt.strftime('%H:%M')
        
#         backtest_grouped = backtest.groupby(['entry_date', 'entry_time'])['TradeID'].agg(['count', list])
#         stratSent_grouped = stratSent.groupby(['ENTRY_DATE', 'ENTRY_TIME'])['TRADEID'].agg(['count', list])

#         backtest_only = backtest_grouped[~backtest_grouped.index.isin(stratSent_grouped.index)]
#         if not backtest_only.empty:
#             backtest_only = backtest_only.reset_index()
#             backtest_only.columns = ['entry_date', 'entry_time', 'count','TradeID']
#             st.dataframe(backtest_only)
#         else:
#             st.info("No timestamps where Backtest sent trades but Strategy did not.")
            
    st.markdown("##### **One Minute Exit Trades**")
    with st.expander("click to compact/elaborate", expanded=True):
        
        temp = clusterSent
        
        def extract_exit_flag(x):
            if isinstance(x, str):
                try:
                    parsed = json.loads(x)
                    if isinstance(parsed, dict):
                        stop_info = parsed.get("order_stoploss_info", {})
                        if isinstance(stop_info, dict):
                            return stop_info.get("exit_on_1min", False)
                except Exception:
                    return False
            return False

        temp["exit_on_1min_flag"] = temp["EXTENDED_INFO"].apply(extract_exit_flag)
        st.dataframe(temp[temp["exit_on_1min_flag"] == True])
        
        ############################# >>>>>>>>>>>>>>>>>>>>>> <<<<<<<<<<<<<<<<<<< #############################

######## IDEAS
# short duration trades ---- alert
# one sec/min stoploss
# alerts! and focus points
# permintute timestamp trade differences sorted
# no cluster association alert
# trades exited at same time as entry
# user specified thresholds
# any special miscellaeous thing

        
        

# --- Dashboard Logic ---
def dashboard():
    st.set_page_config(layout="wide")
    st.markdown("""
    <style>

    /* Set sidebar background to reddish pink and text to black */
    section[data-testid="stSidebar"] {
        background-color: #ADD8E6 !important;  /* Reddish pink */
        color: black !important;
    }

    </style>
""", unsafe_allow_html=True)

    
    st.title("TradeVision")
    st.markdown("###### *Dead trades comparison tool*")
    
    today = date.today()

    with st.sidebar.form(key="input_form"):
        start_date = st.date_input("Start Date", today)
        end_date = st.date_input("End Date", today)
        segment = st.selectbox("Select Segment", ['OPTIDX','OPTIDX_BSE', 'OPTIDX_US', 'OPTSTK', 'FUTSTK'])
        exchange_name = st.selectbox("Select Exchange", ['IND', 'NSE', 'BSE', 'US'])
        strategy_name = st.text_input("Find slaves for cluster (optional):", key="cluster")

        if 'strategy_slaves' not in st.session_state:
            st.session_state.strategy_slaves = ""

        load_slaves_button = st.form_submit_button("Load Slaves")

        if load_slaves_button:
            if strategy_name:
                data = load_slaves(segment, exchange_name, start_date, end_date, strategy_name)
                strategy_slaves = []
                if (data is not None) and (len(data)>0):
                    strategy_slaves = sorted(data[data['STRATEGY'] == strategy_name]['SLAVE_NAME'].unique())
#                     print(strategy_slaves)

                if strategy_slaves:
                    st.session_state.strategy_slaves = ", ".join(strategy_slaves)
                    st.write(strategy_slaves)
                else:
                    st.session_state.strategy_slaves = "No slaves found for the entered strategy."
                    st.write('No slaves found for the entered strategy')
            else:
                data = load_slaves(segment, exchange_name, start_date, end_date)
                if (data is not None) & (len(data)>0):
                    strategy_slaves = sorted(data['SLAVE_NAME'].unique())
                    print(strategy_slaves)
                    st.write(strategy_slaves)
                    
                if strategy_slaves:
                    st.session_state.strategy_slaves = ", ".join(strategy_slaves)
                else:
                    st.session_state.strategy_slaves = "No slaves found for the entered strategy."

        slave_strat = st.text_input("Slave Name:", key="slave_input")  
        short_holding_threshold = st.number_input("Short duration trades threshold (< x-min):", min_value=0, step=1, value=5, key="slave_number_input")
#         comparison_type = st.selectbox(
#             "Select Comparison Type",
#             [
#                 "Compare live trades from backtest",
#                 "Compare strategy and cluster trades"
#             ]
#         )
        analyse_till_last_run_sentinel = False
        
        checkbox = st.checkbox("Analyse till last run sentinel")

        if checkbox:
            analyse_till_last_run_sentinel = True
        
        run_button_balte = st.form_submit_button("Compare")

    if run_button_balte:
#         if comparison_type == "Compare live trades from backtest":
#         st.success("Mode: Live vs Backtest Comparison")
        ##############

#         elif comparison_type == "Compare strategy and cluster trades":
        search(start_date, end_date, slave_strat, segment, exchange_name, analyse_till_last_run_sentinel=analyse_till_last_run_sentinel, short_holding_threshold=short_holding_threshold)
                
#         else:
#             st.error("Select a comparison")

    if st.sidebar.button("Logout"):
        st.session_state["authenticated"] = False
        st.session_state["user"] = None
        st.rerun()


# --- Main Control Flow ---
if "authenticated" not in st.session_state:
    st.session_state["authenticated"] = False

if not st.session_state["authenticated"]:
    login_page()
else:
    dashboard()
