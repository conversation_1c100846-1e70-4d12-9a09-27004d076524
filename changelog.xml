<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
  xmlns:pro="http://www.liquibase.org/xml/ns/pro"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd
                      http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd
                      http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
  <changeSet author="divyansh (generated)" id="1670499011404-1">
    <createTable tableName="changelog">
      <column name="strategy_name" type="TEXT" />
      <column name="change_date" type="TEXT" />
      <column name="column_name" type="TEXT" />
      <column name="new_value" type="TEXT" />
      <column name="old_value" type="TEXT" />
    </createTable>
  </changeSet>


  <changeSet author="divyansh (generated)" id="1670499011404-2">
    <createTable tableName="curr_info">
      <column name="live_start_day" type="date" />
      <column name="developer" type="VARCHAR(64)">
        <constraints nullable="false" />
      </column>
      <column name="last_run_day" type="date" />
      <column name="segment" type="VARCHAR(64)" />
      <column name="strategy_name" type="VARCHAR(128)">
        <constraints nullable="false" primaryKey="true" />
      </column>
      <column name="book_short" type="VARCHAR(128)" />
      <column name="book_long" type="VARCHAR(128)" />
      <column name="exchange_name" type="VARCHAR(64)" />
      <column name="strat_state" type="INT" />
      <column name="max_dd" type="DOUBLE" />
      <column name="submission_day" type="date" />
      <column name="trigger_coeff" type="DOUBLE" />
      <column name="limit_coeff" type="DOUBLE" />
      <column name="long_short" type="INT" />
      <column name="cluster_mapping" type="VARCHAR(64)" />
      <column name="comments" type="VARCHAR(1000)" />
      <column name="backtest_start_date" type="date" />
      <column name="monthly_sharpe" type="DOUBLE" />
      <column name="ret_dd" type="DOUBLE" />
      <column name="reworked_strategy" type="VARCHAR(128)" />
    </createTable>
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-3">
    <createTable tableName="pending_backtest">
      <column autoIncrement="true" name="id" type="INT">
        <constraints nullable="false" primaryKey="true" />
      </column>
      <column defaultValueComputed="CURRENT_TIMESTAMP" name="request_time" type="timestamp">
        <constraints nullable="false" />
      </column>
      <column name="strategy_name" type="VARCHAR(128)">
        <constraints nullable="false" />
      </column>
      <column name="type" type="VARCHAR(128)">
        <constraints nullable="false" />
      </column>
      <column name="backtest_result" type="VARCHAR(255)" />
      <column name="max_dd_monte" type="DOUBLE" />
      <column name="percentile_dd" type="DOUBLE" />
      <column name="error_description" type="TEXT" />
    </createTable>
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-4">
    <createTable tableName="roles">
      <column name="id" type="INT">
        <constraints nullable="false" primaryKey="true" />
      </column>
      <column name="name" type="VARCHAR(64)">
        <constraints nullable="false" />
      </column>
    </createTable>
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-5">
    <createTable tableName="strategy_review">
      <column name="strategy_name" type="VARCHAR(128)">
        <constraints nullable="false" primaryKey="true" />
      </column>
      <column name="timecheck" type="VARCHAR(1000)" />
      <column name="correlation_check" type="VARCHAR(1000)" />
      <column name="trade_distribution_check" type="VARCHAR(1000)" />
      <column name="risk_analysis" type="VARCHAR(1000)" />
      <column name="num_days_trading" type="VARCHAR(1000)" />
      <column name="comments" type="MEDIUMTEXT" />
      <column name="to_change" type="MEDIUMTEXT" />
    </createTable>
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-6">
    <createTable tableName="strategy_state">
      <column name="id" type="INT">
        <constraints nullable="false" primaryKey="true" />
      </column>
      <column name="state_description" type="VARCHAR(64)" />
    </createTable>
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-7">
    <createTable tableName="users">
      <column autoIncrement="true" name="id" type="INT">
        <constraints nullable="false" primaryKey="true" />
      </column>
      <column name="username" type="VARCHAR(64)">
        <constraints nullable="false" unique="true" />
      </column>
      <column name="email" type="VARCHAR(128)">
        <constraints nullable="false" unique="true" />
      </column>
      <column name="password_enc" type="VARCHAR(128)">
        <constraints nullable="false" />
      </column>
      <column name="role_id" type="INT" />
      <column name="manager" type="VARCHAR(64)" />
    </createTable>
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-8">
    <createIndex indexName="developer" tableName="curr_info">
      <column name="developer" />
    </createIndex>
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-9">
    <createIndex indexName="strat_state" tableName="curr_info">
      <column name="strat_state" />
    </createIndex>
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-10">
    <createIndex indexName="users_ibfk_1" tableName="users">
      <column name="role_id" />
    </createIndex>
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-11">
    <addForeignKeyConstraint baseColumnNames="strat_state" baseTableName="curr_info"
      constraintName="curr_info_ibfk_1" deferrable="false" initiallyDeferred="false"
      onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="id"
      referencedTableName="strategy_state" validate="true" />
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-12">
    <addForeignKeyConstraint baseColumnNames="developer" baseTableName="curr_info"
      constraintName="curr_info_ibfk_2" deferrable="false" initiallyDeferred="false"
      onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="username"
      referencedTableName="users" validate="true" />
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-13">
    <addForeignKeyConstraint baseColumnNames="strategy_name" baseTableName="strategy_review"
      constraintName="strategy_review_ibfk_1" deferrable="false" initiallyDeferred="false"
      onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="strategy_name"
      referencedTableName="curr_info" validate="true" />
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-14">
    <addForeignKeyConstraint baseColumnNames="role_id" baseTableName="users"
      constraintName="users_ibfk_1" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT"
      onUpdate="RESTRICT" referencedColumnNames="id" referencedTableName="roles" validate="true" />
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-15">
    <createTable tableName="strategy_meta_data">
      <column name="strategy_name" type="VARCHAR(128)">
        <constraints nullable="false" primaryKey="true" />
      </column>
      <column name="max_dd_submit" type="DOUBLE" />
      <column name="monthly_sharpe_submit" type="DOUBLE" />
      <column name="ret_dd_submit" type="DOUBLE" />
      <column name="max_dd_monte" type="DOUBLE" />
      <column name="percentile_dd" type="DOUBLE" />
    </createTable>
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-16">
    <addForeignKeyConstraint baseColumnNames="strategy_name" baseTableName="strategy_meta_data"
      constraintName="strategy_meta_data_ibfk_1" deferrable="false" initiallyDeferred="false"
      onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="strategy_name"
      referencedTableName="curr_info" validate="true" />
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-17">
    <sql dbms="mysql"
      endDelimiter=";"
      splitStatements="true"
      stripComments="true"> INSERT into strategy_meta_data(strategy_name, max_dd_submit,
      monthly_sharpe_submit, ret_dd_submit, max_dd_monte, percentile_dd) select t1.strategy_name,
      t1.max_dd, t1.monthly_sharpe, t1.ret_dd, t2.max_dd_monte, t2.percentile_dd from (select
      strategy_name, max_dd, monthly_sharpe, ret_dd from curr_info where max_dd!=0) t1 LEFT JOIN (
      select strategy_name, max_dd_monte, percentile_dd from pending_backtest where id in (select
      MAX(id) from pending_backtest group by strategy_name)) t2 ON t1.strategy_name =
      t2.strategy_name; </sql>
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-18">
    <dropColumn tableName="curr_info">
      <column name="max_dd" type="DOUBLE" />
      <column name="monthly_sharpe" type="DOUBLE" />
      <column name="ret_dd" type="DOUBLE" />
    </dropColumn>
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-19">
    <dropColumn tableName="pending_backtest">
      <column name="max_dd_monte" type="DOUBLE" />
      <column name="percentile_dd" type="DOUBLE" />
    </dropColumn>
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-20">
    <addColumn tableName="strategy_meta_data">
      <column name="curr_backtest_dd" type="DOUBLE" />
      <column name="last_sentinel_run" type="date" />
    </addColumn>
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-21">
    <addColumn tableName="curr_info">
      <column name="expiration_time" type="TIME" />
    </addColumn>
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-22">
    <dropColumn tableName="pending_backtest">
      <column name="type" type="VARCHAR(128)" />
      <column name="backtest_result" type="VARCHAR(255)" />
      <column name="error_description" type="text" />
    </dropColumn>
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-23">
    <addColumn tableName="pending_backtest">
      <column name="service_state" type="VARCHAR(20)" defaultValue='00000' />
    </addColumn>
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-24">
    <addColumn tableName="strategy_meta_data">
      <column name="monthly_ret_submit" type="DOUBLE" />
      <column name="monthly_ret_post" type="DOUBLE" />
      <column name="monthly_sharpe_post" type="DOUBLE" />
    </addColumn>
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-25">
    <createTable tableName="strategy_access">
      <column autoIncrement="true" name="id" type="INT">
        <constraints nullable="false" primaryKey="true" />
      </column>
      <column name="strategy_name" type="TEXT">
        <constraints nullable="false" />
      </column>
      <column name="username" type="TEXT">
        <constraints nullable="false" />
      </column>
    </createTable>
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-26">
    <modifyDataType tableName="strategy_access" columnName="strategy_name"
      newDataType="VARCHAR(128)" />
    <modifyDataType tableName="strategy_access" columnName="username" newDataType="VARCHAR(64)" />
    <addForeignKeyConstraint baseColumnNames="strategy_name" baseTableName="strategy_access"
      constraintName="strategy_access_ibfk_1" deferrable="false" initiallyDeferred="false"
      onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="strategy_name"
      referencedTableName="curr_info" validate="true" />
    <addForeignKeyConstraint baseColumnNames="username" baseTableName="strategy_access"
      constraintName="strategy_access_ibfk_2" deferrable="false" initiallyDeferred="false"
      onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="username"
      referencedTableName="users" validate="true" />
  </changeSet>
  <changeSet author="divyansh (generated)" id="1670499011404-27">
    <addColumn tableName="strategy_review">
      <column name="to_do" type="text" />
    </addColumn>
  </changeSet>


  <changeSet id="1670499011404-28" author="akshit">
    <createTable tableName="strategy_cluster_mapping">
      <column name="id" type="INT" autoIncrement="true" startWith="1">
        <constraints primaryKey="true" nullable="false" />
      </column>
      <column name="cluster_mapping" type="VARCHAR(64)" />
      <column name="strategy_name" type="VARCHAR(128)" />
    </createTable>
  </changeSet>

  <changeSet id="1670499011404-29" author="akshit">
    <addForeignKeyConstraint baseTableName="strategy_cluster_mapping"
      baseColumnNames="strategy_name"
      referencedTableName="curr_info"
      referencedColumnNames="strategy_name"
      constraintName="fk_strategy_cluster_mapping"
      onDelete="CASCADE"
      onUpdate="CASCADE" />
  </changeSet>

  <changeSet id="1670499011404-30" author="akshit">
    <sql> INSERT INTO strategy_cluster_mapping (cluster_mapping, strategy_name) SELECT
      cluster_mapping, strategy_name FROM curr_info WHERE cluster_mapping IS NOT NULL; </sql>
  </changeSet>

  <changeSet id="1670499011404-31" author="akshit">
    <dropColumn tableName="curr_info" columnName="cluster_mapping" />
  </changeSet>

  <changeSet id="1670499011404-32" author="akshit">
    <renameColumn tableName="strategy_cluster_mapping" oldColumnName="cluster_mapping"
      newColumnName="cluster_name" columnDataType="VARCHAR(64)" />
  </changeSet>

  <changeSet id="1687514099401-33" author="akshit">
    <createTable tableName="jupyter_user_map">
      <column name="jupyter_username" type="VARCHAR(64)">
        <constraints primaryKey="true" nullable="false" />
      </column>
      <column name="username" type="VARCHAR(64)" />
    </createTable>
  </changeSet>

  <changeSet id="1687514099401-34" author="akshit">
    <addForeignKeyConstraint baseTableName="jupyter_user_map"
      baseColumnNames="username"
      referencedTableName="users"
      referencedColumnNames="username"
      constraintName="fk_jupyter_user_mapping"
      onDelete="CASCADE"
      onUpdate="CASCADE" />
  </changeSet>

  <changeSet author="divyansh" id="1670499011404-35">
    <addColumn tableName="strategy_meta_data">
      <column name="monthly_ret_live" type="DOUBLE" />
      <column name="monthly_sharpe_live" type="DOUBLE" />
      <column name="max_dd_live" type="DOUBLE"/>
      <column name="ret_dd_live" type="DOUBLE" />
      <column name="trading_days_live" type="INT" />
    </addColumn>
  </changeSet>

  <changeSet author="divyansh" id="1670499011404-36">
    <addColumn tableName="strategy_meta_data">
      <column name="inout_sample_bhatt_dist" type="DOUBLE" />
    </addColumn>
  </changeSet>
 <changeSet author="muskan" id="1670499011404-37">
    <addColumn tableName="pending_backtest">
      <column name="allow_modification" type="int" />
    </addColumn>
  </changeSet>
  <changeSet id="1670499011404-38" author="muskan">
    <update tableName="pending_backtest">
        <column name="allow_modification" valueNumeric = "1"/>
    </update>
  </changeSet>
  <changeSet id="1670499011404-39" author="divyansh">
    <addColumn tableName="curr_info">
      <column name="overlap_days" type="int" defaultValue="20" />
    </addColumn>
  </changeSet>
  <changeSet id="1670499011404-40" author="divyansh">
    <addColumn tableName="strategy_meta_data">
      <column name="daily_sharpe_live" type="DOUBLE" defaultValue="20" />
    </addColumn>
  </changeSet>
  <changeSet id="1670499011404-41" author="divyansh_kankariya">
    <addColumn tableName="curr_info">
      <column name="custom_slippage" type="float">
        <constraints nullable="true"/>
      </column>
    </addColumn>
  </changeSet>
  <changeSet id="1670499011404-42" author="divyansh_kankariya">
    <insert tableName="strategy_state">
        <column name="id" value="5" />
        <column name="state_description" value="TEST" />
    </insert>
  </changeSet>
  <changeSet id="1670499011404-43" author="divyansh_kankariya">
    <createTable tableName="cluster_mapping_status">
        <column name="id" type="INT">
            <constraints primaryKey="true" nullable="false"/>
        </column>
        <column name="state_description" type="VARCHAR(8)" />
    </createTable>
  </changeSet>
  <changeSet id="1670499011404-44" author="divyansh_kankariya">
    <insert tableName="cluster_mapping_status">
        <column name="id" value="0" />
        <column name="state_description" value="LIVE_ENV" />
    </insert>
    <insert tableName="cluster_mapping_status">
        <column name="id" value="1" />
        <column name="state_description" value="TEST_ENV" />
    </insert>
  </changeSet>
  <changeSet id="1670499011404-45" author="divyansh_kankariya">
        <addColumn tableName="strategy_cluster_mapping">
            <column name="mapping_state" type="INT" defaultValue="0">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addForeignKeyConstraint 
            baseTableName="strategy_cluster_mapping"
            baseColumnNames="mapping_state"
            referencedTableName="cluster_mapping_status"
            referencedColumnNames="id"
            constraintName="fk_mapping_state"/>
  </changeSet>
  <changeSet id="1670499011404-46" author="divyansh_kankariya">
        <sql>
            INSERT INTO strategy_cluster_mapping (strategy_name, cluster_name, mapping_state)
            SELECT strategy_name, cluster_name, 1 AS mapping_state
            FROM strategy_cluster_mapping;
        </sql>
  </changeSet> 
</databaseChangeLog>