import os
from hvac import Client

# set the base directory
basedir = os.path.abspath(os.path.dirname(__name__))
from dotenv import load_dotenv


# Create the super class
class Config(object):
    SECRET_KEY = "8d04b1084ab5fc01d8d8dbc72e0092bf"
    # SQLALCHEMY_COMMIT_ON_TEARDOWN = True
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    DASHBOARD_DATA_BUCKET = "analytics-data"
    STRAT_STATUS_PATH_MAPPING = {
        "pending": "pending_strats",
        "live": "submitted_strats",
        "rejected": "rejected_strats",
        "dead": "submitted_strats",
        "test": "pending_strats",
    }
    SWAGGER = {"title": "SAMBA API", "uiversion": 3}
    LOGGING_FORMAT = "%(asctime)s.%(msecs)03d %(levelname)s %(username)s: %(message)s"
    LOGGING_PATH = "SAMBA_logs.log"
    SERVICE_INDEX = {
        0: "PYCE",
        1: "TIME_ANALYSIS",
        2: "BACKTEST",
        3: "KIVIFOLIO",
        4: "CLUSTER_BACKTEST",
    }
    SERVICE_UPSTREAMS = {1: [0], 2: [0], 3: [2], 4: [2]}
    SERVICE_DOWNSTREAMS = {0: [1, 2], 2: [3, 4]}
    CLICKHOUSE_HOST = "*************"
    CLICKHOUSE_PORT = "9000"
    CLICKHOUSE_USER = "default"
    CLICKHOUSE_PASS = ""
    CLICKHOUSE_DB = "slippage_db"

    UNION_CORR_TH = 0.5
    PNL_DEPENDENCE_TH = 0.75
    INTERSECTION_CORR_TH = 0.6

    SUPPORTED_EXCHANGES = ["NSE", "NCDEX", "MCX", "GIFT", "KRX", "US"]

    ALL_DATES_DICT = {
        "NSE": "ALL_DATES.npy",
        "MCX": "ALL_DATES_MCX.npy",
        "COMM": "ALL_DATES_MCX.npy",
        "NCDEX": "ALL_DATES_NCDEX.npy",
        "GIFT": "ALL_DATES_GIFT.npy",
        "KRX": "ALL_DATES_KRX.npy",
        "US": "ALL_DATES_INTERNATIONAL",
    }


# Create the development config
class DevelopmentConfig(Config):
    NAME = "DEVELOPMENT"
    DEBUG = True
    MINIO_URL = "*************:9999"
    MINIO_ACCESS_KEY = "super"
    MINIO_SECRET_KEY = "doopersecret"
    SQLALCHEMY_DATABASE_URI = "mysql+pymysql://root:123@*************:3307/strat_db"
    SQLALCHEMY_BINDS = {
        "portfolio_stats": "mysql+pymysql://root:123@*************:3307/portfolio_stats"
    }
    FILE_SERVER = "http://*************:8000"
    PORT = 9005
    TEAMS_WEBHOOK = ""
    KAFKA_SERVER = "*************:9092"
    KAFKA_TOPIC = "samba_test"


# Create the testing config
class TestingConfig(Config):
    NAME = "TESTING"
    DEBUG = True
    MINIO_URL = ""
    MINIO_ACCESS_KEY = ""
    MINIO_SECRET_KEY = ""
    FILE_SERVER = ""
    PORT = 9005
    TEAMS_WEBHOOK = ""
    SQLALCHEMY_DATABASE_URI = "sqlite:///:memory:"
    SQLALCHEMY_BINDS = {"portfolio_stats": "sqlite:///:memory:"}
    FILE_SERVER = "tests/test_data"


# create the production config
class ProductionConfig(Config):
    load_dotenv()

    @classmethod
    def init_app(cls):
        client = Client(url=cls.VAULT_URL)
        client.auth.approle.login(cls.VAULT_ROLE, cls.VAULT_SECRET)
        result = client.read("credentials/data/access/samba_creds")["data"]["data"][
            "data"
        ]
        cls.SQLALCHEMY_BINDS[
            "portfolio_stats"
        ] = f"postgresql+psycopg2://{result['db_username']}:{result['db_password']}@{result['host']}:{result['port']}/portfolio_stats"

    NAME = "PRODUCTION"
    DEBUG = os.environ.get("DEBUG", False)
    MINIO_URL = os.environ.get("MINIO_URL", "")
    MINIO_ACCESS_KEY = os.environ.get("MINIO_ACCESS_KEY", "")
    MINIO_SECRET_KEY = os.environ.get("MINIO_SECRET_KEY", "")
    SQLALCHEMY_DATABASE_URI = os.environ.get("SQLALCHEMY_DATABASE_URI", "")
    SQLALCHEMY_BINDS = {"portfolio_stats": os.environ.get("SQLALCHEMY_BINDS", "")}
    FILE_SERVER = os.environ.get("FILE_SERVER", "")
    PORT = os.environ.get("PORT", 5000)
    TEAMS_WEBHOOK = os.environ.get("TEAMS_WEBHOOK", "")
    KAFKA_SERVER = os.environ.get("KAFKA_SERVER", "")
    KAFKA_TOPIC = os.environ.get("KAFKA_TOPIC", "")
    VAULT_URL = os.environ.get("VAULT_URL", "")
    VAULT_ROLE = os.environ.get("VAULT_ROLE", "")
    VAULT_SECRET = os.environ.get("VAULT_SECRET", "")
