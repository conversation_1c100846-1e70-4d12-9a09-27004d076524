pipeline {
    agent any
    environment {
        DOCKER_IMAGE = '*************:15050/devops/commonlibs/samba_server/production'
        DOCKER_TAG = 'latest'
        CONTAINER_NAME = 'samba_server'
        SERVER_HOST = '*************'
        REPO_URL = 'http://*************:15000/samba-universe/samba.git'
        REPO_DIR = "${WORKSPACE}/samba"
        BRANCH = 'production'
    }

    stages {
        stage('Checkout or Clone') {
            steps {
                script {
                    if (fileExists("${REPO_DIR}/.git")) {
                        echo "Repository exists, pulling latest changes..."
                        dir(REPO_DIR) {
                            sh "git fetch origin ${BRANCH}"
                            sh "git reset --hard origin/${BRANCH}"
                        }
                    } else {
                        echo "Repository not found, cloning..."
                        withCredentials([usernamePassword(credentialsId: 'samba-jenkins-gitlab', usernameVariable: 'GIT_USER', passwordVariable: 'GIT_PASS')]) {
                            sh "git clone http://${GIT_USER}:${GIT_PASS}@*************:15000/samba-universe/samba.git ${REPO_DIR}"
                        }
                    }
                }
            }
        }

stage('Check Changes') {
    steps {
        script {
            env.SKIP_DEPLOY = 'false'  // Set default value

            sh "cd ${REPO_DIR} && git fetch origin"

            def changedFiles = sh(
                script: """
                    cd ${REPO_DIR}
                    if git rev-parse HEAD~1 >/dev/null 2>&1; then
                        git diff --name-only HEAD HEAD~1
                    else
                        echo "No previous commit found"
                    fi
                """,
                returnStdout: true
            ).trim()

            echo "Changed files: ${changedFiles}"

            if (changedFiles.contains("No previous commit found")) {
                echo "First commit or shallow clone, deployment will proceed."
            } else {
                def fileList = changedFiles.split('\n').collect { it.trim() }
                echo "Changed files list: ${fileList}"

                def onlyCiCdFiles = fileList.every { file ->
                    file == 'jenkinsfile' || file == '.gitlab-ci.yml'
                }

                if (onlyCiCdFiles && !fileList.isEmpty()) {
                    echo "Only CI/CD files changed. Skipping deployment."
                    env.SKIP_DEPLOY = 'true'
                }
            }

            echo "Final SKIP_DEPLOY value after Check Changes: ${env.SKIP_DEPLOY}"
        }
    }
}

        stage('Setup') {
            when {
                expression { return env.SKIP_DEPLOY == 'false' }  // Ensure proper evaluation
            }
            steps {
                script {
                    withCredentials([[
                        $class: 'VaultUsernamePasswordCredentialBinding',
                        credentialsId: '*************',
                        passwordVariable: 'PASSWORD',
                        usernameVariable: 'USERNAME'
                    ]]) {
                        try {
                            echo "Setting up SSH connection..."
                            env.SSH_CMD = "sshpass -p ${PASSWORD} ssh -o StrictHostKeyChecking=no ${USERNAME}@${SERVER_HOST}"
                            env.SUDO_PASS = PASSWORD
                            
                            def result = sh(script: "${SSH_CMD} 'echo Connection successful'", returnStatus: true)
                            if (result != 0) {
                                error "SSH connection failed with exit code ${result}"
                            }
                            echo "SSH connection test completed successfully"
                        } catch (Exception e) {
                            echo "Error in Setup stage: ${e.getMessage()}"
                            throw e
                        }
                    }
                }
            }
        }

        stage('Deploy Container') {
            when {
                expression { return env.SKIP_DEPLOY == 'false' }  // Fix condition
            }
            steps {
                sh """#!/bin/bash
                    set -x
                    echo '${SUDO_PASS}' | ${SSH_CMD} 'sudo -S docker container rm -f ${CONTAINER_NAME} || true'
                    echo '${SUDO_PASS}' | ${SSH_CMD} 'sudo -S docker pull ${DOCKER_IMAGE}:${DOCKER_TAG}'
                    echo '${SUDO_PASS}' | ${SSH_CMD} 'sudo -S docker run -d \
                        --name ${CONTAINER_NAME} \
                        -p 9005:9005/tcp \
                        -v /home/<USER>/repos/SAMBA_logs.log:/samba/SAMBA_logs.log \
                        ${DOCKER_IMAGE}:${DOCKER_TAG} live'
                """
            }
        }

        stage('Verify Deployment') {
            when {
                expression { return env.SKIP_DEPLOY == 'false' }  // Fix condition
            }
         steps {
                sh """#!/bin/bash
                    set -x
                    echo '${SUDO_PASS}' | ${SSH_CMD} 'sudo -S docker ps | grep ${CONTAINER_NAME} | { echo ""; cat; }'
                """
            }
        }
    }

    post {
        success {
            script {
                if (env.SKIP_DEPLOY == 'true') {
                    echo 'Deployment was skipped as no relevant changes were detected.'
                } else {
                    echo 'Docker container deployment was successful!'
                }
            }
        }
        failure {
            script {
                if (env.SKIP_DEPLOY == 'false') {
                    echo 'Docker container deployment failed. Fetching logs...'
                    sh """#!/bin/bash
                        set -x
                        echo '${SUDO_PASS}' | ${SSH_CMD} 'sudo -S docker logs ${CONTAINER_NAME} || true'
                    """
                }
            }
        }
    }
}