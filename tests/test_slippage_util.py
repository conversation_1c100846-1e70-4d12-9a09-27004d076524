import unittest
from app.utility.slippage_util import (
    generate_overall_analysis_dict,
    generate_slip_graph,
)
import datetime
import numpy as np
from tests.base_test_fixture import BaseTestFixture


class TestReviewUtil(BaseTestFixture):
    def test_generate_overall_analysis_dict(self) -> None:
        query_res = [(-1, 12.97, 4881.45), (1, 14.41576, 3844.266)]
        result = generate_overall_analysis_dict(query_res)
        self.assertEqual(len(result), 4)
        self.assertAlmostEqual(result[0], 13.607, 2)
        self.assertAlmostEqual(result[1], 14.41576, 2)
        self.assertAlmostEqual(result[2], 12.97, 2)
        self.assertAlmostEqual(result[3], 8725.716, 2)

        query_res = [(1, 0.01, 100), (2, 0.03, 300)]
        expected_res = [np.nan, np.nan, np.nan, np.nan]
        res_lis = generate_overall_analysis_dict(query_res)
        self.assertEqual(res_lis, expected_res)

    def test_generate_slip_graph(self) -> None:
        slippage_dict = {
            datetime.date(2023, 1, 3): [5.4, 5.8, 4.8, 25.5],
            datetime.date(2023, 1, 4): [15.4, 14.8, 19.2, 30.5],
            datetime.date(2023, 1, 5): [9.5, 8.5, 9.8, 28],
            datetime.date(2023, 1, 6): [5.4, 5.8, 4.8, 25.5],
        }

        result = generate_slip_graph(slippage_dict)
        self.assertEqual(len(result), 2)


if __name__ == "__main__":
    unittest.main()
