import unittest
from unittest.mock import patch, MagicMock
import pandas as pd
from app.utility.risk_util import fetch_risk_limits, update_risk_limits
from tests.base_test_fixture import BaseTestFixture


class TestRiskUtil(BaseTestFixture):
    def setUp(self) -> None:
        super().setUp()
        self.mock_data = b"OptidxSellLimit,100\nOptidxBuyLimit,200\n"

    def test_fetch_risk_limits_success(self):
        with self.app.test_request_context():
            with patch("app.utility.risk_util.current_app") as mock_current_app, patch(
                "app.utility.risk_util.minio"
            ) as mock_minio:

                mock_current_app.config = {"DASHBOARD_DATA_BUCKET": "test-bucket"}
                mock_minio.get_object.return_value.data = self.mock_data
                result_df, error_msg = fetch_risk_limits()
                expected_df = pd.DataFrame(
                    {
                        "variable_name": ["OptidxSellLimit", "OptidxBuyLimit"],
                        "variable_value": [100, 200],
                    }
                )
                pd.testing.assert_frame_equal(result_df, expected_df)
                self.assertEqual(error_msg, None)

    def test_fetch_risk_limits_failure(self):
        with self.app.test_request_context():
            with patch("app.utility.risk_util.current_app") as mock_current_app, patch(
                "app.utility.risk_util.minio"
            ) as mock_minio:

                mock_current_app.config = {"DASHBOARD_DATA_BUCKET": "test-bucket"}
                mock_minio.get_object.side_effect = Exception("Minio Error")
                result_df, error_msg = fetch_risk_limits()
                self.assertTrue(result_df.empty)
                self.assertEqual(error_msg, "Failed to fetch risk limits.")

    def test_update_risk_limits_success(self):
        with self.app.test_request_context():
            with patch("app.utility.risk_util.current_app") as mock_current_app, patch(
                "app.utility.risk_util.minio"
            ) as mock_minio, patch("app.utility.risk_util.flash") as mock_flash:

                mock_current_app.config = {"DASHBOARD_DATA_BUCKET": "test-bucket"}
                limits_df = pd.DataFrame(
                    {
                        "variable_name": ["OptidxSellLimit", "OptidxBuyLimit"],
                        "variable_value": [150, 250],
                    }
                )
                mock_minio.put_object.return_value = None
                error_msg = update_risk_limits(limits_df)
                self.assertEqual(error_msg, None)
                mock_minio.put_object.assert_called_once()
                mock_flash.assert_called_once_with(
                    "Limits updated successfully.", "success"
                )

    def test_update_risk_limits_failure(self):
        with self.app.test_request_context():
            with patch("app.utility.risk_util.current_app") as mock_current_app, patch(
                "app.utility.risk_util.minio"
            ) as mock_minio, patch("app.utility.risk_util.flash") as mock_flash:

                mock_current_app.config = {"DASHBOARD_DATA_BUCKET": "test-bucket"}
                limits_dict = {"OptidxSellLimit": 150, "OptidxBuyLimit": 250}
                mock_minio.put_object.side_effect = Exception("Minio Error")
                error_msg = update_risk_limits(limits_dict)
                mock_flash.assert_not_called()
                self.assertEqual(error_msg, "Failed to update risk limits.")


if __name__ == "__main__":
    unittest.main()
