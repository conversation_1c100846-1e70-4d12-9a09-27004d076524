import unittest
import sys
from flask import request

# sys.path.append("/home/<USER>/analytics_dashboard")
from flask import template_rendered
from app.models import User
from flask_login import current_user
from tests.base_test_fixture import BaseTestFixture
from contextlib import contextmanager


@contextmanager
def captured_templates(app):
    recorded = []

    def record(sender, template, context, **extra):
        recorded.append((template, context))

    template_rendered.connect(record, app)
    try:
        yield recorded
    finally:
        template_rendered.disconnect(record, app)


class TestAuthViews(BaseTestFixture):
    def test_register(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    message = "form error not captured!"
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_developer, "password": self.password},
                    )
                    response = self.client.post(
                        "/register",
                        data={
                            "username": "test_new",
                            "email": "<EMAIL>",
                            "password": self.password,
                            "confirm_password": self.password,
                            "roles": 3,
                            "manager": "test_manager",
                        },
                    )
                    self.assertFalse(
                        "test_new" in [user.username for user in User.query.all()],
                        msg="developer registered a new user",
                    )
                    self.assertEqual(
                        "/home",
                        response.location,
                        msg="developer got access to registration page!",
                    )
                    self.assertEqual(
                        response.status_code, 302, msg=self.message_response_code
                    )
                    response = self.client.get("/sign_out")

                    response = self.client.post(
                        "/login",
                        data={"email": self.email_manager, "password": self.password},
                    )
                    response = self.client.post(
                        "/register",
                        data={
                            "username": "test_new",
                            "email": "<EMAIL>",
                            "password": self.password,
                            "confirm_password": self.password,
                            "roles": 3,
                            "manager": "test_manager",
                        },
                    )
                    self.assertTrue(
                        "test_new" in [user.username for user in User.query.all()],
                        msg="new user registration failed!",
                    )
                    self.assertEqual(
                        "/user_management",
                        response.location,
                        msg="redirection incorrect!",
                    )
                    response = self.client.get("/sign_out")

                    response = self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )

                    response = self.client.post(
                        "/register",
                        data={
                            "username": "test_admin",
                            "email": self.email_admin,
                            "password": self.password,
                            "confirm_password": self.password,
                            "roles": 1,
                            "manager": None,
                        },
                    )
                    template, context = templates[0]
                    self.assertEqual(
                        template.name, "auth/register.html", msg=self.message_template
                    )
                    self.assertEqual(
                        {
                            "username": ["User Already Exists"],
                            "email": ["Email Already Exists"],
                        },
                        context["form"].errors,
                        msg=message,
                    )
                    response = self.client.post(
                        "/register",
                        data={
                            "username": "test_new_1",
                            "email": "<EMAIL>",
                            "password": self.password,
                            "confirm_password": "test@456",
                            "roles": 3,
                            "manager": "test_admin",
                        },
                    )
                    template, context = templates[1]
                    self.assertEqual(
                        template.name, "auth/register.html", msg=self.message_template
                    )
                    self.assertEqual(
                        {"confirm_password": ["Passwords must match"]},
                        context["form"].errors,
                        msg=message,
                    )
                    response = self.client.post(
                        "/register",
                        data={
                            "username": "test_new_1",
                            "email": "<EMAIL>",
                            "password": self.password,
                            "confirm_password": self.password,
                            "roles": 3,
                            "manager": "test_admin",
                        },
                    )
                    self.assertTrue(
                        "test_new_1" in [user.username for user in User.query.all()],
                        msg="new user registration failed!",
                    )

    def test_login(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_developer, "password": "test@456"},
                    )
                    self.assertEqual(
                        response.status_code, 200, msg=self.message_response_code
                    )
                    template, context = templates[0]
                    self.assertEqual(
                        template.name, "auth/login.html", msg=self.message_template
                    )
                    self.assertNotEqual(
                        current_user,
                        User.query.filter_by(username="test_developer"),
                        msg="user logged in with wrong password!",
                    )
                    response = self.client.post(
                        "/login",
                        data={"email": "test_admin", "password": self.password},
                    )
                    self.assertEqual(
                        response.status_code, 302, msg=self.message_response_code
                    )
                    self.assertEqual(
                        "/home", response.location, msg="redirection incorrect!"
                    )
                    self.assertEqual(
                        current_user,
                        User.query.filter_by(username="test_admin").first(),
                        msg="login failed!",
                    )
                    response = self.client.post(
                        "/login",
                        data={"email": "test_admin", "password": self.password},
                    )
                    self.assertEqual(
                        "/home", response.location, msg="already logged in user!"
                    )

    def test_sign_out(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )

                self.assertEqual(
                    current_user,
                    User.query.filter_by(username="test_admin").first(),
                    msg="login failed!",
                )

                response = self.client.get("/sign_out")

                self.assertEqual(
                    response.status_code, 302, msg=self.message_response_code
                )
                self.assertNotEqual(
                    current_user,
                    User.query.filter_by(username="test_admin").first(),
                    msg="sign out failed!",
                )

    def test_change_password(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )

                    message_popup = "visibility of {} popup incorrect!"
                    response = self.client.get("/change_password")
                    template, context = templates[0]
                    self.assertEqual(
                        template.name, "auth/profile.html", msg=self.message_template
                    )
                    self.assertEqual(
                        context["vis_success"],
                        "hidden",
                        msg=message_popup.format("success"),
                    )
                    self.assertEqual(
                        context["vis_fail"],
                        "hidden",
                        msg=message_popup.format("failure"),
                    )
                    response = self.client.post(
                        "/change_password",
                        data={
                            "new_password": "test@456",
                            "confirm_new_password": "test@456",
                        },
                    )
                    template, context = templates[1]
                    self.assertEqual(
                        template.name, "auth/profile.html", msg=self.message_template
                    )
                    self.assertEqual(
                        context["vis_success"],
                        "visible",
                        msg=message_popup.format("success"),
                    )
                    self.assertEqual(
                        context["vis_fail"],
                        "hidden",
                        msg=message_popup.format("failure"),
                    )

                    response = self.client.get("/sign_out")
                    message = "password incorrectly changed!"
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )
                    self.assertIn(
                        b"Invalid Email or Password", response.data, msg=message
                    )

                    self.assertNotEqual(
                        current_user,
                        User.query.filter_by(username="test_admin").first(),
                        msg=message,
                    )

                    response = self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": "test@456"},
                    )
                    self.assertEqual(
                        current_user,
                        User.query.filter_by(username="test_admin").first(),
                        msg=message,
                    )

    def test_manage_users(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    # Logged in as developer
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_developer, "password": self.password},
                    )

                    response = self.client.get("/user_management")
                    self.assertEqual(
                        "/home",
                        response.location,
                        msg="developer got access to user management page!",
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as manager
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_manager, "password": self.password},
                    )
                    response = self.client.get("/user_management")
                    template, context = templates[0]
                    self.assertEqual(
                        template.name,
                        "auth/user_management.html",
                        self.message_template,
                    )
                    self.assertTrue(
                        User.query.filter_by(username="test_developer").first()
                        in context["user_list"],
                        msg="manager did not get access to developer under him!",
                    )
                    self.assertFalse(
                        User.query.filter_by(username="test_admin").first()
                        in context["user_list"],
                        msg="manager got access to admin!",
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as admin
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )
                    response = self.client.get("/user_management")
                    template, context = templates[1]
                    self.assertEqual(
                        template.name,
                        "auth/user_management.html",
                        self.message_template,
                    )
                    self.assertEqual(
                        set(user.username for user in context["user_list"]),
                        set(["test_admin", "test_manager", "test_developer"]),
                        msg="manager did not get access to developer under him!",
                    )

    def test_edit_users(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                message = "{} editing {} testing failed!"
                message_editing = "{} changing role of {} not executed!"

                # Logged in as developer
                response = self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                response = self.client.post(
                    "/user_management/edit_user",
                    data={"username": "test_admin", "role": "ADMIN"},
                )
                self.assertEqual(
                    "/home", response.location, msg=message.format("developer", "admin")
                )
                response = self.client.post(
                    "/user_management/edit_user",
                    data={"username": "test_manager", "role": "ADMIN"},
                )
                self.assertEqual(
                    "/home",
                    response.location,
                    msg=message.format("developer", "manager"),
                )
                response = self.client.post(
                    "/user_management/edit_user",
                    data={"username": "test_developer", "role": "ADMIN"},
                )
                self.assertEqual(
                    "/home",
                    response.location,
                    msg=message.format("developer", "developer"),
                )
                response = self.client.get("/sign_out")

                # Logged in as manager
                self.add_user(
                    password=self.password,
                    username="test_developer_2",
                    email="<EMAIL>",
                    role_id=3,
                    manager="test_admin",
                )
                response = self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )
                response = self.client.post(
                    "/user_management/edit_user",
                    data={"username": "test_admin", "role": "ADMIN"},
                )
                self.assertEqual(
                    b"failed", response.data, msg=message.format("manager", "admin")
                )
                response = self.client.post(
                    "/user_management/edit_user",
                    data={"username": "test_manager", "role": "ADMIN"},
                )
                self.assertEqual(
                    b"failed", response.data, msg=message.format("manager", "manager")
                )
                response = self.client.post(
                    "/user_management/edit_user",
                    data={"username": "test_developer_2", "role": "ADMIN"},
                )
                self.assertEqual(
                    b"failed",
                    response.data,
                    msg=message.format("manager", "developer not under him"),
                )
                response = self.client.post(
                    "/user_management/edit_user",
                    data={"username": "test_developer", "role": "ADMIN"},
                )
                self.assertEqual(
                    b"success",
                    response.data,
                    msg=message.format("manager", "developer"),
                )
                self.assertTrue(
                    User.query.filter_by(username="test_developer").first().role_id
                    == 1,
                    msg=message_editing.format("manager", "developer"),
                )
                self.assertTrue(
                    User.query.filter_by(username="test_developer").first().manager
                    is None,
                    msg=message_editing.format("manager", "developer"),
                )
                User.query.filter_by(username="test_developer").first().role_id = 3
                User.query.filter_by(
                    username="test_developer"
                ).first().manager = "test_manager"
                response = self.client.post(
                    "/user_management/edit_user",
                    data={
                        "username": "test_developer",
                        "role": "DEVELOPER",
                        "manager": "test_admin",
                    },
                )
                self.assertEqual(
                    b"success",
                    response.data,
                    msg=message.format("manager", "developer"),
                )
                self.assertTrue(
                    User.query.filter_by(username="test_developer").first().role_id
                    == 3,
                    msg=message_editing.format("manager", "developer"),
                )
                self.assertTrue(
                    User.query.filter_by(username="test_developer").first().manager
                    == "test_admin",
                    msg=message_editing.format("manager", "developer"),
                )

                response = self.client.get("/sign_out")

                # Logged in as admin
                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                response = self.client.post(
                    "/user_management/edit_user",
                    data={
                        "username": "test_developer",
                        "role": "DEVELOPER",
                        "manager": "test_manager",
                    },
                )
                self.assertEqual(
                    b"success", response.data, msg=message.format("admin", "developer")
                )
                self.assertTrue(
                    User.query.filter_by(username="test_developer").first().role_id
                    == 3,
                    msg=message_editing.format("admin", "developer"),
                )
                self.assertTrue(
                    User.query.filter_by(username="test_developer").first().manager
                    == "test_manager",
                    msg=message_editing.format("admin", "developer"),
                )
                response = self.client.post(
                    "/user_management/edit_user",
                    data={
                        "username": "test_manager",
                        "role": "DEVELOPER",
                        "manager": "test_admin",
                    },
                )
                self.assertEqual(
                    b"failed", response.data, msg=message.format("admin", "manager")
                )
                response = self.client.post(
                    "/user_management/edit_user",
                    data={"username": "test_developer", "role": "ADMIN"},
                )
                self.assertEqual(
                    b"success", response.data, msg=message.format("admin", "developer")
                )
                self.assertTrue(
                    User.query.filter_by(username="test_developer").first().role_id
                    == 1,
                    msg=message_editing.format("admin", "developer"),
                )
                self.assertTrue(
                    User.query.filter_by(username="test_developer").first().manager
                    is None,
                    msg=message_editing.format("admin", "developer"),
                )
                response = self.client.post(
                    "/user_management/edit_user",
                    data={
                        "username": "test_manager",
                        "role": "DEVELOPER",
                        "manager": "test_manager",
                    },
                )
                self.assertEqual(
                    b"failed", response.data, msg=message.format("admin", "manager")
                )
                response = self.client.post(
                    "/user_management/edit_user",
                    data={
                        "username": "test_manager",
                        "role": "DEVELOPER",
                        "manager": "test_admin",
                    },
                )
                self.assertEqual(
                    b"success", response.data, msg=message.format("admin", "developer")
                )
                self.assertTrue(
                    User.query.filter_by(username="test_manager").first().role_id == 3,
                    msg=message_editing.format("admin", "manager"),
                )
                self.assertTrue(
                    User.query.filter_by(username="test_manager").first().manager
                    == "test_admin",
                    msg=message_editing.format("admin", "manager"),
                )
                response = self.client.post(
                    "/user_management/edit_user",
                    data={
                        "username": "test_developer",
                        "role": "DEVELOPER",
                        "manager": "test_admin",
                    },
                )
                self.assertEqual(
                    b"success", response.data, msg=message.format("admin", "developer")
                )
                self.assertTrue(
                    User.query.filter_by(username="test_developer").first().role_id
                    == 3,
                    msg=message_editing.format("admin", "developer"),
                )
                self.assertTrue(
                    User.query.filter_by(username="test_developer").first().manager
                    == "test_admin",
                    msg=message_editing.format("admin", "developer"),
                )
                response = self.client.post(
                    "/user_management/edit_user",
                    data={
                        "username": "test_developer",
                        "rol": "TESTER",
                        "manager": "test_admin",
                    },
                )
                self.assertEqual(
                    b"failed",
                    response.data,
                    msg="wrong format of post request succeeded!",
                )

    def test_edit_password(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                message = "{} editing {} testing failed!"
                message_editing = "{} changing password of {} not executed!"
                password = "test@456"

                # Logged in as developer
                response = self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                response = self.client.post(
                    "/user_management/edit_user_password",
                    data={
                        "username": "test_admin",
                        "password": password,
                        "confirm_password": password,
                    },
                )
                self.assertEqual(
                    "/home", response.location, msg=message.format("developer", "admin")
                )
                response = self.client.post(
                    "/user_management/edit_user_password",
                    data={
                        "username": "test_manager",
                        "password": password,
                        "confirm_password": password,
                    },
                )
                self.assertEqual(
                    "/home",
                    response.location,
                    msg=message.format("developer", "manager"),
                )
                response = self.client.post(
                    "/user_management/edit_user_password",
                    data={
                        "username": "test_developer",
                        "password": password,
                        "confirm_password": password,
                    },
                )
                self.assertEqual(
                    "/home",
                    response.location,
                    msg=message.format("developer", "developer"),
                )
                response = self.client.get("/sign_out")

                # Logged in as manager
                self.add_user(
                    password=self.password,
                    username="test_developer_2",
                    email="<EMAIL>",
                    role_id=3,
                    manager="test_admin",
                )
                response = self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )
                response = self.client.post(
                    "/user_management/edit_user_password",
                    data={
                        "username": "test_admin",
                        "password": password,
                        "confirm_password": password,
                    },
                )
                self.assertEqual(
                    b"failed", response.data, msg=message.format("manager", "admin")
                )
                response = self.client.post(
                    "/user_management/edit_user_password",
                    data={
                        "username": "test_manager",
                        "password": password,
                        "confirm_password": password,
                    },
                )
                self.assertEqual(
                    b"failed", response.data, msg=message.format("manager", "manager")
                )
                response = self.client.post(
                    "/user_management/edit_user_password",
                    data={
                        "username": "test_developer_2",
                        "password": password,
                        "confirm_password": password,
                    },
                )
                self.assertEqual(
                    b"failed",
                    response.data,
                    msg=message.format("manager", "developer not under him"),
                )
                response = self.client.post(
                    "/user_management/edit_user_password",
                    data={
                        "username": "test_developer",
                        "password": password,
                        "confirm_password": password,
                    },
                )
                self.assertEqual(
                    b"success",
                    response.data,
                    msg=message.format("manager", "developer"),
                )
                response = self.client.get("/sign_out")
                response = self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": "test@456"},
                )
                self.assertEqual(
                    current_user,
                    User.query.filter_by(username="test_developer").first(),
                    msg=message_editing.format("manager", "developer"),
                )
                response = self.client.get("/sign_out")

                # Logged in as admin
                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                response = self.client.post(
                    "/user_management/edit_user_password",
                    data={
                        "username": "test_manager",
                        "password": password,
                        "confirm_password": password,
                    },
                )
                self.assertEqual(
                    b"success", response.data, msg=message.format("admin", "manager")
                )
                response = self.client.get("/sign_out")
                response = self.client.post(
                    "/login", data={"email": self.email_manager, "password": "test@456"}
                )
                self.assertEqual(
                    current_user,
                    User.query.filter_by(username="test_manager").first(),
                    msg=message_editing.format("manager", "developer"),
                )
                response = self.client.get("/sign_out")

                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                response = self.client.post(
                    "/user_management/edit_user_password",
                    data={
                        "username": "test_developer",
                        "password": password,
                        "confirm_password": password,
                    },
                )
                self.assertEqual(
                    b"success", response.data, msg=message.format("admin", "developer")
                )
                response = self.client.get("/sign_out")
                response = self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": "test@456"},
                )
                self.assertEqual(
                    current_user,
                    User.query.filter_by(username="test_developer").first(),
                    msg=message_editing.format("manager", "developer"),
                )
                response = self.client.get("/sign_out")

                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                response = self.client.post(
                    "/user_management/edit_user_password",
                    data={
                        "username": "test_admin",
                        "password": password,
                        "confirm_password": password,
                    },
                )
                self.assertEqual(
                    b"success", response.data, msg=message.format("admin", "admin")
                )
                response = self.client.get("/sign_out")
                response = self.client.post(
                    "/login", data={"email": self.email_admin, "password": "test@456"}
                )
                self.assertEqual(
                    current_user,
                    User.query.filter_by(username="test_admin").first(),
                    msg=message_editing.format("manager", "developer"),
                )
                response = self.client.post(
                    "/user_management/edit_user_password",
                    data={
                        "username": "test_admin",
                        "pass": password,
                        "confirm_password": password,
                    },
                )
                self.assertEqual(
                    b"failed",
                    response.data,
                    msg="wrong format of post request succeeded!",
                )

    def test_delete_user(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                message = "{} deleting {} testing failed!"

                message_deletion = "{} deletion of {} not executed!"
                # Logged in as developer
                response = self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                response = self.client.post(
                    "/user_management/delete_user", data={"username": "test_admin"}
                )
                self.assertEqual(
                    "/home", response.location, msg=message.format("developer", "admin")
                )
                response = self.client.post(
                    "/user_management/delete_user", data={"username": "test_manager"}
                )
                self.assertEqual(
                    "/home",
                    response.location,
                    msg=message.format("developer", "manager"),
                )
                response = self.client.post(
                    "/user_management/delete_user", data={"username": "test_developer"}
                )
                self.assertEqual(
                    "/home",
                    response.location,
                    msg=message.format("developer", "developer"),
                )
                response = self.client.get("/sign_out")

                # Logged in as manager
                response = self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )
                response = self.client.post(
                    "/user_management/delete_user", data={"username": "test_admin"}
                )
                self.assertEqual(
                    b"failed", response.data, msg=message.format("manager", "admin")
                )
                response = self.client.post(
                    "/user_management/delete_user", data={"username": "test_manager"}
                )
                self.assertEqual(
                    b"failed", response.data, msg=message.format("manager", "manager")
                )
                response = self.client.post(
                    "/user_management/delete_user", data={"username": "test_developer"}
                )
                self.assertEqual(
                    b"success",
                    response.data,
                    msg=message.format("manager", "developer"),
                )
                self.assertTrue(
                    "test_developer"
                    not in [user.username for user in User.query.all()],
                    msg=message_deletion.format("manager", "developer"),
                )

                self.add_user(
                    password=self.password,
                    username="test_developer",
                    email=self.email_developer,
                    role_id=3,
                    manager="test_manager",
                )
                response = self.client.get("/sign_out")

                # Logged in as admin
                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                response = self.client.post(
                    "/user_management/delete_user", data={"username": "test_manager"}
                )
                self.assertEqual(
                    b"success", response.data, msg=message.format("admin", "manager")
                )
                self.assertTrue(
                    "test_manager" not in [user.username for user in User.query.all()],
                    msg=message_deletion.format("admin", "manager"),
                )
                response = self.client.post(
                    "/user_management/delete_user", data={"username": "test_developer"}
                )
                self.assertEqual(
                    b"success", response.data, msg=message.format("admin", "developer")
                )
                self.assertTrue(
                    "test_developer"
                    not in [user.username for user in User.query.all()],
                    msg=message_deletion.format("admin", "developer"),
                )
                response = self.client.post(
                    "/user_management/delete_user", data={"username": "test_admin"}
                )
                self.assertEqual(
                    b"success", response.data, msg=message.format("admin", "admin")
                )
                self.assertTrue(
                    "test_admin" not in [user.username for user in User.query.all()],
                    msg=message_deletion.format("admin", "admin"),
                )
                response = self.client.post(
                    "/user_management/delete_user", data={"user": "test_admin"}
                )
                self.assertEqual(
                    b"failed",
                    response.data,
                    msg="wrong format of post request succeeded!",
                )

    def test_index(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    response = self.client.get("/home")
                    self.assertEqual(
                        response.status_code, 200, msg=self.message_template
                    )
                    template = templates[0][0]
                    self.assertEqual(
                        template.name, "home.html", msg=self.message_template
                    )
                    self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )
                    response = self.client.get("/home")
                    self.assertEqual(
                        response.status_code, 302, msg=self.message_template
                    )
                    self.assertEqual(
                        response.location, "/strat_home", msg=self.message_template
                    )


if __name__ == "__main__":
    unittest.main()
