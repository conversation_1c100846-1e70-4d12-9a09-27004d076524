from flask import template_rendered, current_app
import json
import unittest
from app.models import (
    StrategyClusterMapping,
    User,
    db,
    PendingBacktests,
    Strategy,
    StrategyReview,
    StrategyMetaData,
    StrategyAccess,
    ClusterMappingStatus,
    Status,
)
import datetime
import pandas as pd
from flask_login import current_user
from contextlib import contextmanager
import numpy as np
from app.utility.strat_review_util import validate_download_request
from tests.base_test_fixture import BaseTestFixture
from app.strat_add import strat_add_views
from unittest.mock import patch


@contextmanager
def captured_templates(app):
    recorded = []

    def record(sender, template, context, **extra):
        recorded.append((template, context))

    template_rendered.connect(record, app)
    try:
        yield recorded
    finally:
        template_rendered.disconnect(record, app)


class TestStratAddViews(BaseTestFixture):
    def setUp(self) -> None:
        super().setUp()

        def mock_check_object_exists(prefix: str) -> bool:
            if (current_user.role.name == "DEVELOPER") and (
                ("cluster_perforamnce" in prefix)
                or (("test_strat_3" in prefix) and ("submitted_strats" in prefix))
            ):
                return True
            if current_user.role.name == "ADMIN":
                return True
            return False

        def mock_upload_to_minio(python_file, ipython_file, strategy_name: str):
            pass

        strat_add_views.upload_to_minio = mock_upload_to_minio
        strat_add_views.check_object_exists = mock_check_object_exists

    def test_add_strategy(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )

                    self.client.post(
                        "/strat_sub",
                        data={
                            "strategy_name": "test_strat_1",
                            "segment": "OPTIDX",
                            "exchange_name": "NSE",
                            "backtest_date": datetime.datetime.now().date(),
                            "overlap_days": 20,
                            "long_book_mapping": "LC1",
                            "short_book_mapping": "SC1",
                            "trigger_coeff": 100.0,
                            "limit_coeff": 156.0,
                            "expiration_time": "15:15",
                            "long_short": -1,
                            "cluster_mapping": ["cluster_options"],
                            "comments": "",
                            "old_strategy": "test_strat_4",
                            "is_rework": True,
                            "python_file": open("./tests/test_data/test.py", "rb"),
                            "ipynb_file": open("./tests/test_data/test.ipynb", "rb"),
                        },
                    )
                    template, context = templates[0]
                    self.assertEqual(
                        "strat_add/submission.html",
                        template.name,
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        context["form"].errors,
                        {
                            "strategy_name": [
                                "Live strategy already exists with this name!!"
                            ]
                        },
                        msg="multiple strategies of the same name uploaded!",
                    )
                    Strategy.query.filter_by(
                        strategy_name="test_strat_4"
                    ).first().strat_state = (
                        Status.query.filter_by(state_description="TEST").first().id
                    )
                    self.client.post(
                        "/strat_sub",
                        data={
                            "strategy_name": "test_strat_9",
                            "segment": "OPTIDX",
                            "exchange_name": "NSE",
                            "backtest_date": datetime.datetime.now().date(),
                            "overlap_days": 20,
                            "long_book_mapping": "LC1",
                            "short_book_mapping": "SC1",
                            "trigger_coeff": 100.0,
                            "limit_coeff": 156.0,
                            "expiration_time": "15:15",
                            "long_short": -1,
                            "cluster_mapping": ["cluster_options"],
                            "comments": "",
                            "old_strategy": "test_strat_4",
                            "is_rework": True,
                            "python_file": open("./tests/test_data/test.py", "rb"),
                            "ipynb_file": open("./tests/test_data/test.ipynb", "rb"),
                        },
                    )
                    template, context = templates[1]
                    self.assertEqual(
                        "strat_add/submission.html",
                        template.name,
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        context["form"].errors,
                        {
                            "old_strategy": [
                                "Original strategy must not be in test-only mode!!"
                            ]
                        },
                        msg="Rework accepted even when original strategy is in test-only mode!",
                    )
                    self.client.post(
                        "/strat_sub",
                        data={
                            "strategy_name": "test_strat_5",
                            "developer": "test_admin",
                            "segment": "OPTIDX",
                            "exchange_name": "NSE",
                            "backtest_date": datetime.datetime.now().date(),
                            "overlap_days": 30,
                            "trigger_coeff": 100,
                            "limit_coeff": 156,
                            "expiration_time": "15:00",
                            "long_short": -1,
                            "cluster_mapping": ["cluster_options"],
                            "comments": "",
                            "old_strategy": "test_non_existent",
                            "is_rework": False,
                            "python_file": open("./tests/test_data/test.py", "rb"),
                            "ipynb_file": open("./tests/test_data/test.ipynb", "rb"),
                        },
                    )
                    template, context = templates[2]
                    self.assertEqual(
                        "strat_add/submission.html",
                        template.name,
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        context["form"].errors,
                        {
                            "long_book_mapping": [
                                "This field or short_book_mapping is required."
                            ],
                            "short_book_mapping": [
                                "This field or long_book_mapping is required."
                            ],
                            "old_strategy": ["No strategy exists with this name!!"],
                        },
                        msg="form errors not captured!",
                    )
                    self.client.post(
                        "/strat_sub",
                        data={
                            "strategy_name": " test_strat_5 ",
                            "developer": "test_admin",
                            "segment": "OPTIDX",
                            "exchange_name": "NSE",
                            "backtest_date": datetime.datetime.now().date(),
                            "overlap_days": 20,
                            "long_book_mapping": "LC1",
                            "short_book_mapping": "SC1",
                            "trigger_coeff": 100,
                            "limit_coeff": 156,
                            "expiration_time": "15:00",
                            "long_short": -1,
                            "cluster_mapping": ["cluster_options"],
                            "comments": "",
                            "old_strategy": "",
                            "is_rework": False,
                            "python_file": open("./tests/test_data/test.py", "rb"),
                            "ipynb_file": open("./tests/test_data/test.ipynb", "rb"),
                        },
                    )
                    self.assertTrue(
                        Strategy.query.filter_by(strategy_name="test_strat_5").first()
                        is not None,
                        msg="strategy addition failed!",
                    )
                    self.assertTrue(
                        PendingBacktests.query.filter_by(
                            strategy_name="test_strat_5"
                        ).first()
                        is not None,
                        msg="strategy addition failed!",
                    )
                    strategy = Strategy(
                        strategy_name="master_cluster",
                        developer="test_manager",
                        segment="CASH",
                        exchange_name="NSE",
                        backtest_start_date=pd.Timestamp(2009, 1, 1),
                        overlap_days=20,
                        book_long="LC1",
                        book_short="SC2",
                        strat_state=2,
                        submission_day=datetime.datetime.now(),
                        trigger_coeff=500,
                        limit_coeff=300,
                        expiration_time=datetime.time(15, 15),
                        long_short="-1",
                        cluster_mapping=[],
                        comments=None,
                        reworked_strategy=None,
                    )
                    db.session.add(strategy)
                    db.session.commit()
                    self.client.get("/strat_sub")
                    template, context = templates[3]
                    self.assertEqual(
                        "strat_add/submission.html",
                        template.name,
                        msg=self.message_template,
                    )
                    self.client.post(
                        "/strat_sub",
                        data={
                            "strategy_name": " invalid_strat ",
                            "developer": "test_admin",
                            "segment": "OPTIDX",
                            "exchange_name": "NSE",
                            "backtest_date": datetime.datetime.now().date(),
                            "overlap_days": 30,
                            "long_book_mapping": "LC1",
                            "short_book_mapping": "SC1",
                            "trigger_coeff": 100,
                            "limit_coeff": 156,
                            "expiration_time": "15:00",
                            "long_short": -1,
                            "cluster_mapping": ["cluster_options"],
                            "comments": "",
                            "old_strategy": "",
                            "is_rework": False,
                            "python_file": open(
                                "./tests/test_data/invalid_strat.py", "rb"
                            ),
                            "ipynb_file": open("./tests/test_data/test.ipynb", "rb"),
                        },
                    )
                    template, context = templates[4]
                    self.assertEqual(
                        set(context["invalid_filter"]),
                        set(
                            [
                                "option_filter",
                                "stock_option_filter",
                                "optcom_filter",
                                "set_trace",
                                "Only KeyError Exception is allowed",
                                "finally",
                            ]
                        ),
                        msg=self.message_template,
                    )

                    self.client.post(
                        "/strat_sub",
                        data={
                            "strategy_name": "test_strat_multiple",
                            "segment": "OPTIDX",
                            "exchange_name": "NSE",
                            "backtest_date": datetime.datetime.now().date(),
                            "overlap_days": 20,
                            "long_book_mapping": "LC1",
                            "short_book_mapping": "SC1",
                            "trigger_coeff": 100.0,
                            "limit_coeff": 156.0,
                            "expiration_time": "15:15",
                            "long_short": -1,
                            "cluster_mapping": ["cluster_1", "cluster_2", "cluster_3"],
                            "comments": "",
                            "old_strategy": "",
                            "is_rework": False,
                            "python_file": open("./tests/test_data/test.py", "rb"),
                            "ipynb_file": open("./tests/test_data/test.ipynb", "rb"),
                        },
                    )
                    self.assertEqual(
                        [
                            strat.cluster_name
                            for strat in StrategyClusterMapping.query.filter_by(
                                strategy_name="test_strat_multiple",
                                mapping_status=ClusterMappingStatus.get_status(
                                    description="LIVE_ENV"
                                ),
                            ).all()
                        ],
                        ["cluster_1", "cluster_2", "cluster_3"],
                        msg="Clusters not mapped correctly!",
                    )
                    self.assertEqual(
                        [
                            strat.cluster_name
                            for strat in StrategyClusterMapping.query.filter_by(
                                strategy_name="test_strat_multiple",
                                mapping_status=ClusterMappingStatus.get_status(
                                    description="TEST_ENV"
                                ),
                            ).all()
                        ],
                        ["cluster_1", "cluster_2", "cluster_3"],
                        msg="Clusters not mapped correctly!",
                    )

                    self.client.post(
                        "/strat_sub",
                        data={
                            "strategy_name": " test_strat_6",
                            "developer": "test_admin",
                            "segment": "OPTIDX",
                            "exchange_name": "NSE",
                            "backtest_date": datetime.datetime.now().date(),
                            "overlap_days": 20,
                            "long_book_mapping": "LC1",
                            "short_book_mapping": "SC1",
                            "trigger_coeff": 100,
                            "limit_coeff": 156,
                            "expiration_time": "15:00",
                            "long_short": -1,
                            "cluster_mapping": ["cluster_options"],
                            "comments": "",
                            "old_strategy": "",
                            "is_rework": False,
                            "python_file": open("./tests/test_data/test.py", "rb"),
                            "ipynb_file": open("./tests/test_data/test.ipynb", "rb"),
                            "not_run_cluster_backtest": True,
                            "not_run_kivifolio": True,
                        },
                    )
                    pending_strat_obj = PendingBacktests.query.filter_by(
                        strategy_name="test_strat_6"
                    ).first()
                    self.assertTrue(
                        pending_strat_obj is not None,
                        msg="strategy addition failed!",
                    )
                    self.assertEqual(
                        pending_strat_obj.service_state,
                        "00033",
                        msg="Failed setting default no run service state!",
                    )

    def test_modify_strategy(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )

                    response = self.client.get("/strat_sub/modify/test_strat_5")
                    self.assertEqual(
                        response.status_code, 404, msg=self.message_response_code
                    )

                    response = self.client.get("/strat_sub/modify/test_strat_2")
                    self.assertEqual(
                        response.status_code, 403, msg=self.message_response_code
                    )
                    Strategy.query.filter_by(
                        strategy_name="test_strat_1"
                    ).first().reworked_strategy = "test_strat_reworked"
                    strategy = Strategy(
                        strategy_name="master_cluster",
                        developer="test_manager",
                        segment="CASH",
                        exchange_name="NSE",
                        backtest_start_date=pd.Timestamp(2009, 1, 1),
                        overlap_days=30,
                        book_long="LC1",
                        book_short="SC2",
                        strat_state=2,
                        submission_day=datetime.datetime.now(),
                        trigger_coeff=500,
                        limit_coeff=300,
                        expiration_time=datetime.time(15, 0),
                        long_short="-1",
                        comments=None,
                        reworked_strategy=None,
                    )
                    db.session.add(strategy)
                    db.session.commit()
                    response = self.client.get("/strat_sub/modify/test_strat_1")
                    template, context = templates[0]
                    self.assertEqual(
                        template.name,
                        "strat_add/submission.html",
                        msg=self.message_template,
                    )
                    message_data_not_matching = (
                        "{} data not matching in modification form!"
                    )
                    response = self.client.get("/strat_sub/modify/test_strat_4")
                    template, context = templates[1]
                    self.assertEqual(
                        context["form"].strategy_name.data,
                        "test_strat_4",
                        msg=message_data_not_matching.format("strategy"),
                    )
                    self.assertEqual(
                        context["form"].exchange_name.data,
                        "NSE",
                        msg=message_data_not_matching.format("exchnage name"),
                    )
                    self.assertEqual(
                        context["form"].trigger_coeff.data,
                        100,
                        msg=message_data_not_matching.format("trigger_coeff"),
                    )
                    self.assertEqual(
                        context["form"].limit_coeff.data,
                        156,
                        msg=message_data_not_matching.format("limit_coeff"),
                    )
                    self.assertEqual(
                        context["form"].expiration_time.data,
                        datetime.time(13, 20),
                        msg=message_data_not_matching.format("expiration_time"),
                    )
                    self.assertEqual(
                        context["form"].long_short.data,
                        -1,
                        msg=message_data_not_matching.format("long_short"),
                    )
                    self.assertEqual(
                        context["form"].long_book_mapping.data,
                        "LC0",
                        msg=message_data_not_matching.format("book_long"),
                    )
                    self.assertEqual(
                        context["form"].short_book_mapping.data,
                        "SC1",
                        msg=message_data_not_matching.format("book_short"),
                    )
                    self.assertEqual(
                        context["form"].cluster_mapping.data,
                        ["cluster_options"],
                        msg=message_data_not_matching.format("cluster_mapping"),
                    )
                    self.assertEqual(
                        context["form"].comments.data,
                        None,
                        msg=message_data_not_matching.format("comments"),
                    )
                    self.assertEqual(
                        context["form"].old_strategy.data,
                        None,
                        msg=message_data_not_matching.format("old_strategy"),
                    )

                    message_not_modified_properly = "{} data not modified!"
                    response = self.client.post(
                        "/strat_sub/modify/test_strat_4",
                        data={
                            "strategy_name": "test_strat_4",
                            "segment": "FUTSTK",
                            "exchange_name": "MCX",
                            "backtest_date": datetime.datetime.now().date(),
                            "long_book_mapping": "None",
                            "short_book_mapping": "SC1",
                            "trigger_coeff": 10.0,
                            "limit_coeff": 200.0,
                            "expiration_time": "15:00",
                            "long_short": 1,
                            "cluster_mapping": ["cluster_test"],
                            "comments": "This is a modified strategy!",
                            "old_strategy": "test_strat_2",
                            "is_rework": True,
                            "python_file": open("./tests/test_data/test.py", "rb"),
                            "ipynb_file": open("./tests/test_data/test.ipynb", "rb"),
                            "not_run_cluster_backtest": True,
                            "not_run_kivifolio": True,
                        },
                    )
                    strategy = Strategy.query.filter_by(
                        strategy_name="test_strat_4"
                    ).first()
                    self.assertEqual(
                        strategy.strategy_name,
                        "test_strat_4",
                        msg="strategy name not correct!",
                    )
                    self.assertEqual(
                        strategy.exchange_name,
                        "MCX",
                        msg=message_not_modified_properly.format("exchange_name"),
                    )
                    self.assertEqual(
                        strategy.developer,
                        User.query.filter_by(username="test_admin").first().username,
                        msg=message_not_modified_properly.format("developer"),
                    )
                    self.assertEqual(
                        strategy.segment,
                        "FUTSTK",
                        msg=message_not_modified_properly.format("segment"),
                    )
                    self.assertEqual(
                        strategy.backtest_start_date,
                        datetime.datetime.now().date(),
                        msg=message_not_modified_properly.format("backtest_start_date"),
                    )
                    self.assertEqual(
                        strategy.book_long,
                        None,
                        msg=message_not_modified_properly.format("book_long"),
                    )
                    self.assertEqual(
                        strategy.book_short,
                        "SC1",
                        msg=message_not_modified_properly.format("book_short"),
                    )
                    self.assertEqual(
                        strategy.strat_state,
                        1,
                        msg=message_not_modified_properly.format("strat_state"),
                    )
                    self.assertEqual(
                        strategy.trigger_coeff,
                        10.0,
                        msg=message_not_modified_properly.format("trigger_coeff"),
                    )
                    self.assertEqual(
                        strategy.limit_coeff,
                        200.0,
                        msg=message_not_modified_properly.format("limit_coeff"),
                    )
                    self.assertEqual(
                        strategy.expiration_time,
                        datetime.time(15, 0),
                        msg=message_data_not_matching.format("expiration_time"),
                    )
                    self.assertEqual(
                        strategy.long_short,
                        1,
                        msg=message_not_modified_properly.format("long_short"),
                    )
                    self.assertEqual(
                        [
                            cluster.cluster_name
                            for cluster in strategy.cluster_mapping
                            if cluster.mapping_status
                            == ClusterMappingStatus.get_status(description="LIVE_ENV")
                        ],
                        ["cluster_test"],
                        msg=message_not_modified_properly.format("cluster_mapping"),
                    )
                    self.assertEqual(
                        [
                            cluster.cluster_name
                            for cluster in strategy.cluster_mapping
                            if cluster.mapping_status
                            == ClusterMappingStatus.get_status(description="TEST_ENV")
                        ],
                        ["cluster_test"],
                        msg=message_not_modified_properly.format("cluster_mapping"),
                    )
                    self.assertEqual(
                        strategy.comments,
                        "This is a modified strategy!",
                        msg=message_not_modified_properly.format("comments"),
                    )
                    self.assertEqual(
                        strategy.reworked_strategy,
                        "test_strat_2",
                        msg=message_not_modified_properly.format("reworked_strategy"),
                    )
                    pending_strat_obj = PendingBacktests.query.filter_by(
                        strategy_name="test_strat_4"
                    ).first()
                    self.assertEqual(
                        pending_strat_obj.service_state,
                        "00033",
                        msg="Failed modifying default no run service state!",
                    )
                    response = self.client.post(
                        "/strat_sub/modify/test_strat_4",
                        data={
                            "strategy_name": "test_strat_4",
                            "segment": "FUTSTK",
                            "exchange_name": "MCX",
                            "backtest_date": datetime.datetime.now().date(),
                            "long_book_mapping": "None",
                            "short_book_mapping": "SC1",
                            "trigger_coeff": 10.0,
                            "limit_coeff": 200.0,
                            "expiration_time": "15:00",
                            "long_short": 1,
                            "cluster_mapping": [],
                            "comments": "This is a modified strategy!",
                            "old_strategy": "test_strat_2",
                            "is_rework": True,
                            "python_file": open("./tests/test_data/test.py", "rb"),
                            "ipynb_file": open("./tests/test_data/test.ipynb", "rb"),
                        },
                    )
                    strategy = Strategy.query.filter_by(
                        strategy_name="test_strat_4"
                    ).first()
                    self.assertEqual(
                        [cluster.cluster_name for cluster in strategy.cluster_mapping],
                        [],
                        msg="cluster mapping not changed properly!",
                    )

    def test_delete_strategy(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                from app.strat_add import strat_add_views

                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )

                response = self.client.post("/strat_sub/test_strat_5/delete")
                self.assertEqual(
                    response.status_code, 404, msg=self.message_response_code
                )

                response = self.client.post("/strat_sub/test_strat_2/delete")
                self.assertEqual(
                    response.status_code, 302, msg=self.message_response_code
                )
                self.assertEqual(
                    Strategy.query.filter_by(strategy_name="test_strat_2").first(),
                    None,
                    msg="strategy not deleted properly!",
                )
                response = self.client.post("/strat_sub/test_strat_1/delete")
                self.assertEqual(
                    response.status_code, 302, msg=self.message_response_code
                )
                self.assertEqual(
                    Strategy.query.filter_by(strategy_name="test_strat_1").first(),
                    None,
                    msg="strategy not deleted properly!",
                )
                self.assertEqual(
                    StrategyClusterMapping.query.filter_by(
                        strategy_name="test_strat_1",
                        mapping_status=ClusterMappingStatus.get_status(
                            description="LIVE_ENV"
                        ),
                    ).all(),
                    [],
                    msg="strategy cluster mapping not deleted properly!",
                )
                message = "strategy not deleted properly!"
                self.assertTrue(
                    Strategy.query.filter_by(strategy_name="test_strat_1").first()
                    is None,
                    msg=message,
                )
                self.assertTrue(
                    PendingBacktests.query.filter_by(
                        strategy_name="test_strat_1"
                    ).first()
                    is None,
                    msg=message,
                )

    def test_control_modification_for_strategy(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                strat = PendingBacktests.query.filter_by(
                    strategy_name="test_strat_2"
                ).first()
                # initial state is 1 by default i.e. modification is allowed
                allow_modification = strat.allow_modification

                # Logged in as developer
                response = self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                response = self.client.post(
                    "/control_modification", data={"strategy": "test_strat_2"}
                )
                # Developer trying to access the control modification option -> Failed
                self.assertEqual(
                    response.status_code, 403, msg=self.message_response_code
                )
                response = self.client.get("/sign_out")
                # Logged in as manager
                response = self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )
                response = self.client.post(
                    "/control_modification", data={"strategy": "test_strat_2"}
                )
                # state is toggled
                self.assertEqual(strat.allow_modification, not allow_modification)
                #  Request to delete the strategy
                response = self.client.post("/strat_sub/test_strat_2/delete")
                # Strategy is not deleted
                self.assertTrue(
                    Strategy.query.filter_by(strategy_name="test_strat_2").first()
                    is not None
                )

                # Initial value for limit coeff is 300
                self.assertEqual(
                    Strategy.query.filter_by(strategy_name="test_strat_2")
                    .first()
                    .limit_coeff,
                    300,
                )
                response = self.client.post(
                    "/strat_sub/modify/test_strat_2",
                    data={
                        "strategy_name": "test_strat_2",
                        "segment": "FUTSTK",
                        "exchange_name": "MCX",
                        "backtest_date": datetime.datetime.now().date(),
                        "long_book_mapping": "None",
                        "short_book_mapping": "SC1",
                        "trigger_coeff": 10.0,
                        "limit_coeff": 400.0,
                        "expiration_time": "15:00",
                        "long_short": 1,
                        "cluster_mapping": [],
                        "old_strategy": "test_strat_2",
                        "comments": "This is a modified strategy!",
                        "python_file": open("./tests/test_data/test.py", "rb"),
                        "ipynb_file": open("./tests/test_data/test.ipynb", "rb"),
                    },
                )
                # Coeff is still 300, it is not changed to 400
                self.assertEqual(
                    Strategy.query.filter_by(strategy_name="test_strat_2")
                    .first()
                    .limit_coeff,
                    300,
                )

    def test_review_strategy_dashboard(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    from app.strat_add import strat_add_views

                    def mock_get_correlation_pending_strats(
                        strategy_info: list,
                    ) -> tuple:
                        data = {
                            "test_strat_1": {
                                "test_strat_1": [np.nan, "pending"],
                                "test_strat_2": [0.12, "pending"],
                                "test_strat_3": [0.23, "pending"],
                                "test_strat_4": [0.44, "pending"],
                            },
                            "test_strat_2": {
                                "test_strat_1": [0.12, "pending"],
                                "test_strat_2": [np.nan, "pending"],
                                "test_strat_3": [0.15, "pending"],
                                "test_strat_4": [0.56, "pending"],
                            },
                            "test_strat_3": {
                                "test_strat_1": [0.23, "pending"],
                                "test_strat_2": [0.15, "pending"],
                                "test_strat_3": [np.nan, "pending"],
                                "test_strat_4": [0.35, "pending"],
                            },
                            "test_strat_4": {
                                "test_strat_1": [0.44, "pending"],
                                "test_strat_2": [0.56, "pending"],
                                "test_strat_3": [0.35, "pending"],
                                "test_strat_4": [np.nan, "pending"],
                            },
                        }
                        df = pd.DataFrame(data)
                        error_strats = []
                        for strat in data.keys():
                            if strat not in [info[0] for info in strategy_info]:
                                df = df.drop(columns=[strat])
                                df = df.drop(strat)
                                error_strats.append(strat)
                        if current_user.role.name == "MANAGER":
                            df = df.drop(columns=["test_strat_3"])
                            df = df.drop("test_strat_3")
                        return df, error_strats

                    strat_add_views.get_correlation_pending_strats = (
                        mock_get_correlation_pending_strats
                    )
                    db.session.commit()
                    message_strats_incorrect = "pending strats incorrect for {}!"
                    message_backtest_results = "backtest results incorrect for {}!"

                    # Logged in as developer
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_developer, "password": self.password},
                    )

                    response = self.client.get("/strat_review")
                    template, context = templates[0]
                    self.assertEqual([], context["submitted_strategies"])
                    strat_access = StrategyAccess(
                        strategy_name="test_strat_2", username="test_developer"
                    )
                    db.session.add(strat_access)
                    db.session.commit()
                    response = self.client.get("/strat_review")
                    template, context = templates[1]
                    self.assertEqual(
                        [
                            Strategy.query.filter_by(
                                strategy_name="test_strat_2"
                            ).first()
                        ],
                        context["submitted_strategies"],
                    )
                    review_list = json.dumps(["test_strat_1"])
                    response = self.client.post(
                        "/strat_review", data={"strategies": review_list}
                    )
                    self.assertEqual(
                        "/home",
                        response.location,
                        msg="developer got access to review result page for an admin strategy!",
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as manager
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_manager, "password": self.password},
                    )
                    response = self.client.get("/strat_review")
                    template, context = templates[2]
                    self.assertEqual(
                        template.name,
                        "strat_add/review_strategy_dashboard.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        set(
                            [
                                strategy.strategy_name
                                for strategy in context["submitted_strategies"]
                            ]
                        ),
                        set(
                            [
                                strategy.strategy_name
                                for strategy in Strategy.query.filter(
                                    Strategy.strategy_name.in_(
                                        ["test_strat_2", "test_strat_3"]
                                    )
                                ).all()
                            ]
                        ),
                        msg=message_strats_incorrect.format("manager"),
                    )
                    self.assertEqual(
                        context["backtest_results"],
                        {"test_strat_2": "DONE", "test_strat_3": "ERROR"},
                        msg=message_backtest_results.format("manager"),
                    )
                    self.assertEqual(
                        context["service_state"],
                        {"test_strat_2": "22222", "test_strat_3": "10000"},
                    )

                    review_list = json.dumps(["test_strat_1"])
                    response = self.client.post(
                        "/strat_review", data={"strategies": review_list}
                    )
                    self.assertEqual(
                        "/home",
                        response.location,
                        msg="manager got access to admin strat result page!",
                    )
                    review_list = json.dumps(["test_strat_2", "test_strat_3"])
                    response = self.client.post(
                        "/strat_review", data={"strategies": review_list}
                    )
                    self.assertEqual(
                        response.data, b"success", msg="manager denied access!"
                    )
                    review_list = json.dumps(["test_strat_3"])
                    response = self.client.post(
                        "/strat_review", data={"strategies": review_list}
                    )
                    self.assertEqual(
                        response.data,
                        b"results_not_available",
                        msg="manager denied access!",
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as admin
                    PendingBacktests.query.filter_by(
                        strategy_name="test_strat_1"
                    ).first().backtest_result = "DONE"
                    db.session.commit()
                    Strategy.query.filter_by(
                        strategy_name="test_strat_4"
                    ).first().strat_state = 5
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )
                    response = self.client.get("/strat_review")
                    template, context = templates[3]
                    self.assertEqual(
                        template.name,
                        "strat_add/review_strategy_dashboard.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        set(
                            [
                                strategy.strategy_name
                                for strategy in context["submitted_strategies"]
                            ]
                        ),
                        set(
                            [
                                strategy.strategy_name
                                for strategy in Status.query.filter_by(
                                    state_description="PENDING"
                                )
                                .first()
                                .strategies.all()
                            ]
                        ),
                        msg=message_strats_incorrect.format("admin"),
                    )
                    self.assertEqual(
                        context["backtest_results"],
                        {
                            "test_strat_1": "Pending",
                            "test_strat_2": "DONE",
                            "test_strat_3": "ERROR",
                        },
                        msg=message_backtest_results.format("admin"),
                    )
                    self.assertEqual(
                        context["service_state"],
                        {
                            "test_strat_1": "00000",
                            "test_strat_2": "22222",
                            "test_strat_3": "10000",
                        },
                    )
                    review_list = json.dumps(
                        ["test_strat_1", "test_strat_2", "test_strat_3"]
                    )
                    response = self.client.post(
                        "/strat_review", data={"strategies": review_list}
                    )
                    self.assertEqual(
                        response.data, b"success", msg="admin denied access!"
                    )

    def test_expand_strategy(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    message_context = "incorrect context variable passed!"

                    def mock_get_charts_from_minio(strategy_name: str, status: str):
                        if strategy_name in ["test_strat_1"]:
                            return {
                                "dd": "test_1",
                                "monte": "test_2",
                                "ret": "test_3",
                                "kde": "test_4",
                            }
                        else:
                            return {"dd": None, "monte": None, "ret": None, "kde": None}

                    strat_add_views.get_charts_from_minio = mock_get_charts_from_minio

                    # Logged in as developer
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_developer, "password": self.password},
                    )
                    response = self.client.get("/test_strat_1/expand")
                    self.assertEqual(
                        response.status_code, 403, msg=self.message_response_code
                    )

                    response = self.client.get("/test_strat_3/expand")
                    template, context = templates[0]
                    self.assertEqual(
                        template.name,
                        "strat_add/expand_strat.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        context["strategy"],
                        Strategy.query.filter_by(strategy_name="test_strat_3").first(),
                        msg=message_context,
                    )
                    self.assertEqual(
                        context["backtest_result"], "ERROR", msg=message_context
                    )
                    self.assertEqual(
                        context["retrieval_url"],
                        {"dd": None, "monte": None, "ret": None, "kde": None},
                        msg=message_context,
                    )
                    self.assertEqual(
                        context["title"],
                        {
                            "dd": "Cumulative Distribution of DD (%)",
                            "monte": "Monte Carlo Simulation",
                            "ret": "Scatter Plot of BackTest Returns",
                            "kde": "KDE plot of insample and outsample returns",
                        },
                        msg=message_context,
                    )
                    self.assertEqual(
                        context["meta_data"],
                        StrategyMetaData.query.filter_by(
                            strategy_name="test_strat_3"
                        ).first(),
                        msg=message_context,
                    )
                    self.assertIn("available_dates_perf", context, message_context)
                    self.assertIn("available_dates_var", context, message_context)

                    response = self.client.get("/sign_out")

                    # Logged in as admin
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )
                    response = self.client.get("/test_strat_1/expand")
                    template, context = templates[1]
                    self.assertEqual(
                        template.name,
                        "strat_add/expand_strat.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        context["strategy"],
                        Strategy.query.filter_by(strategy_name="test_strat_1").first(),
                        msg=message_context,
                    )
                    self.assertEqual(
                        context["backtest_result"], "Pending", msg=message_context
                    )
                    self.assertEqual(
                        context["retrieval_url"],
                        {
                            "dd": "test_1",
                            "monte": "test_2",
                            "ret": "test_3",
                            "kde": "test_4",
                        },
                        msg=message_context,
                    )
                    self.assertEqual(
                        context["title"],
                        {
                            "dd": "Cumulative Distribution of DD (%)",
                            "monte": "Monte Carlo Simulation",
                            "ret": "Scatter Plot of BackTest Returns",
                            "kde": "KDE plot of insample and outsample returns",
                        },
                        msg=message_context,
                    )
                    self.assertEqual(
                        context["meta_data"],
                        StrategyMetaData.query.filter_by(
                            strategy_name="test_strat_1"
                        ).first(),
                        msg=message_context,
                    )
                    self.assertEqual(
                        context["user_list"],
                        [],
                        msg="existing user list not fetched properly",
                    )
                    self.assertEqual(
                        set(context["users"]),
                        set(["test_developer", "test_manager"]),
                        msg="new possible user list not fetched properly!",
                    )
                    response = self.client.get("/test_non_existent/expand")
                    self.assertEqual(
                        response.status_code, 404, msg=self.message_response_code
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as manager
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_manager, "password": self.password},
                    )
                    response = self.client.get("/test_strat_3/expand")
                    template, context = templates[2]
                    self.assertEqual(
                        context["users"],
                        ["test_developer"],
                        msg="new possible user list not fetched properly!",
                    )

    def test_add_user_access(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                response = self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                response = self.client.post(
                    "/strat_expand/add_user",
                    data={
                        "strategy_name": "test_strat_1",
                        "username": "test_developer",
                    },
                )
                self.assertEqual(
                    response.status_code, 403, msg=self.message_response_code
                )
                response = self.client.get("/sign_out")

                # Logged in as admin
                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                response = self.client.post(
                    "/strat_expand/add_user",
                    data={
                        "strategy_name": "test_not_existant",
                        "username": "test_developer",
                    },
                )
                self.assertEqual(
                    response.status_code, 404, msg=self.message_response_code
                )
                with self.assertRaises(Exception):
                    response = self.client.post(
                        "/strat_expand/add_user",
                        data={
                            "strategy_name": "test_strat_1",
                            "username": "test_non_existant",
                        },
                    )
                    self.assertEqual(
                        response.status_code, 500, msg=self.message_response_code
                    )
                response = self.client.post(
                    "/strat_expand/add_user",
                    data={
                        "strategy_name": "test_strat_1",
                        "username": "test_developer",
                    },
                )
                self.assertTrue(
                    "test_developer"
                    in [
                        user.username
                        for user in StrategyAccess.query.filter_by(
                            strategy_name="test_strat_1"
                        ).all()
                    ],
                    msg="strategy access not provided properly!",
                )

    def test_revoke_user_access(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                response = self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                response = self.client.post(
                    "strat_expand/remove_user",
                    data={
                        "strategy_name": "test_strat_1",
                        "username": "test_developer",
                    },
                )
                self.assertEqual(
                    response.status_code, 403, msg=self.message_response_code
                )
                response = self.client.get("/sign_out")

                # Logged in as admin
                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                response = self.client.post(
                    "strat_expand/remove_user",
                    data={
                        "strategy_name": "test_not_existant",
                        "username": "test_developer",
                    },
                )
                self.assertEqual(
                    response.status_code, 404, msg=self.message_response_code
                )
                with self.assertLogs(current_app.logger, level="ERROR") as out:
                    response = self.client.post(
                        "strat_expand/remove_user",
                        data={
                            "strategy_name": "test_strat_1",
                            "username": "test_developer",
                        },
                    )
                    self.assertEqual(
                        out.output,
                        [
                            "ERROR:app:Strat access for strategy test_strat_1 for test_developer not found"
                        ],
                        msg="error message not displayed correctly!",
                    )
                    self.assertEqual(
                        response.status_code, 200, msg=self.message_response_code
                    )
                    self.assertEqual(
                        response.text, "failed", msg="response not correct!"
                    )
                response = self.client.post(
                    "/strat_expand/add_user",
                    data={
                        "strategy_name": "test_strat_1",
                        "username": "test_developer",
                    },
                )
                response = self.client.post(
                    "strat_expand/remove_user",
                    data={
                        "strategy_name": "test_strat_1",
                        "username": "test_developer",
                    },
                )
                self.assertTrue(
                    "test_developer"
                    not in [
                        user.username
                        for user in StrategyAccess.query.filter_by(
                            strategy_name="test_strat_1"
                        ).all()
                    ],
                    msg="strategy access not provided properly!",
                )

    def test_download_strategy(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                private_key_file = open("tests/test_data/private_key.pem", "rb")
                key_to_decrypt, isValid = validate_download_request(
                    private_key_file=private_key_file,
                    strategy_name="test_strat_1",
                    status="PENDING",
                )
                private_key_file.close()

                from app.strat_add import strat_add_views

                def mock_valdidate_download_request(
                    private_key_file, strategy_name: str, status="PENDING"
                ):
                    return key_to_decrypt, True

                strat_add_views.validate_download_request = (
                    mock_valdidate_download_request
                )
                message_downloading = "{} downloading {} failed!"
                # Logged in as developer
                response = self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                response = self.client.post(
                    "/strat_download",
                    data={
                        "file": open("./tests/test_data/private_key.pem", "rb"),
                        "strategy_list": '["test_strat_1"]',
                        "status": "PENDING",
                    },
                )
                self.assertEqual(
                    response.status_code, 400, msg=self.message_response_code
                )
                self.assertEqual(
                    response.data,
                    b"failed",
                    msg="developer downloading admin strat failed!",
                )
                response = self.client.get("/sign_out")

                # Logged in as manager
                response = self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )
                response = self.client.post(
                    "/strat_download",
                    data={
                        "file": open("./tests/test_data/private_key.pem", "rb"),
                        "strategy_list": '["test_strat_1"]',
                        "status": "PENDING",
                    },
                )
                self.assertEqual(
                    response.status_code, 400, msg=self.message_response_code
                )
                self.assertEqual(
                    response.data,
                    b"failed",
                    msg=message_downloading.format("manager", "admin"),
                )

                response = self.client.post(
                    "/strat_download",
                    data={
                        "file": open("./tests/test_data/private_key.pem", "rb"),
                        "strategy_list": '["test_strat_2", "test_strat_3"]',
                        "status": "PENDING",
                    },
                )
                self.assertEqual(
                    response.status_code, 200, msg=self.message_response_code
                )
                self.assertIn(
                    b"test_strat_2.py",
                    response.data,
                    msg=message_downloading.format("manager", "manager"),
                )
                self.assertIn(
                    b"test_strat_3.py",
                    response.data,
                    msg=message_downloading.format("manager", "manager"),
                )
                self.assertIn(
                    b"test_strat_2.ipynb",
                    response.data,
                    msg=message_downloading.format("manager", "manager"),
                )
                self.assertIn(
                    b"test_strat_3.ipynb",
                    response.data,
                    msg=message_downloading.format("manager", "manager"),
                )
                self.assertIn(
                    b"test_strat_2_MTM.log",
                    response.data,
                    msg=message_downloading.format("manager", "manager"),
                )

                response = self.client.get("/sign_out")

                # Logged in as admin
                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                response = self.client.post(
                    "/strat_download",
                    data={
                        "file": open("./tests/test_data/private_key.pem", "rb"),
                        "strategy_list": '["test_strat_1"]',
                        "status": "PENDING",
                    },
                )
                self.assertEqual(
                    response.status_code, 200, msg=self.message_response_code
                )
                self.assertIn(
                    b"test_strat_1.py",
                    response.data,
                    msg=message_downloading.format("admin", "admin"),
                )
                self.assertIn(
                    b"test_strat_1.ipynb",
                    response.data,
                    msg=message_downloading.format("admin", "admin"),
                )
                self.assertIn(
                    b"test_strat_1_MTM.log",
                    response.data,
                    msg=message_downloading.format("admin", "admin"),
                )

                self.assertIn(
                    b"test_strat_1_Tradelog.parquet",
                    response.data,
                    msg=message_downloading.format("admin", "admin"),
                )

                def mock_get_files_from_minio_failure(
                    status: str,
                    strategy_name: str,
                    post_fix: str,
                    cluster_name: str = "",
                ):
                    raise Exception("File not found!")

                strat_add_views.get_files_from_minio = mock_get_files_from_minio_failure
                response = self.client.post(
                    "/strat_download",
                    data={
                        "file": open("./tests/test_data/private_key.pem", "rb"),
                        "strategy_list": '["test_strat_1"]',
                        "status": "PENDING",
                    },
                )
                self.assertEqual(
                    response.status_code, 200, msg=self.message_response_code
                )
                self.assertNotIn(
                    b"test_strat_1.py",
                    response.data,
                    msg=message_downloading.format("admin", "admin"),
                )

                def mock_valdidate_download_request_failure(
                    private_key_file, strategy_name: str, status="PENDING"
                ):
                    raise Exception("Could not validate!")

                strat_add_views.validate_download_request = (
                    mock_valdidate_download_request_failure
                )
                response = self.client.post(
                    "/strat_download",
                    data={
                        "file": open("./tests/test_data/private_key.pem", "rb"),
                        "strategy_list": '["test_strat_1"]',
                        "status": "PENDING",
                    },
                )
                self.assertEqual(
                    response.status_code, 400, msg=self.message_response_code
                )
                self.assertEqual(
                    response.data,
                    b"failed",
                    msg="validation failed but file was still downloaded!",
                )

    def test_download_custom_cluster_backtest(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():

                from app.strat_add import strat_add_views

                def mock_get_file_data_from_minio(path: str):
                    if "csv" in path:
                        file = open(
                            "./tests/test_data/cluster_backtest.csv",
                            "rb",
                        )
                    else:
                        file = open(
                            "./tests/test_data/cluster_test_0_MTM.log",
                            "rb",
                        )
                    data = file.read()
                    file.close()
                    return data

                strat_add_views.get_file_data_from_minio = mock_get_file_data_from_minio

                def mock_check_object_exists(prefix: str):
                    return True

                strat_add_views.check_object_exists = mock_check_object_exists
                cluster1 = Strategy(
                    strategy_name="cluster_restart",
                    developer="test_admin",
                    segment="CASH",
                    exchange_name="NSE",
                    backtest_start_date=pd.Timestamp(2009, 1, 1),
                    book_long="LC1",
                    book_short="SC2",
                    strat_state=2,
                    submission_day=datetime.datetime.now(),
                    trigger_coeff=500,
                    limit_coeff=300,
                    expiration_time=datetime.time(15, 15),
                    long_short="-1",
                    cluster_mapping=[],
                    comments=None,
                    reworked_strategy=None,
                )
                cluster2 = Strategy(
                    strategy_name="cluster_test",
                    developer="test_manager",
                    segment="CASH",
                    exchange_name="NSE",
                    backtest_start_date=pd.Timestamp(2009, 1, 1),
                    book_long="LC1",
                    book_short="SC2",
                    strat_state=2,
                    submission_day=datetime.datetime.now(),
                    trigger_coeff=500,
                    limit_coeff=300,
                    expiration_time=datetime.time(15, 15),
                    long_short="-1",
                    cluster_mapping=[],
                    comments=None,
                    reworked_strategy=None,
                )
                db.session.add(cluster1)
                db.session.add(cluster2)
                db.session.commit()
                # Logged in as developer
                response = self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                response = self.client.post(
                    "/download_custom_cluster_backtest",
                    data={
                        "cluster": "cluster_test",
                        "slaves": "slave1:slave2",
                    },
                )
                # developer trying to download manager's strategy
                self.assertEqual(
                    response.status_code, 403, msg=self.message_response_code
                )
                response = self.client.get("/sign_out")

                # Logged in as manager
                response = self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )
                response = self.client.post(
                    "/download_custom_cluster_backtest",
                    data={
                        "cluster": "cluster_test",
                        "slaves": "slave1:slave2",
                    },
                )
                self.assertEqual(response.status_code, 200)

                self.assertIn(b"cluster_test_0_MTM.log", response.data)

                response = self.client.post(
                    "/download_custom_cluster_backtest",
                    data={
                        "cluster": "cluster_restart",
                        "slaves": "slave1:slave2",
                    },
                )
                self.assertEqual(response.status_code, 403)
                response = self.client.get("/sign_out")
                # Logged in as admin
                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                response = self.client.post(
                    "/download_custom_cluster_backtest",
                    data={
                        "cluster": "cluster_restart",
                        "slaves": "slave1:slave2",
                    },
                )
                self.assertEqual(response.status_code, 200)
                # Slave combination non existent
                with self.assertRaises(ValueError) as context:
                    self.client.post(
                        "/download_custom_cluster_backtest",
                        data={
                            "cluster": "cluster_restart",
                            "slaves": "slave3:slave2",
                        },
                    )
                    self.assertEqual(str(context.exception), "Invalid Cluster")

    def test_performance_metrics(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    # Logged in as developer
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_developer, "password": self.password},
                    )
                    response = self.client.get(
                        "/strat_expand/performance/test_strat_3/current"
                    )
                    self.assertEqual(
                        response.status_code, 403, msg=self.message_response_code
                    )
                    Strategy.query.filter_by(
                        strategy_name="test_strat_3"
                    ).first().strat_state = 2
                    db.session.commit()
                    message = "performance file does not match for {}!"
                    response = self.client.get(
                        "/strat_expand/performance/test_strat_3/current"
                    )
                    template, context = templates[0]
                    self.assertTrue(
                        "performance" in context, msg=message.format("developer")
                    )
                    self.assertTrue(
                        "removal_report" in context, msg=message.format("developer")
                    )
                    self.assertTrue(
                        template.name == "strat_add/performance_metric.html",
                        msg=self.message_template,
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as manager
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_manager, "password": self.password},
                    )
                    response = self.client.get(
                        "/strat_expand/performance/test_strat_2/current"
                    )
                    template, context = templates[1]
                    self.assertTrue(
                        "performance" in context, msg=message.format("manager")
                    )
                    self.assertTrue(
                        template.name == "strat_add/performance_metric.html",
                        msg=self.message_template,
                    )

                    response = self.client.get(
                        "/strat_expand/performance/test_strat_1/current"
                    )
                    self.assertEqual(
                        response.status_code, 403, msg=self.message_response_code
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as manager
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )
                    response = self.client.get(
                        "/strat_expand/performance/test_strat_1/current"
                    )
                    template, context = templates[2]
                    self.assertTrue(
                        "performance" in context, msg=message.format("admin")
                    )
                    self.assertTrue(
                        template.name == "strat_add/performance_metric.html",
                        msg=self.message_template,
                    )
                    response = self.client.get(
                        "/strat_expand/performance/test_non_existent/current"
                    )
                    self.assertEqual(
                        response.status_code, 404, msg=self.message_response_code
                    )
                    with self.assertRaises(Exception) as context:
                        self.client.get(
                            "/strat_expand/performance/test_strat_1/2023_10_20"
                        )
                        self.assertEqual(str(context.exception), "File not found!")
                    response = self.client.get(
                        "/strat_expand/performance/test_strat_1/10-Oct-2023"
                    )
                    template, context = templates[3]
                    self.assertTrue("performance" in context)
                    self.assertTrue(
                        template.name == "strat_add/performance_metric.html"
                    )
                    self.assertEqual(response.status_code, 200)
                    response = self.client.get("/sign_out")

    def test_get_current_clusters(self) -> None:
        with self.app.app_context():
            # Logged in as developer
            response = self.client.post(
                "/login",
                data={"email": self.email_developer, "password": self.password},
            )
            response = self.client.post(
                "/get_current_clusters",
                data={"selected_strategy": "test_strat_2", "mapping_state": "LIVE_ENV"},
            )
            self.assertEqual(response.status_code, 403)
            response = self.client.get("/sign_out")
            # Logged in as manager
            response = self.client.post(
                "/login",
                data={"email": self.email_manager, "password": self.password},
            )
            # Access by owner of strategy for both test and live mode mapping
            strat = Strategy.query.filter_by(strategy_name="test_strat_2").first()
            strat.cluster_mapping.append(
                StrategyClusterMapping(
                    cluster_name="new_test_cluster",
                    strategy_name="test_strat_2",
                    mapping_status=ClusterMappingStatus.get_status(
                        description="TEST_ENV"
                    ),
                )
            )
            strat.strat_state = 2
            response = self.client.post(
                "/get_current_clusters",
                data={"selected_strategy": "test_strat_2", "mapping_state": "TEST_ENV"},
            )
            self.assertEqual(response.status_code, 200)
            self.assertEqual(
                b'[\n  "cluster_test",\n  "new_test_cluster"\n]\n', response.data
            )
            response = self.client.post(
                "/get_current_clusters",
                data={"selected_strategy": "test_strat_2", "mapping_state": "LIVE_ENV"},
            )
            self.assertEqual(response.status_code, 200)
            self.assertEqual(b'[\n  "cluster_test"\n]\n', response.data)

            # Accessing developer's startegy by manager
            Strategy.query.filter_by(
                strategy_name="test_strat_3"
            ).first().strat_state = 2
            response = self.client.post(
                "/get_current_clusters",
                data={"selected_strategy": "test_strat_3", "mapping_state": "LIVE_ENV"},
            )
            self.assertEqual(response.status_code, 200)

    def test_portfolio_performance_metrics(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    # Populating manager deadsheet to check for exchange wise seperation
                    # and creating a mock get_gandalf
                    self.add_deadtrade(
                        exchange="NSE",
                        slave_dev="test_manager",
                        exit_date=datetime.date(2020, 5, 6),
                        PnL=20.3,
                    )
                    self.add_deadtrade(
                        exchange="KRX",
                        slave_dev="test_manager",
                        exit_date=datetime.date(2020, 5, 6),
                        PnL=63.5,
                    )
                    self.add_deadtrade(
                        exchange="MCX",
                        slave_dev="test_admin",
                        exit_date=datetime.date(2020, 5, 6),
                        PnL=63.5,
                    )

                    def mock_get_gandalf(dead_trades, exchange):
                        return pd.DataFrame()

                    strat_add_views.get_gandalf = mock_get_gandalf

                    # Logged in as developer
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_developer, "password": self.password},
                    )
                    response = self.client.get(
                        "/portfolio_performance/test_developer_2"
                    )
                    self.assertEqual(
                        response.status_code, 403, msg=self.message_response_code
                    )
                    response = self.client.get("/portfolio_performance/company")
                    self.assertEqual(
                        response.status_code, 403, msg=self.message_response_code
                    )
                    response = self.client.get("/portfolio_performance/test_manager")
                    self.assertEqual(
                        response.status_code, 403, msg=self.message_response_code
                    )
                    response = self.client.get("/portfolio_performance/test_developer")
                    self.assertEqual(response.status_code, 200)
                    self.assertEqual(
                        response.data,
                        b"test_developer portfolio doesn't exist",
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as manager
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_manager, "password": self.password},
                    )
                    response = self.client.get("/portfolio_performance/company")
                    self.assertEqual(
                        response.status_code, 403, msg=self.message_response_code
                    )
                    response = self.client.get("/portfolio_performance/test_manager")
                    self.assertEqual(response.status_code, 200)
                    template, context = templates[0]
                    self.assertEqual(template.name, "strat_add/performance_metric.html")
                    self.assertTrue("gandalf_data" in context)
                    self.assertEqual(
                        set(context["gandalf_data"].keys()),
                        set(["KRX", "NSE"]),
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as admin
                    self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )
                    response = self.client.get("/portfolio_performance/company")
                    self.assertEqual(response.status_code, 200)
                    template, context = templates[1]
                    self.assertEqual(template.name, "strat_add/performance_metric.html")
                    self.assertTrue("gandalf_data" in context)
                    self.assertEqual(
                        set(context["gandalf_data"].keys()),
                        set(["KRX", "NSE", "MCX"]),
                    )
                    response = self.client.get("/sign_out")

    def test_kivifolio_report(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    # Logged in as developer
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_developer, "password": self.password},
                    )
                    response = self.client.get(
                        "/strat_expand/kivifolio_report/test_strat_3"
                    )
                    self.assertEqual(
                        response.status_code, 403, msg=self.message_response_code
                    )
                    Strategy.query.filter_by(
                        strategy_name="test_strat_3"
                    ).first().strat_state = 2
                    db.session.commit()
                    message = "kivifolio_report file does not match for {}!"
                    response = self.client.get(
                        "/strat_expand/kivifolio_report/test_strat_3"
                    )
                    template, context = templates[0]
                    self.assertTrue(
                        "report" in context, msg=message.format("developer")
                    )
                    self.assertTrue(
                        template.name == "strat_add/report.html",
                        msg=self.message_template,
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as manager
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_manager, "password": self.password},
                    )
                    response = self.client.get(
                        "/strat_expand/kivifolio_report/test_strat_2"
                    )
                    template, context = templates[1]
                    self.assertTrue("report" in context, msg=message.format("manager"))
                    self.assertTrue(
                        template.name == "strat_add/report.html",
                        msg=self.message_template,
                    )

                    response = self.client.get(
                        "/strat_expand/kivifolio_report/test_strat_1"
                    )
                    self.assertEqual(
                        response.status_code, 403, msg=self.message_response_code
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as admin
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )
                    response = self.client.get(
                        "/strat_expand/kivifolio_report/test_strat_1"
                    )
                    template, context = templates[2]
                    self.assertTrue("report" in context, msg=message.format("admin"))
                    self.assertTrue(
                        template.name == "strat_add/report.html",
                        msg=self.message_template,
                    )
                    response = self.client.get(
                        "/strat_expand/kivifolio_report/test_non_existent"
                    )
                    self.assertEqual(
                        response.status_code, 404, msg=self.message_response_code
                    )

    def test_show_performance_metrics(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                message = "performance metric checking failed for {}!"
                # Logged in as developer
                response = self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                response = self.client.post(
                    "/strat_expand/performance",
                    data={"strategy": "test_strat_3", "week": "current"},
                )
                self.assertEqual(
                    b"failed", response.data, msg=message.format("developer")
                )
                Strategy.query.filter_by(
                    strategy_name="test_strat_3"
                ).first().strat_state = 2
                db.session.commit()
                response = self.client.post(
                    "/strat_expand/performance",
                    data={"strategy": "test_strat_3", "week": "current"},
                )
                self.assertEqual(
                    b"success", response.data, msg=message.format("developer")
                )

                # Logged in as manager
                response = self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )
                response = self.client.post(
                    "/strat_expand/performance",
                    data={"strategy": "test_strat_1", "week": "current"},
                )
                self.assertEqual(
                    b"failed", response.data, msg=message.format("manager")
                )
                response = self.client.get("/sign_out")

                # Logged in as admin
                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                response = self.client.post(
                    "/strat_expand/performance",
                    data={"strategy": "test_strat_1", "week": "current"},
                )
                self.assertEqual(b"success", response.data, msg=message.format("admin"))

                Strategy.query.filter_by(
                    strategy_name="test_strat_2"
                ).first().strat_state = 3
                db.session.commit()
                response = self.client.post(
                    "/strat_expand/performance",
                    data={"strategy": "test_strat_2", "week": "current"},
                )
                self.assertEqual(b"success", response.data, msg=message.format("admin"))
                response = self.client.post(
                    "/strat_expand/performance",
                    data={"strategy": "test_non_existent", "week": "current"},
                )
                self.assertEqual(
                    b"failed", response.data, msg="non existent strategy passed!"
                )

    def test_show_kivifolio_report(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                message = "kivifolio report checking failed!"
                response = self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )
                response = self.client.post(
                    "/strat_expand/kivifolio_report",
                    data={"strategy": "test_strat_1"},
                )
                self.assertEqual(b"failed", response.data, msg=message)
                response = self.client.post(
                    "/strat_expand/kivifolio_report",
                    data={"strategy": "test_strat_2"},
                )
                self.assertEqual(b"failed", response.data, msg=message)
                response = self.client.get("/sign_out")

                # Logged in as admin
                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                response = self.client.post(
                    "/strat_expand/kivifolio_report",
                    data={"strategy": "test_strat_1"},
                )
                self.assertEqual(b"success", response.data, msg=message)
                response = self.client.post(
                    "/strat_expand/kivifolio_report",
                    data={"strategy": "test_non_existent"},
                )
                self.assertEqual(b"failed", response.data, msg="non existing strategy")

    def test_get_next_function_chart(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                # Logged in as developer
                response = self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                message = "get next function chart file does not match for {}!"
                response = self.client.post(
                    "/get_next_function_chart", data={"strategy": "test_strat_1"}
                )
                self.assertEqual(b"", response.data, msg=message.format("developer"))
                Strategy.query.filter_by(
                    strategy_name="test_strat_3"
                ).first().strat_state = 2
                db.session.commit()
                response = self.client.post(
                    "/get_next_function_chart", data={"strategy": "test_strat_3"}
                )
                self.assertIn(
                    b"75th Percentile", response.data, msg=message.format("developer")
                )
                response = self.client.get("/sign_out")

                # Logged in as manager
                response = self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )
                response = self.client.post(
                    "/get_next_function_chart", data={"strategy": "test_strat_1"}
                )
                self.assertEqual(b"", response.data, msg=message.format("developer"))
                response = self.client.post(
                    "/get_next_function_chart", data={"strategy": "test_strat_2"}
                )
                self.assertIn(
                    b"75th Percentile", response.data, msg=message.format("manager")
                )
                response = self.client.get("/sign_out")

                # Logged in as admin
                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                response = self.client.post(
                    "/get_next_function_chart", data={"strategy": "test_strat_1"}
                )
                self.assertIn(
                    b"75th Percentile", response.data, msg=message.format("admin")
                )
                response = self.client.post(
                    "/get_next_function_chart", data={"strategy": "test_non_existent"}
                )
                self.assertEqual(b"", response.data, msg="non existing strategy")

    def test_strategy_manager(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )
                    message = "{} strats not fetched correctly!"
                    message_review = "{} strats reviews not fetched correctly!"
                    Strategy.query.filter_by(
                        strategy_name="test_strat_2"
                    ).first().strat_state = 2
                    Strategy.query.filter_by(
                        strategy_name="test_strat_3"
                    ).first().strat_state = 2
                    Strategy.query.filter_by(
                        strategy_name="test_strat_1"
                    ).first().strat_state = 3

                    def mock_get_resetted_strats(environment):
                        if environment == "LIVE":
                            return ["test_strat_2"]
                        if environment == "TEST":
                            return []

                    strat_add_views.get_resetted_strats = mock_get_resetted_strats

                    def mock_get_strats_not_in_test_env():
                        return [
                            "test_strat_2"
                        ]  # Assuming test_start_2 has a newer rework running in test mode

                    strat_add_views.get_strats_not_in_test_env = (
                        mock_get_strats_not_in_test_env
                    )

                    self.client.get("/strategy_management")
                    template, context = templates[0]
                    self.assertEqual(
                        template.name,
                        "strat_add/strat_management.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        len(context["live_strats"]),
                        3,
                        msg=message.format("live"),
                    )
                    self.assertEqual(
                        list(context["live_strats"].keys()),
                        [
                            Strategy.query.filter_by(
                                strategy_name="test_strat_3"
                            ).first(),
                            Strategy.query.filter_by(
                                strategy_name="test_strat_2"
                            ).first(),
                            Strategy.query.filter_by(
                                strategy_name="test_reworked"
                            ).first(),
                        ],
                        msg=message.format("live"),
                    )
                    self.assertEqual(
                        len(
                            context["live_strats"][
                                Strategy.query.filter_by(
                                    strategy_name="test_strat_3"
                                ).first()
                            ]
                        ),
                        3,
                    )
                    self.assertEqual(
                        context["live_strats"][
                            Strategy.query.filter_by(
                                strategy_name="test_strat_2"
                            ).first()
                        ],
                        [0, 0, 0],
                    )
                    self.assertEqual(
                        context["live_strats_review_comments"],
                        {
                            "test_reworked": False,
                            "test_strat_2": True,
                            "test_strat_3": False,
                        },
                        msg=message_review.format("live"),
                    )
                    self.assertEqual(
                        context["rejected_strats"], [], msg=message.format("rejected")
                    )
                    self.assertEqual(
                        context["rejected_strats_review_comments"],
                        {},
                        msg=message_review.format("rejected"),
                    )
                    self.assertEqual(
                        context["dead_strats"],
                        {
                            Strategy.query.filter_by(
                                strategy_name="test_strat_1"
                            ).first(): [0, 0]
                        },
                        msg=message.format("dead"),
                    )
                    self.assertEqual(
                        context["dead_strats_review_comments"],
                        {"test_strat_1": True},
                        msg=message_review.format("dead"),
                    )
                    self.assertEqual(
                        context["resetted_strats_live"],
                        ["test_strat_2"],
                    )
                    self.assertEqual(
                        context["resetted_strats_test"],
                        [],
                    )
                    self.assertEqual(
                        context["strats_not_in_test_env"],
                        ["test_strat_2"],
                    )
                    self.client.get("/sign_out")
                    self.client.post(
                        "/login",
                        data={"email": self.email_developer, "password": self.password},
                    )
                    self.client.get("/strategy_management")
                    template, context = templates[1]
                    self.assertEqual(
                        template.name,
                        "strat_add/strat_management.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        len(context["live_strats"]),
                        1,
                        msg=message.format("live"),
                    )
                    strat_access = StrategyAccess(
                        strategy_name="test_strat_2", username="test_developer"
                    )
                    db.session.add(strat_access)
                    db.session.commit()
                    self.client.get("/strategy_management")
                    template, context = templates[2]
                    self.assertEqual(
                        len(context["live_strats"]),
                        2,
                        msg=message.format("live"),
                    )

    def test_live_performance_tracker(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )
                    message = "{} strats not fetched correctly!"
                    Strategy.query.filter_by(
                        strategy_name="test_strat_2"
                    ).first().strat_state = 2
                    Strategy.query.filter_by(
                        strategy_name="test_strat_3"
                    ).first().strat_state = 2
                    Strategy.query.filter_by(
                        strategy_name="test_strat_1"
                    ).first().strat_state = 2
                    self.client.get("/live_performance_tracker")
                    template, context = templates[0]
                    self.assertEqual(
                        template.name,
                        "strat_add/live_performance_tracker.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        len(context["live_strat_info"]),
                        2,
                        msg=message.format("live"),
                    )
                    self.assertEqual(
                        set(context["live_strat_info"].keys()),
                        set(
                            [
                                Strategy.query.filter_by(
                                    strategy_name="test_strat_3"
                                ).first(),
                                Strategy.query.filter_by(
                                    strategy_name="test_strat_1"
                                ).first(),
                            ]
                        ),
                        msg=message.format("live"),
                    )
                    strat = Strategy.query.filter_by(
                        strategy_name="test_strat_1"
                    ).first()
                    self.assertEqual(
                        context["live_strat_info"][strat]["monthly_sr"], 0.1
                    )
                    self.assertEqual(
                        context["live_strat_info"][strat]["trading_days"], 55
                    )
                    self.assertEqual(context["live_strat_info"][strat]["daily_sr"], 10)

                    self.client.get("/sign_out")
                    self.client.post(
                        "/login",
                        data={"email": self.email_developer, "password": self.password},
                    )
                    self.client.get("/live_performance_tracker")
                    template, context = templates[1]
                    self.assertEqual(
                        template.name,
                        "strat_add/live_performance_tracker.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        len(context["live_strat_info"]),
                        1,
                        msg=message.format("live"),
                    )
                    strat_access = StrategyAccess(
                        strategy_name="test_strat_1", username="test_developer"
                    )
                    db.session.add(strat_access)
                    db.session.commit()
                    self.client.get("/live_performance_tracker")
                    template, context = templates[2]
                    self.assertEqual(
                        len(context["live_strat_info"]),
                        2,
                        msg=message.format("live"),
                    )

    def test_run_backtest(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                message = "adding backtest for {} failed!"

                # Logged in as developer
                response = self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                response = self.client.post(
                    "/run_backtest",
                    data={"strategy": "test_strat_1", "service_index": "0"},
                )
                self.assertEqual(
                    b"failed",
                    response.data,
                    msg="developer submitting backtest for admin strategy!",
                )
                response = self.client.post(
                    "/run_backtest",
                    data={"strategy": "test_strat_2", "service_index": "1"},
                )
                self.assertEqual(
                    b"failed",
                    response.data,
                    msg="developer submitting backtest for developer strategy!",
                )
                response = self.client.get("/sign_out")

                # Logged in as manager
                response = self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )
                response = self.client.post(
                    "/run_backtest",
                    data={"strategy": "test_strat_2", "service_index": "0"},
                )
                self.assertEqual(b"added", response.data, msg=message.format("manager"))
                self.assertEqual(
                    PendingBacktests.query.filter_by(strategy_name="test_strat_2")
                    .first()
                    .service_state,
                    "00000",
                    msg=message.format("manager"),
                )

                response = self.client.post(
                    "/run_backtest",
                    data={"strategy": "test_strat_3", "service_index": "6"},
                )
                self.assertEqual(
                    b"unavailable", response.data, msg=message.format("manager")
                )

                response = self.client.post(
                    "/run_backtest",
                    data={"strategy": "test_strat_3", "service_index": "3"},
                )
                self.assertEqual(
                    b"already_submitted", response.data, msg=message.format("manager")
                )

                response = self.client.get("/sign_out")

                # Logged in as admin
                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                response = self.client.post(
                    "/run_backtest",
                    data={"strategy": "test_strat_1", "service_index": "3"},
                )
                self.assertEqual(
                    b"already_submitted", response.data, msg=message.format("admin")
                )

                backtest = PendingBacktests.query.filter_by(
                    strategy_name="test_strat_1"
                ).first()
                backtest.service_state = "22111"
                db.session.commit()
                response = self.client.post(
                    "/run_backtest",
                    data={"strategy": "test_strat_1", "service_index": "3"},
                )
                self.assertEqual(
                    b"upstream_error", response.data, msg=message.format("admin")
                )

                response = self.client.post(
                    "/run_backtest",
                    data={"strategy": "test_non_existent", "service_index": "0"},
                )
                self.assertEqual(
                    b"failed",
                    response.data,
                    msg="non existing strategy!",
                )

    def test_cluster_performance(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    pre_performance_message = "pre-performance report incorrect for {}!"
                    post_performance_message = (
                        "post-performance report incorrect for {}!"
                    )
                    # Logged in as developer
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_developer, "password": self.password},
                    )
                    response = self.client.get(
                        "/strat_expand/cluster_performance/test_strat_1/cluster_test"
                    )
                    self.assertEqual(
                        response.status_code,
                        403,
                        msg="developer was not denied access to cluster performance of admin strat!",
                    )
                    response = self.client.get(
                        "/strat_expand/cluster_performance/test_strat_3/cluster_test"
                    )
                    self.assertEqual(
                        response.status_code,
                        403,
                        msg="developer was not denied access to cluster performance of developer strat!",
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as manager
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_manager, "password": self.password},
                    )
                    response = self.client.get(
                        "/strat_expand/cluster_performance/test_strat_1/cluster_test"
                    )
                    self.assertEqual(
                        response.status_code,
                        403,
                        msg="manager was not denied access to admin strat!",
                    )
                    response = self.client.get(
                        "/strat_expand/cluster_performance/test_strat_3/cluster_test"
                    )
                    template, context = templates[0]
                    self.assertEqual(
                        template.name,
                        "strat_add/cluster_performance.html",
                        msg=self.message_template,
                    )
                    with open(
                        "./tests/test_data/test_strat_1_backtest_results.html"
                    ) as pre:
                        pre_performance = pre.read()
                    with open(
                        "./tests/test_data/test_strat_1_cluster_performance.html"
                    ) as post:
                        post_performance = post.read()
                    self.assertEqual(
                        context["pre_performance"],
                        pre_performance,
                        msg=pre_performance_message.format("manager"),
                    )
                    self.assertEqual(
                        context["post_performance"],
                        post_performance,
                        msg=post_performance_message.format("manager"),
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as admin
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )
                    response = self.client.get(
                        "/strat_expand/cluster_performance/test_strat_1/cluster_test"
                    )
                    template, context = templates[1]
                    self.assertEqual(
                        template.name,
                        "strat_add/cluster_performance.html",
                        msg=self.message_template,
                    )
                    with open(
                        "./tests/test_data/test_strat_1_backtest_results.html"
                    ) as pre:
                        pre_performance = pre.read()
                    with open(
                        "./tests/test_data/test_strat_1_cluster_performance.html"
                    ) as post:
                        post_performance = post.read()
                    self.assertEqual(
                        context["pre_performance"],
                        pre_performance,
                        msg=pre_performance_message.format("admin"),
                    )
                    self.assertEqual(
                        context["post_performance"],
                        post_performance,
                        msg=post_performance_message.format("admin"),
                    )
                    response = self.client.get(
                        "/strat_expand/cluster_performance/test_non_existent/cluster_test"
                    )
                    self.assertEqual(
                        response.status_code, 404, msg=self.message_response_code
                    )

    def test_show_cluster_performance(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                message = "cluster performance checking failed!"
                # Logged in as developer
                response = self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                response = self.client.post(
                    "/strat_expand/cluster_performance",
                    data={"strategy": "test_strat_1", "cluster": "cluster_test"},
                )
                self.assertEqual(b"failed", response.data, msg=message)
                response = self.client.post(
                    "/strat_expand/cluster_performance",
                    data={"strategy": "test_strat_3", "cluster": "cluster_test"},
                )
                self.assertEqual(
                    b"failed", response.data, msg="kivifolio report checking failed!"
                )
                response = self.client.get("/sign_out")

                # Logged in as manager
                response = self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )
                response = self.client.post(
                    "/strat_expand/cluster_performance",
                    data={"strategy": "test_strat_1", "cluster": "cluster_test"},
                )
                self.assertEqual(b"failed", response.data, msg=message)
                response = self.client.post(
                    "/strat_expand/cluster_performance",
                    data={"strategy": "test_strat_3", "cluster": "cluster_test"},
                )
                self.assertEqual(b"failed", response.data, msg=message)
                response = self.client.get("/sign_out")

                # Logged in as admin
                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                response = self.client.post(
                    "/strat_expand/cluster_performance",
                    data={"strategy": "test_strat_1", "cluster": "cluster_test"},
                )
                self.assertEqual(b"success", response.data, msg=message)
                response = self.client.post(
                    "/strat_expand/cluster_performance",
                    data={"strategy": "test_non_existent", "cluster": "cluster_test"},
                )
                self.assertEqual(b"failed", response.data, msg=message)

    def test_make_strategy_dead(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():

                def mock_kill_strategy(strategy: Strategy):
                    strategy.strat_state = 3
                    db.session.commit()
                    return [
                        "cluster_test_1",
                        "cluster_list_2",
                    ], "test_strat_1 was removed from live!"

                strat_add_views.kill_strategy = mock_kill_strategy
                Strategy.query.filter_by(
                    strategy_name="test_strat_3"
                ).first().strat_state = 2
                db.session.commit()
                message = "making strat dead failed for {}"
                # Logged in as developer
                response = self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                response = self.client.post(
                    "/make_strategy_dead", data={"strategy": "test_strat_3"}
                )
                self.assertEqual(
                    b"failed",
                    response.data,
                    msg="developer was not denied access to make strat dead page!",
                )
                response = self.client.get("/sign_out")

                # Logged in as manager
                response = self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )
                response = self.client.post(
                    "/make_strategy_dead", data={"strategy": "test_strat_3"}
                )
                self.assertEqual(
                    b'[\n  "cluster_test_1",\n  "cluster_list_2"\n]\n',
                    response.data,
                    msg=message.format("manager"),
                )
                self.assertTrue(
                    Strategy.query.filter_by(strategy_name="test_strat_3")
                    .first()
                    .strat_state
                    == 3,
                    msg="strategy not killed properly!",
                )
                response = self.client.get("/sign_out")

                Strategy.query.filter_by(
                    strategy_name="test_strat_3"
                ).first().strat_state = 2
                db.session.commit()
                # Logged in as admin
                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                response = self.client.post(
                    "/make_strategy_dead", data={"strategy": "test_strat_3"}
                )
                self.assertEqual(
                    b'[\n  "cluster_test_1",\n  "cluster_list_2"\n]\n',
                    response.data,
                    msg=message.format("admin"),
                )
                self.assertTrue(
                    Strategy.query.filter_by(strategy_name="test_strat_3")
                    .first()
                    .strat_state
                    == 3,
                    msg="strategy not killed properly!",
                )
                response = self.client.post(
                    "/make_strategy_dead", data={"strategy": "test_non_existent"}
                )
                self.assertEqual(
                    b"failed",
                    response.data,
                    msg="non existing strategy",
                )

    def test_strategy_review(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    self.add_strategy(
                        strategy_name="test_strat_101",
                        developer="test_admin",
                        segment="CASH",
                        exchange_name="NSE",
                        backtest_start_date=pd.Timestamp(2011, 1, 1),
                        book_long="LC1",
                        book_short="SC1",
                        strat_state=2,
                        submission_day=pd.Timestamp(2019, 12, 10),
                        trigger_coeff=100,
                        limit_coeff=156,
                        expiration_time=datetime.time(15, 15),
                        long_short=-1,
                        cluster_mapping=["cluster_test"],
                        live_start_day=pd.Timestamp(2020, 1, 1),
                    )
                    self.add_strategy(
                        strategy_name="test_strat_102",
                        developer="test_admin",
                        segment="CASH",
                        exchange_name="NSE",
                        backtest_start_date=pd.Timestamp(2011, 1, 1),
                        book_long="LC1",
                        book_short="SC1",
                        strat_state=1,
                        submission_day=datetime.datetime.now(),
                        trigger_coeff=100,
                        limit_coeff=156,
                        expiration_time=datetime.time(15, 15),
                        long_short=-1,
                        cluster_mapping=["cluster_test"],
                        strategy_reworked="test_strat_101",
                    )

                    def mock_accept_strategy(
                        strategy: str,
                        start_date: datetime.date,
                        mode: str,
                    ):
                        if strategy in ["test_strat_1"]:
                            return False, {}
                        else:
                            if not start_date:
                                start_date = datetime.datetime.now().date()
                            if mode == "live":
                                Strategy.query.filter_by(
                                    strategy_name=strategy
                                ).first().live_start_day = start_date
                                Strategy.query.filter_by(
                                    strategy_name=strategy
                                ).first().strat_state = 2
                            else:
                                Strategy.query.filter_by(
                                    strategy_name=strategy
                                ).first().strat_state = 5
                            db.session.commit()
                            return True, {
                                "cluster_list": ["cluster_test_1"],
                                "reworked_strategy": "test_strat_1",
                            }

                    def mock_reject_strategy(strategy: str):
                        if strategy in ["test_strat_1"]:
                            return False
                        else:
                            Strategy.query.filter_by(
                                strategy_name=strategy
                            ).first().strat_state = 4
                            db.session.commit()
                            return True

                    strat_add_views.accept_strategy = mock_accept_strategy
                    strat_add_views.reject_strategy = mock_reject_strategy
                    message = "context variable incorrect!"
                    # Logged in as developer
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_developer, "password": self.password},
                    )
                    response = self.client.get("/review_strategy/test_strat_5")
                    self.assertEqual(
                        response.status_code, 404, msg="non-existent strategy!"
                    )

                    response = self.client.get("/review_strategy/test_strat_1")
                    self.assertEqual(
                        response.status_code, 403, msg=self.message_response_code
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as manager
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_manager, "password": self.password},
                    )
                    response = self.client.get("/review_strategy/test_strat_2")
                    template, context = templates[0]
                    self.assertEqual(
                        template.name,
                        "strat_add/strategy_review.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        context["strategy_name"], "test_strat_2", msg=message
                    )
                    self.assertEqual(context["vis_success"], "hidden", msg=message)
                    self.assertEqual(context["vis_fail"], "hidden", msg=message)
                    self.assertEqual(context["strats_info"], {}, msg=message)
                    self.assertEqual(context["action"], "", msg=message)
                    self.assertEqual(
                        context["form"].timecheck.data, "FAIL", msg=message
                    )
                    self.assertEqual(
                        context["form"].trade_distribution_check.data,
                        "PASS",
                        msg=message,
                    )
                    self.assertEqual(
                        context["form"].risk_analysis.data, "FAILED", msg=message
                    )
                    self.assertEqual(
                        context["form"].num_days_trading.data, "PASS", msg=message
                    )

                    # Trying to save an empty review
                    response = self.client.post(
                        "/review_strategy/test_strat_3",
                        data={
                            "timecheck": "",
                            "correlation_check": "",
                            "trade_distribution_check": "",
                            "risk_analysis": "",
                            "num_days_trading": "",
                            "comments": "",
                            "to_change": "",
                            "action": "save",
                        },
                    )
                    template, context = templates[1]
                    self.assertEqual(
                        template.name,
                        "strat_add/strategy_review.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        context["strategy_name"], "test_strat_3", msg=message
                    )
                    self.assertEqual(context["vis_success"], "visible", msg=message)
                    self.assertEqual(context["vis_fail"], "hidden", msg=message)
                    self.assertEqual(context["strats_info"], {}, msg=message)
                    self.assertEqual(context["action"], "save", msg=message)
                    self.assertFalse(
                        "test_strat_3"
                        in [
                            strategy_review.strategy_name
                            for strategy_review in StrategyReview.query.all()
                        ],
                        msg="empty form entry added!",
                    )
                    # Accepting to live for a pending strategy
                    test_pending_backtest = PendingBacktests(
                        strategy_name="test_strat_3"
                    )
                    db.session.add(test_pending_backtest)
                    db.session.commit()
                    response = self.client.post(
                        "/review_strategy/test_strat_3",
                        data={
                            "timecheck": "PASS",
                            "correlation_check": "FAIL",
                            "trade_distribution_check": "",
                            "risk_analysis": "",
                            "num_days_trading": "",
                            "comments": "",
                            "to_change": "",
                            "action": "accept_live",
                        },
                    )
                    template, context = templates[2]
                    self.assertEqual(
                        template.name,
                        "strat_add/strategy_review.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        context["strategy_name"], "test_strat_3", msg=message
                    )
                    self.assertEqual(context["vis_success"], "visible", msg=message)
                    self.assertEqual(context["vis_fail"], "hidden", msg=message)
                    self.assertEqual(
                        context["strats_info"],
                        {
                            "cluster_list": ["cluster_test_1"],
                            "reworked_strategy": "test_strat_1",
                        },
                        msg=message,
                    )
                    self.assertEqual(context["action"], "accept_live", msg=message)
                    self.assertFalse(
                        "test_strat_3"
                        in [
                            backtest.strategy_name
                            for backtest in PendingBacktests.query.all()
                        ],
                        msg="backtests not removed after acceptance!",
                    )
                    self.assertTrue(
                        "test_strat_3"
                        in [
                            strategy_review.strategy_name
                            for strategy_review in StrategyReview.query.all()
                        ],
                        msg="strategy review not added!",
                    )
                    self.assertTrue(
                        Strategy.query.filter_by(strategy_name="test_strat_3")
                        .first()
                        .strat_state
                        == 2,
                        msg="strategy not accepted!",
                    )
                    self.assertEqual(
                        context["form"].timecheck.data, "PASS", msg=message
                    )
                    self.assertEqual(
                        context["form"].correlation_check.data, "FAIL", msg=message
                    )
                    self.assertEqual(
                        context["form"].trade_distribution_check.data, "", msg=message
                    )
                    self.assertEqual(
                        context["form"].risk_analysis.data, "", msg=message
                    )
                    self.assertEqual(
                        context["form"].num_days_trading.data, "", msg=message
                    )
                    self.assertEqual(context["form"].comments.data, "", msg=message)
                    self.assertEqual(context["form"].to_change.data, "", msg=message)

                    # Accepting to test for a pending strategy
                    StrategyReview.query.filter(
                        StrategyReview.strategy_name == "test_strat_3"
                    ).delete()
                    test_pending_backtest = PendingBacktests(
                        strategy_name="test_strat_3"
                    )
                    db.session.add(test_pending_backtest)
                    db.session.commit()
                    Strategy.query.filter_by(
                        strategy_name="test_strat_3"
                    ).first().strat_state = 1
                    response = self.client.post(
                        "/review_strategy/test_strat_3",
                        data={
                            "timecheck": "PASS",
                            "correlation_check": "FAIL",
                            "trade_distribution_check": "DONE",
                            "risk_analysis": "",
                            "num_days_trading": "",
                            "comments": "",
                            "to_change": "",
                            "action": "accept_test",
                        },
                    )
                    template, context = templates[3]
                    self.assertEqual(
                        template.name,
                        "strat_add/strategy_review.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        context["strategy_name"], "test_strat_3", msg=message
                    )
                    self.assertEqual(context["vis_success"], "visible", msg=message)
                    self.assertEqual(context["vis_fail"], "hidden", msg=message)
                    self.assertEqual(
                        context["strats_info"],
                        {
                            "cluster_list": ["cluster_test_1"],
                            "reworked_strategy": "test_strat_1",
                        },
                        msg=message,
                    )
                    self.assertEqual(context["action"], "accept_test", msg=message)
                    self.assertFalse(
                        "test_strat_3"
                        in [
                            backtest.strategy_name
                            for backtest in PendingBacktests.query.all()
                        ],
                        msg="backtests not removed after acceptance!",
                    )
                    self.assertTrue(
                        "test_strat_3"
                        in [
                            strategy_review.strategy_name
                            for strategy_review in StrategyReview.query.all()
                        ],
                        msg="strategy review not added!",
                    )
                    self.assertTrue(
                        Strategy.query.filter_by(strategy_name="test_strat_3")
                        .first()
                        .strat_state
                        == 5,
                        msg="strategy not accepted!",
                    )
                    self.assertEqual(
                        context["form"].timecheck.data, "PASS", msg=message
                    )
                    self.assertEqual(
                        context["form"].correlation_check.data, "FAIL", msg=message
                    )
                    self.assertEqual(
                        context["form"].trade_distribution_check.data,
                        "DONE",
                        msg=message,
                    )
                    self.assertEqual(
                        context["form"].risk_analysis.data, "", msg=message
                    )
                    self.assertEqual(
                        context["form"].num_days_trading.data, "", msg=message
                    )
                    self.assertEqual(context["form"].comments.data, "", msg=message)
                    self.assertEqual(context["form"].to_change.data, "", msg=message)

                    # Accepting to live for a test strategy
                    StrategyReview.query.filter(
                        StrategyReview.strategy_name == "test_strat_3"
                    ).delete()
                    db.session.commit()
                    response = self.client.post(
                        "/review_strategy/test_strat_3",
                        data={
                            "timecheck": "PASS",
                            "correlation_check": "FAIL",
                            "trade_distribution_check": "DONE",
                            "risk_analysis": "PASS",
                            "num_days_trading": "",
                            "comments": "",
                            "to_change": "",
                            "action": "accept_live",
                        },
                    )
                    template, context = templates[4]
                    self.assertEqual(
                        template.name,
                        "strat_add/strategy_review.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        context["strategy_name"], "test_strat_3", msg=message
                    )
                    self.assertEqual(context["vis_success"], "visible", msg=message)
                    self.assertEqual(context["vis_fail"], "hidden", msg=message)
                    self.assertEqual(
                        context["strats_info"],
                        {
                            "cluster_list": ["cluster_test_1"],
                            "reworked_strategy": "test_strat_1",
                        },
                        msg=message,
                    )
                    self.assertEqual(context["action"], "accept_live", msg=message)
                    self.assertTrue(
                        "test_strat_3"
                        in [
                            strategy_review.strategy_name
                            for strategy_review in StrategyReview.query.all()
                        ],
                        msg="strategy review not added!",
                    )
                    self.assertTrue(
                        Strategy.query.filter_by(strategy_name="test_strat_3")
                        .first()
                        .strat_state
                        == 2,
                        msg="strategy not accepted!",
                    )
                    self.assertEqual(
                        context["form"].timecheck.data, "PASS", msg=message
                    )
                    self.assertEqual(
                        context["form"].correlation_check.data, "FAIL", msg=message
                    )
                    self.assertEqual(
                        context["form"].trade_distribution_check.data,
                        "DONE",
                        msg=message,
                    )
                    self.assertEqual(
                        context["form"].risk_analysis.data, "PASS", msg=message
                    )
                    self.assertEqual(
                        context["form"].num_days_trading.data, "", msg=message
                    )
                    self.assertEqual(context["form"].comments.data, "", msg=message)
                    self.assertEqual(context["form"].to_change.data, "", msg=message)

                    # Reject a strategy
                    StrategyReview.query.filter(
                        StrategyReview.strategy_name == "test_strat_3"
                    ).delete()
                    test_pending_backtest = PendingBacktests(
                        strategy_name="test_strat_3"
                    )
                    db.session.add(test_pending_backtest)
                    db.session.commit()
                    response = self.client.post(
                        "/review_strategy/test_strat_2",
                        data={
                            "timecheck": "PASS",
                            "correlation_check": "FAIL",
                            "trade_distribution_check": "",
                            "risk_analysis": "",
                            "num_days_trading": "",
                            "comments": "",
                            "to_change": "",
                            "action": "reject",
                        },
                    )
                    template, context = templates[5]
                    self.assertEqual(
                        template.name,
                        "strat_add/strategy_review.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        context["strategy_name"], "test_strat_2", msg=message
                    )
                    self.assertEqual(context["vis_success"], "visible", msg=message)
                    self.assertEqual(context["vis_fail"], "hidden", msg=message)
                    self.assertEqual(context["strats_info"], {}, msg=message)
                    self.assertEqual(context["action"], "reject", msg=message)
                    self.assertFalse(
                        "test_strat_2"
                        in [
                            backtest.strategy_name
                            for backtest in PendingBacktests.query.all()
                        ],
                        msg="backtests not removed after rejection!",
                    )
                    self.assertTrue(
                        "test_strat_2"
                        in [
                            strategy_review.strategy_name
                            for strategy_review in StrategyReview.query.all()
                        ],
                        msg="strategy review not added!",
                    )
                    self.assertTrue(
                        Strategy.query.filter_by(strategy_name="test_strat_2")
                        .first()
                        .strat_state
                        == 4,
                        msg="strategy not rejected!",
                    )
                    self.assertEqual(
                        context["form"].timecheck.data, "PASS", msg=message
                    )
                    self.assertEqual(
                        context["form"].correlation_check.data, "FAIL", msg=message
                    )
                    self.assertEqual(
                        context["form"].trade_distribution_check.data, "", msg=message
                    )
                    self.assertEqual(
                        context["form"].risk_analysis.data, "", msg=message
                    )
                    self.assertEqual(
                        context["form"].num_days_trading.data, "", msg=message
                    )
                    self.assertEqual(context["form"].comments.data, "", msg=message)
                    self.assertEqual(context["form"].to_change.data, "", msg=message)
                    response = self.client.get("/sign_out")

                    # Logged in as admin
                    ## Failed accepting a strategy
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )
                    response = self.client.post(
                        "/review_strategy/test_strat_1",
                        data={
                            "timecheck": "PASS",
                            "correlation_check": "FAIL",
                            "trade_distribution_check": "",
                            "risk_analysis": "",
                            "num_days_trading": "",
                            "comments": "",
                            "to_change": "",
                            "action": "accept_live",
                        },
                    )
                    template, context = templates[6]
                    self.assertEqual(
                        template.name,
                        "strat_add/strategy_review.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        context["strategy_name"], "test_strat_1", msg=message
                    )
                    self.assertEqual(context["vis_success"], "hidden", msg=message)
                    self.assertEqual(context["vis_fail"], "visible", msg=message)
                    self.assertEqual(context["strats_info"], {}, msg=message)
                    self.assertEqual(context["action"], "accept_live", msg=message)
                    self.assertTrue(
                        Strategy.query.filter_by(strategy_name="test_strat_1")
                        .first()
                        .strat_state
                        == 1,
                        msg="strategy accepted!",
                    )
                    ## Failed rejecting a strategy
                    response = self.client.post(
                        "/review_strategy/test_strat_1",
                        data={
                            "timecheck": "PASS",
                            "correlation_check": "FAIL",
                            "trade_distribution_check": "",
                            "risk_analysis": "",
                            "num_days_trading": "",
                            "comments": "",
                            "to_change": "",
                            "action": "reject",
                        },
                    )
                    template, context = templates[7]
                    self.assertEqual(
                        template.name,
                        "strat_add/strategy_review.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        context["strategy_name"], "test_strat_1", msg=message
                    )
                    self.assertEqual(context["vis_success"], "hidden", msg=message)
                    self.assertEqual(context["vis_fail"], "visible", msg=message)
                    self.assertEqual(context["strats_info"], {}, msg=message)
                    self.assertEqual(context["action"], "reject", msg=message)
                    self.assertTrue(
                        Strategy.query.filter_by(strategy_name="test_strat_1")
                        .first()
                        .strat_state
                        == 1,
                        msg="strategy rejected!",
                    )
                    ## Accepting a rework strategy
                    response = self.client.post(
                        "/review_strategy/test_strat_102",
                        data={
                            "timecheck": "PASS",
                            "correlation_check": "FAIL",
                            "trade_distribution_check": "",
                            "risk_analysis": "",
                            "num_days_trading": "",
                            "comments": "",
                            "to_change": "",
                            "action": "accept_live",
                            "strat_date": datetime.datetime.now().date(),
                            "is_reworked_start": True,
                        },
                    )
                    template, context = templates[8]
                    self.assertEqual(
                        context["rework_start"], datetime.date(2020, 1, 1), msg=message
                    )
                    self.assertTrue(
                        Strategy.query.filter_by(strategy_name="test_strat_102")
                        .first()
                        .live_start_day
                        == datetime.datetime.now().date(),
                        msg="live_start_date not set",
                    )

    def test_show_strategy_review(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    message = "strategy review fetched wrongly for {}!"

                    # Logged in as developer
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_developer, "password": self.password},
                    )
                    response = self.client.get("/show_review_strategy/test_strat_5")
                    self.assertEqual(
                        response.status_code, 404, msg="non-existent strategy!"
                    )

                    response = self.client.get("/show_review_strategy/test_strat_1")
                    self.assertEqual(
                        response.status_code, 403, msg=self.message_response_code
                    )

                    response = self.client.get("/show_review_strategy/test_strat_3")
                    template, context = templates[0]
                    self.assertEqual(
                        template.name,
                        "strat_add/strategy_review_display.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        context["strategy_review"],
                        StrategyReview.query.filter_by(
                            strategy_name="test_strat_3"
                        ).first(),
                        msg=message.format("developer"),
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as manager
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_manager, "password": self.password},
                    )
                    response = self.client.get("/show_review_strategy/test_strat_2")
                    template, context = templates[1]
                    self.assertEqual(
                        template.name,
                        "strat_add/strategy_review_display.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        context["strategy_review"],
                        StrategyReview.query.filter_by(
                            strategy_name="test_strat_2"
                        ).first(),
                        msg=message.format("manager"),
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as admin
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )
                    response = self.client.get("/show_review_strategy/test_strat_1")
                    template, context = templates[2]
                    self.assertEqual(
                        template.name,
                        "strat_add/strategy_review_display.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        context["strategy_review"],
                        StrategyReview.query.filter_by(
                            strategy_name="test_strat_1"
                        ).first(),
                        msg=message.format("admin"),
                    )

    def test_comments_dashboard(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    message_keys = "strategies name do not match for {}!"
                    message = "strategy review data does not match for {}!"

                    # Logged in as developer
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_developer, "password": self.password},
                    )
                    strat_access = StrategyAccess(
                        strategy_name="test_strat_2", username="test_developer"
                    )
                    db.session.add(strat_access)
                    db.session.commit()
                    response = self.client.get("/comments_dashboard")
                    template, context = templates[0]
                    self.assertEqual(
                        template.name,
                        "strat_add/comments_dashboard.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        list(context["review_data"].keys()),
                        ["test_strat_2"],
                        msg=message_keys.format("manager"),
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as manager
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_manager, "password": self.password},
                    )
                    response = self.client.get("/comments_dashboard")
                    template, context = templates[1]
                    self.assertEqual(
                        template.name,
                        "strat_add/comments_dashboard.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        set(context["review_data"].keys()),
                        set(["test_strat_2", "test_strat_3"]),
                        msg=message_keys.format("manager"),
                    )
                    self.assertEqual(
                        context["review_data"]["test_strat_2"],
                        StrategyReview.query.filter_by(
                            strategy_name="test_strat_2"
                        ).first(),
                        msg=message.format("manager"),
                    )
                    self.assertEqual(
                        context["review_data"]["test_strat_3"],
                        StrategyReview.query.filter_by(
                            strategy_name="test_strat_3"
                        ).first(),
                        msg=message.format("manager"),
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as admin
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )
                    response = self.client.get("/comments_dashboard")  # noqa: F841
                    template, context = templates[2]
                    self.assertEqual(
                        template.name,
                        "strat_add/comments_dashboard.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        set(list(context["review_data"].keys())),
                        set(
                            [
                                "test_strat_1",
                                "test_strat_2",
                                "test_strat_3",
                                "test_strat_4",
                            ]
                        ),
                        msg=message_keys.format("admin"),
                    )
                    self.assertEqual(
                        context["review_data"]["test_strat_1"],
                        StrategyReview.query.filter_by(
                            strategy_name="test_strat_1"
                        ).first(),
                        msg=message.format("admin"),
                    )
                    self.assertEqual(
                        context["review_data"]["test_strat_2"],
                        StrategyReview.query.filter_by(
                            strategy_name="test_strat_2"
                        ).first(),
                        msg=message.format("admin"),
                    )
                    self.assertEqual(
                        context["review_data"]["test_strat_3"],
                        StrategyReview.query.filter_by(
                            strategy_name="test_strat_3"
                        ).first(),
                        msg=message.format("admin"),
                    )
                    self.assertEqual(
                        context["review_data"]["test_strat_4"],
                        StrategyReview.query.filter_by(
                            strategy_name="test_strat_4"
                        ).first(),
                        msg=message.format("admin"),
                    )

    def test_pending_strats(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    Strategy.query.filter_by(
                        strategy_name="test_strat_2"
                    ).first().strat_state = 5
                    Strategy.query.filter_by(
                        strategy_name="test_strat_4"
                    ).first().strat_state = 5

                    def mock_get_resetted_strats(environment):
                        return ["test_strat_2"]

                    strat_add_views.get_resetted_strats = mock_get_resetted_strats

                    # Logged in as developer
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_developer, "password": self.password},
                    )
                    response = self.client.get("/pending_strats")
                    self.assertEqual(
                        200,
                        response.status_code,
                    )
                    template, context = templates[0]

                    self.assertEqual(
                        template.name,
                        "strat_add/pending_strats.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        set(
                            strategy.strategy_name
                            for strategy in context["submitted_strategies"]
                        ),
                        set(["test_strat_3"]),
                    )
                    self.assertEqual(
                        set(
                            strategy.strategy_name
                            for strategy in context["test_strategies"]
                        ),
                        set(),
                    )
                    self.assertEqual(
                        context["resetted_strats"],
                        ["test_strat_2"],
                    )

                    response = self.client.get("/sign_out")

                    # Logged in as manager
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_manager, "password": self.password},
                    )
                    response = self.client.get("/pending_strats")
                    template, context = templates[1]

                    self.assertEqual(
                        template.name,
                        "strat_add/pending_strats.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        set(
                            strategy.strategy_name
                            for strategy in context["submitted_strategies"]
                        ),
                        set([]),
                    )
                    self.assertEqual(
                        set(
                            strategy.strategy_name
                            for strategy in context["test_strategies"]
                        ),
                        set(["test_strat_2"]),
                    )
                    self.assertEqual(
                        context["resetted_strats"],
                        ["test_strat_2"],
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as admin
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )
                    response = self.client.get("/pending_strats")
                    template, context = templates[2]
                    self.assertEqual(
                        set(
                            strategy.strategy_name
                            for strategy in context["submitted_strategies"]
                        ),
                        set(
                            [
                                "test_strat_1",
                            ]
                        ),
                    )
                    self.assertEqual(
                        set(
                            strategy.strategy_name
                            for strategy in context["test_strategies"]
                        ),
                        set(
                            [
                                "test_strat_4",
                            ]
                        ),
                    )
                    self.assertEqual(
                        context["review_comments"],
                        {
                            "test_strat_1": True,
                            "test_strat_4": False,
                        },
                    )
                    self.assertEqual(
                        context["resetted_strats"],
                        ["test_strat_2"],
                    )
                    response = self.client.get("/sign_out")

    def test_test_strats(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    Strategy.query.filter_by(
                        strategy_name="test_strat_3"
                    ).first().strat_state = 5
                    Strategy.query.filter_by(
                        strategy_name="test_strat_2"
                    ).first().strat_state = 5
                    Strategy.query.filter_by(
                        strategy_name="test_strat_4"
                    ).first().strat_state = 5

                    def mock_get_resetted_strats(environment):
                        return ["test_strat_2"]

                    strat_add_views.get_resetted_strats = mock_get_resetted_strats
                    # Logged in as developer
                    # (developer role cannot see self strategies here; only special access strats can be viewed)
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_developer, "password": self.password},
                    )
                    response = self.client.get("/test_strats")
                    self.assertEqual(
                        200,
                        response.status_code,
                    )
                    template, context = templates[0]
                    self.assertEqual(
                        template.name,
                        "strat_add/test_strats.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(
                        set(
                            strategy.strategy_name
                            for strategy in context["test_mode_strategies"]
                        ),
                        set([]),
                    )
                    self.assertEqual(
                        context["resetted_strats"],
                        ["test_strat_2"],
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as manager
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_manager, "password": self.password},
                    )

                    response = self.client.get("/test_strats")
                    template, context = templates[1]
                    self.assertEqual(
                        set(
                            strategy.strategy_name
                            for strategy in context["test_mode_strategies"]
                        ),
                        set(["test_strat_3", "test_strat_2"]),
                    )
                    self.assertEqual(
                        context["resetted_strats"],
                        ["test_strat_2"],
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as admin
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )
                    response = self.client.get("/test_strats")
                    template, context = templates[2]
                    self.assertEqual(
                        set(
                            strategy.strategy_name
                            for strategy in context["test_mode_strategies"]
                        ),
                        set(
                            [
                                "test_strat_3",
                                "test_strat_2",
                                "test_strat_4",
                            ]
                        ),
                    )
                    self.assertEqual(
                        context["resetted_strats"],
                        ["test_strat_2"],
                    )
                    response = self.client.get("/sign_out")

    def test_strategy_home(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:

                    def mock_get_available_historical_dates(folders):
                        pass

                    def mock_get_all_md_files(path, folder_name):
                        return {
                            "new_areas_to_work/title1/title1.md": [
                                "title1",
                                "#Content",
                                "description",
                            ],
                            "new_areas_to_work/title2/title2.md": [
                                "title2",
                                "conetnt",
                                "description",
                            ],
                        }

                    def mock_get_file_data_from_minio(path: str):
                        file = open(f"./tests/test_data/{path}", "rb")
                        data = file.read()
                        file.close()
                        return data

                    def mock_update_content_on_minio(path, content):
                        pass

                    def mock_upload_dataframe_to_minio(
                        path: str, dataframe: pd.DataFrame
                    ):
                        self.assertEqual(
                            ["path", "title", "description"],
                            dataframe.columns.tolist(),
                            "dataframe recieved is invalid",
                        )

                    strat_add_views.upload_dataframe_to_minio = (
                        mock_upload_dataframe_to_minio
                    )
                    strat_add_views.update_content_on_minio = (
                        mock_update_content_on_minio
                    )
                    strat_add_views.get_available_historical_dates = (
                        mock_get_available_historical_dates
                    )
                    strat_add_views.get_all_md_files = mock_get_all_md_files
                    strat_add_views.get_file_data_from_minio = (
                        mock_get_file_data_from_minio
                    )

                    # Logged in as developer
                    self.client.post(
                        "/login",
                        data={"email": self.email_developer, "password": self.password},
                    )
                    response = self.client.get("/strat_home")
                    self.assertEqual(
                        200,
                        response.status_code,
                    )
                    template, context = templates[0]

                    self.assertEqual(
                        template.name,
                        "strat_add/strat_home.html",
                        msg=self.message_template,
                    )
                    self.assertTrue("new_areas_to_work" in context.keys())
                    response = self.client.post(
                        "/strat_home",
                        data={
                            "path": "new_areas_to_work/title2/title2.md",
                            "title": "policy2",
                            "content": "# modified",
                            "description": "description",
                        },
                    )
                    self.assertEqual(
                        403,
                        response.status_code,
                    )
                    response = self.client.get("/sign_out")
                    # Logged in as admin
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )
                    response = self.client.get("/strat_home")
                    response = self.client.get("/strat_home")
                    self.assertEqual(
                        200,
                        response.status_code,
                    )
                    template, context = templates[1]
                    self.assertEqual(
                        template.name,
                        "strat_add/strat_home.html",
                        msg=self.message_template,
                    )
                    self.assertTrue("new_areas_to_work" in context.keys())
                    response = self.client.post(
                        "/strat_home",
                        data={
                            "path": "new_areas_to_work/title2/title2.md",
                            "title": "policy2",
                            "content": "# modified",
                            "description": "description",
                        },
                    )
                    self.assertEqual(
                        200,
                        response.status_code,
                    )

                    def mock_check_object_exists(prefix: str):
                        return False

                    strat_add_views.check_object_exists = mock_check_object_exists
                    response = self.client.post(
                        "/strat_home",
                        data={
                            "path": "new_areas_to_work/title2/title2.md",
                            "title": "policy2",
                            "content": "# modified",
                            "description": "description",
                        },
                    )
                    self.assertEqual(
                        200,
                        response.status_code,
                    )

    def test_manage_cluster_mapping(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                strat = Strategy.query.filter_by(strategy_name="test_strat_2").first()
                strat.strat_state = 2
                db.session.commit()
                cluster1 = Strategy(
                    strategy_name="cluster_options",
                    developer="test_manager",
                    segment="CASH",
                    exchange_name="NSE",
                    backtest_start_date=pd.Timestamp(2009, 1, 1),
                    book_long="LC1",
                    book_short="SC2",
                    strat_state=2,
                    submission_day=datetime.datetime.now(),
                    trigger_coeff=500,
                    limit_coeff=300,
                    expiration_time=datetime.time(15, 15),
                    long_short="-1",
                    cluster_mapping=[],
                    comments=None,
                    reworked_strategy=None,
                )
                cluster2 = Strategy(
                    strategy_name="cluster_test",
                    developer="test_manager",
                    segment="CASH",
                    exchange_name="NSE",
                    backtest_start_date=pd.Timestamp(2009, 1, 1),
                    book_long="LC1",
                    book_short="SC2",
                    strat_state=2,
                    submission_day=datetime.datetime.now(),
                    trigger_coeff=500,
                    limit_coeff=300,
                    expiration_time=datetime.time(15, 15),
                    long_short="-1",
                    cluster_mapping=[],
                    comments=None,
                    reworked_strategy=None,
                )
                db.session.add(cluster1)
                db.session.add(cluster2)
                db.session.commit()
                # Logged in as developer
                self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                response = self.client.get("/manage_cluster_mapping")
                self.assertEqual(
                    403,
                    response.status_code,
                )
                response = self.client.get("/sign_out")
                # Logged in as manager
                response = self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )
                response = self.client.get("/manage_cluster_mapping")
                self.assertEqual(
                    200,
                    response.status_code,
                )
                # Current Clusters
                strat = Strategy.query.filter_by(strategy_name="test_strat_2").first()
                self.assertEqual(
                    set(cluster.cluster_name for cluster in strat.cluster_mapping),
                    set(["cluster_test"]),
                )
                clusters = StrategyClusterMapping.query.filter_by(
                    strategy_name="test_strat_2",
                    mapping_status=ClusterMappingStatus.get_status(
                        description="LIVE_ENV"
                    ),
                ).all()
                self.assertEqual(len(clusters), 1)
                clusters = StrategyClusterMapping.query.filter_by(
                    strategy_name="test_strat_2",
                    mapping_status=ClusterMappingStatus.get_status(
                        description="TEST_ENV"
                    ),
                ).all()
                self.assertEqual(len(clusters), 1)

                # New clusters for live env
                response = self.client.post(
                    "/manage_cluster_mapping",
                    data={
                        "strategies": "test_strat_2",
                        "cluster_mapping": ["cluster_options", "cluster_test"],
                        "ENV": "LIVE_ENV",
                    },
                )
                db.session.commit()
                clusters = StrategyClusterMapping.query.filter_by(
                    strategy_name="test_strat_2",
                    mapping_status=ClusterMappingStatus.get_status(
                        description="LIVE_ENV"
                    ),
                ).all()
                self.assertEqual(len(clusters), 2)
                clusters = StrategyClusterMapping.query.filter_by(
                    strategy_name="test_strat_2",
                    mapping_status=ClusterMappingStatus.get_status(
                        description="TEST_ENV"
                    ),
                ).all()
                self.assertEqual(len(clusters), 1)
                # New clusters for test env
                response = self.client.post(
                    "/manage_cluster_mapping",
                    data={
                        "strategies": "test_strat_2",
                        "cluster_mapping": ["cluster_options"],
                        "ENV": "TEST_ENV",
                    },
                )
                clusters = StrategyClusterMapping.query.filter_by(
                    strategy_name="test_strat_2",
                    mapping_status=ClusterMappingStatus.get_status(
                        description="TEST_ENV"
                    ),
                ).all()
                self.assertEqual(len(clusters), 1)
                clusters = StrategyClusterMapping.query.filter_by(
                    strategy_name="test_strat_2",
                    mapping_status=ClusterMappingStatus.get_status(
                        description="LIVE_ENV"
                    ),
                ).all()
                self.assertEqual(len(clusters), 2)

                self.assertEqual(
                    set(cluster.cluster_name for cluster in strat.cluster_mapping),
                    set(["cluster_options", "cluster_test"]),
                )

                # manager accessing admin strategy
                response = self.client.post(
                    "/manage_cluster_mapping",
                    data={
                        "strategies": "test_strat_1",
                        "cluster_mapping": "cluster_options",
                        "ENV": "TEST_ENV",
                    },
                )
                self.assertEqual(
                    403,
                    response.status_code,
                )

    def test_todo_dashboard(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    review = StrategyReview(
                        strategy_name="test_strat_3", to_do="testing"
                    )
                    db.session.add(review)
                    strat = Strategy.query.filter_by(
                        strategy_name="test_strat_3"
                    ).first()
                    strat.strat_state = 2
                    db.session.commit()

                    # Logged in as developer
                    self.client.post(
                        "/login",
                        data={"email": self.email_developer, "password": self.password},
                    )
                    response = self.client.get("/todo_dashboard/live")
                    self.assertEqual(
                        200,
                        response.status_code,
                    )
                    template, context = templates[0]

                    self.assertEqual(
                        template.name,
                        "strat_add/todo_dashboard.html",
                        msg=self.message_template,
                    )
                    self.assertEqual(len(context["todo_list"]), 1)
                    self.assertEqual(
                        list(context["todo_list"].keys())[0].strategy_name,
                        "test_strat_3",
                    )
                    self.assertEqual(list(context["todo_list"].values())[0], "testing")
                    response = self.client.get("/sign_out")
                    strat = Strategy.query.filter_by(
                        strategy_name="test_strat_2"
                    ).first()
                    strat.strat_state = 3
                    strat.live_start_day = datetime.datetime.now()
                    strat.review.first().to_do = "testing_manager"
                    db.session.commit()
                    # Logged in as manager
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_manager, "password": self.password},
                    )
                    response = self.client.get("/todo_dashboard/DEAD")
                    template, context = templates[1]
                    self.assertEqual(len(context["todo_list"]), 1)
                    self.assertEqual(
                        set(
                            [
                                strategy.strategy_name
                                for strategy in list(context["todo_list"].keys())
                            ]
                        ),
                        set(["test_strat_2"]),
                    )
                    self.assertEqual(
                        set(list(context["todo_list"].values())),
                        set(["testing_manager"]),
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as admin
                    strat = Strategy.query.filter_by(
                        strategy_name="test_strat_2"
                    ).first()
                    strat.strat_state = 4
                    strat = Strategy.query.filter_by(
                        strategy_name="test_strat_3"
                    ).first()
                    strat.strat_state = 4
                    db.session.commit()
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )
                    response = self.client.get("/todo_dashboard/REJECTED")
                    template, context = templates[2]
                    self.assertEqual(len(context["todo_list"]), 2)
                    self.assertEqual(
                        set(
                            [
                                strategy.strategy_name
                                for strategy in list(context["todo_list"].keys())
                            ]
                        ),
                        set(["test_strat_2", "test_strat_3"]),
                    )

    def test_add_todo(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                # Logged in as developer
                response = self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                response = self.client.post(
                    "/strat_expand/add_todo",
                    data={"strategy_name": "test_strat_1", "todo": "testing"},
                )
                self.assertEqual(
                    403,
                    response.status_code,
                )
                response = self.client.get("/sign_out")

                # Logged in as admin
                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                response = self.client.post(
                    "/strat_expand/add_todo",
                    data={"strategy_name": "test_strat_1", "todo": "testing"},
                )
                self.assertEqual(
                    b"success", response.data, msg="admin unaale to add todo"
                )
                self.assertEqual(
                    200,
                    response.status_code,
                )
                self.assertEqual(
                    "testing",
                    Strategy.query.filter_by(strategy_name="test_strat_1")
                    .first()
                    .review.first()
                    .to_do,
                    msg="todo list not updated",
                )
                response = self.client.post(
                    "/strat_expand/add_todo",
                    data={"strategy_name": "test_strat_2", "todo": "testing"},
                )
                self.assertEqual(
                    b"success", response.data, msg="admin unaale to add todo"
                )
                self.assertEqual(
                    200,
                    response.status_code,
                )
                response = self.client.post(
                    "/strat_expand/add_todo",
                    data={
                        "strategy_name": "test_strat_non_existant",
                        "todo": "testing",
                    },
                )
                self.assertEqual(
                    404,
                    response.status_code,
                )

    def test_sentinel_dead_backtest(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                self.add_strategy(
                    strategy_name="test_strat_101",
                    developer="test_admin",
                    segment="CASH",
                    exchange_name="NSE",
                    backtest_start_date=pd.Timestamp(2011, 1, 1),
                    book_long="LC1",
                    book_short="SC1",
                    strat_state=3,
                    submission_day=datetime.datetime.now(),
                    trigger_coeff=100,
                    limit_coeff=156,
                    expiration_time=datetime.time(15, 15),
                    long_short=-1,
                    cluster_mapping=["cluster_test"],
                )
                self.add_strategy(
                    strategy_name="test_strat_102",
                    developer="test_manager",
                    segment="CASH",
                    exchange_name="NSE",
                    backtest_start_date=pd.Timestamp(2009, 1, 1),
                    book_long="LC1",
                    book_short="SC2",
                    strat_state=3,
                    submission_day=datetime.datetime.now(),
                    trigger_coeff=500,
                    limit_coeff=300,
                    expiration_time=datetime.time(15, 15),
                    long_short=-1,
                    cluster_mapping=["cluster_test"],
                )

                self.add_strategy(
                    strategy_name="test_strat_103",
                    developer="test_developer",
                    segment="FUTSTK",
                    exchange_name="NSE",
                    backtest_start_date=pd.Timestamp(2018, 1, 1),
                    book_long="LC3",
                    book_short="SC1",
                    strat_state=3,
                    submission_day=datetime.datetime.now(),
                    trigger_coeff=100,
                    limit_coeff=156,
                    expiration_time=datetime.time(14, 45),
                    long_short=-1,
                    cluster_mapping=["cluster_kailash"],
                    comments="uses multiple indicators",
                )
                self.add_strategy(
                    strategy_name="cluster_dead",
                    developer="test_developer",
                    segment="FUTSTK",
                    exchange_name="NSE",
                    backtest_start_date=pd.Timestamp(2018, 1, 1),
                    book_long="LC3",
                    book_short="SC1",
                    strat_state=3,
                    submission_day=datetime.datetime.now(),
                    trigger_coeff=100,
                    limit_coeff=156,
                    expiration_time=datetime.time(14, 45),
                    long_short=-1,
                    cluster_mapping=["cluster_kailash"],
                    comments="uses multiple indicators",
                )

                # Logged in as developer
                response = self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "test_strat_101",
                        "action": "Add",
                        "service": "dead_backtest",
                    },
                )
                self.assertEqual(
                    b"failed",
                    response.data,
                    msg="developer submitting dead_backtest for admin strategy!",
                )
                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "test_strat_3",
                        "action": "Add",
                        "service": "dead_backtest",
                    },
                )
                self.assertEqual(
                    b"status_failed",
                    response.data,
                    msg="developer submitting dead_backtest for pending strategy!",
                )

                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "test_strat_103",
                        "action": "Add",
                        "service": "dead_backtest",
                    },
                )
                self.assertEqual(
                    b"success",
                    response.data,
                    msg="developer submitting dead_backtest",
                )

                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "cluster_dead",
                        "action": "Add",
                        "service": "dead_backtest",
                    },
                )
                self.assertEqual(
                    b"invalid_strategy",
                    response.data,
                    msg="developer submitting dead_backtest for cluster strategy",
                )

                response = self.client.get("/sign_out")

                # Logged in as manager
                response = self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )
                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "test_strat_101",
                        "action": "Add",
                        "service": "dead_backtest",
                    },
                )
                self.assertEqual(
                    b"failed",
                    response.data,
                    msg="manager submitting dead_backtest for admin strategy!",
                )

                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "test_strat_102",
                        "action": "Add",
                        "service": "dead_backtest",
                    },
                )
                self.assertEqual(
                    b"success",
                    response.data,
                    msg="manager submitting dead_backtest!",
                )

                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "test_strat_103",
                        "action": "Add",
                        "service": "dead_backtest",
                    },
                )
                self.assertEqual(
                    b"success",
                    response.data,
                    msg="manager submitting dead_backtest for developer strategy!",
                )

                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "test_strat_3",
                        "action": "Add",
                        "service": "dead_backtest",
                    },
                )
                self.assertEqual(
                    b"status_failed",
                    response.data,
                    msg="manager submitting dead_backtest for pending strategy!",
                )

                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "cluster_dead",
                        "action": "Add",
                        "service": "dead_backtest",
                    },
                )
                self.assertEqual(
                    b"invalid_strategy",
                    response.data,
                    msg="manager submitting dead_backtest for cluster strategy",
                )

                response = self.client.get("/sign_out")

                # Logged in as admin
                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )

                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "test_strat_1",
                        "action": "Add",
                        "service": "dead_backtest",
                    },
                )
                self.assertEqual(
                    b"status_failed",
                    response.data,
                    msg="admin submitting dead_backtest for pending strategy!",
                )

                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "test_strat_101",
                        "action": "Add",
                        "service": "dead_backtest",
                    },
                )
                self.assertEqual(
                    b"success",
                    response.data,
                    msg="admin submitting dead_backtest!",
                )

                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "test_strat_102",
                        "action": "Add",
                        "service": "dead_backtest",
                    },
                )
                self.assertEqual(
                    b"success",
                    response.data,
                    msg="admin submitting dead_backtest for manager strategy!",
                )

                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "test_strat_103",
                        "action": "Add",
                        "service": "dead_backtest",
                    },
                )
                self.assertEqual(
                    b"success",
                    response.data,
                    msg="admin submitting dead_backtest for developer strategy!",
                )

                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "cluster_dead",
                        "action": "Add",
                        "service": "dead_backtest",
                    },
                )
                self.assertEqual(
                    b"invalid_strategy",
                    response.data,
                    msg="admin submitting dead_backtest for cluster strategy",
                )

    def test_sentinel_rejected_backtest(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                self.add_strategy(
                    strategy_name="test_strat_101",
                    developer="test_admin",
                    segment="FUTSTK",
                    exchange_name="NSE",
                    backtest_start_date=pd.Timestamp(2018, 1, 1),
                    book_long="LC3",
                    book_short="SC1",
                    strat_state=4,
                    submission_day=datetime.datetime.now(),
                    trigger_coeff=100,
                    limit_coeff=156,
                    expiration_time=datetime.time(14, 45),
                    long_short=-1,
                    cluster_mapping=["cluster_kailash"],
                    comments="uses multiple indicators",
                )
                self.add_strategy(
                    strategy_name="test_strat_102",
                    developer="test_manager",
                    segment="FUTSTK",
                    exchange_name="NSE",
                    backtest_start_date=pd.Timestamp(2018, 1, 1),
                    book_long="LC3",
                    book_short="SC1",
                    strat_state=4,
                    submission_day=datetime.datetime.now(),
                    trigger_coeff=100,
                    limit_coeff=156,
                    expiration_time=datetime.time(14, 45),
                    long_short=-1,
                    cluster_mapping=["cluster_kailash"],
                    comments="uses multiple indicators",
                )
                self.add_strategy(
                    strategy_name="test_strat_103",
                    developer="test_developer",
                    segment="FUTSTK",
                    exchange_name="NSE",
                    backtest_start_date=pd.Timestamp(2018, 1, 1),
                    book_long="LC3",
                    book_short="SC1",
                    strat_state=4,
                    submission_day=datetime.datetime.now(),
                    trigger_coeff=100,
                    limit_coeff=156,
                    expiration_time=datetime.time(14, 45),
                    long_short=-1,
                    cluster_mapping=["cluster_kailash"],
                    comments="uses multiple indicators",
                )
                self.add_strategy(
                    strategy_name="cluster_rejected",
                    developer="test_developer",
                    segment="FUTSTK",
                    exchange_name="NSE",
                    backtest_start_date=pd.Timestamp(2018, 1, 1),
                    book_long="LC3",
                    book_short="SC1",
                    strat_state=4,
                    submission_day=datetime.datetime.now(),
                    trigger_coeff=100,
                    limit_coeff=156,
                    expiration_time=datetime.time(14, 45),
                    long_short=-1,
                    cluster_mapping=["cluster_kailash"],
                    comments="uses multiple indicators",
                )

                # Logged in as developer
                response = self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "test_strat_101",
                        "action": "Add",
                        "service": "rejected_backtest",
                    },
                )
                self.assertEqual(
                    b"failed",
                    response.data,
                    msg="developer submitting rejected_backtest for admin strategy!",
                )
                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "test_strat_3",
                        "action": "Add",
                        "service": "rejected_backtest",
                    },
                )
                self.assertEqual(
                    b"status_failed",
                    response.data,
                    msg="developer submitting rejected_backtest for pending strategy!",
                )

                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "test_strat_103",
                        "action": "Add",
                        "service": "rejected_backtest",
                    },
                )
                self.assertEqual(
                    b"success",
                    response.data,
                    msg="developer submitting rejected_backtest",
                )

                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "cluster_rejected",
                        "action": "Add",
                        "service": "rejected_backtest",
                    },
                )
                self.assertEqual(
                    b"invalid_strategy",
                    response.data,
                    msg="developer submitting rejected_backtest for cluster strategy",
                )

                response = self.client.get("/sign_out")

                # Logged in as manager
                response = self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )
                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "test_strat_101",
                        "action": "Add",
                        "service": "rejected_backtest",
                    },
                )
                self.assertEqual(
                    b"failed",
                    response.data,
                    msg="manager submitting rejected_backtest for admin strategy!",
                )

                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "test_strat_102",
                        "action": "Add",
                        "service": "rejected_backtest",
                    },
                )
                self.assertEqual(
                    b"success",
                    response.data,
                    msg="manager submitting rejected_backtest!",
                )

                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "test_strat_103",
                        "action": "Add",
                        "service": "rejected_backtest",
                    },
                )
                self.assertEqual(
                    b"success",
                    response.data,
                    msg="manager submitting rejected_backtest for developer strategy!",
                )

                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "test_strat_3",
                        "action": "Add",
                        "service": "rejected_backtest",
                    },
                )
                self.assertEqual(
                    b"status_failed",
                    response.data,
                    msg="manager submitting rejected_backtest for pending strategy!",
                )

                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "cluster_rejected",
                        "action": "Add",
                        "service": "rejected_backtest",
                    },
                )
                self.assertEqual(
                    b"invalid_strategy",
                    response.data,
                    msg="manager submitting rejected_backtest for cluster strategy",
                )

                response = self.client.get("/sign_out")

                # Logged in as admin
                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )

                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "test_strat_1",
                        "action": "Add",
                        "service": "rejected_backtest",
                    },
                )
                self.assertEqual(
                    b"status_failed",
                    response.data,
                    msg="admin submitting rejected_backtest for pending strategy!",
                )

                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "test_strat_101",
                        "action": "Add",
                        "service": "rejected_backtest",
                    },
                )
                self.assertEqual(
                    b"success",
                    response.data,
                    msg="admin submitting rejected_backtest!",
                )

                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "test_strat_102",
                        "action": "Add",
                        "service": "rejected_backtest",
                    },
                )
                self.assertEqual(
                    b"success",
                    response.data,
                    msg="admin submitting rejected_backtest for manager strategy!",
                )

                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "test_strat_103",
                        "action": "Add",
                        "service": "rejected_backtest",
                    },
                )
                self.assertEqual(
                    b"success",
                    response.data,
                    msg="admin submitting rejected_backtest for developer strategy!",
                )

                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "cluster_rejected",
                        "action": "Add",
                        "service": "rejected_backtest",
                    },
                )
                self.assertEqual(
                    b"invalid_strategy",
                    response.data,
                    msg="admin submitting rejected_backtest for cluster strategy",
                )

    def test_sentinel_kivifolio(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                self.add_strategy(
                    strategy_name="cluster_live",
                    developer="test_admin",
                    segment="FUTSTK",
                    exchange_name="NSE",
                    backtest_start_date=pd.Timestamp(2018, 1, 1),
                    book_long="LC3",
                    book_short="SC1",
                    strat_state=2,
                    submission_day=datetime.datetime.now(),
                    trigger_coeff=100,
                    limit_coeff=156,
                    expiration_time=datetime.time(14, 45),
                    long_short=-1,
                    cluster_mapping=["cluster_kailash"],
                    comments="uses multiple indicators",
                )

                self.add_strategy(
                    strategy_name="cluster_dead",
                    developer="test_developer",
                    segment="FUTSTK",
                    exchange_name="NSE",
                    backtest_start_date=pd.Timestamp(2018, 1, 1),
                    book_long="LC3",
                    book_short="SC1",
                    strat_state=3,
                    submission_day=datetime.datetime.now(),
                    trigger_coeff=100,
                    limit_coeff=156,
                    expiration_time=datetime.time(14, 45),
                    long_short=-1,
                    cluster_mapping=["cluster_kailash"],
                    comments="uses multiple indicators",
                )

                # Logged in as developer
                response = self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "cluster_live",
                        "action": "Add",
                        "service": "kivifolio",
                    },
                )
                self.assertEqual(
                    b"failed",
                    response.data,
                    msg="developer submitting sentinel kivifolio",
                )

                response = self.client.get("/sign_out")

                # Logged in as manager
                response = self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )

                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "cluster_live",
                        "action": "Add",
                        "service": "kivifolio",
                    },
                )
                self.assertEqual(
                    b"failed",
                    response.data,
                    msg="manager submitting sentinel kivifolio",
                )

                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "cluster_dead",
                        "action": "Add",
                        "service": "kivifolio",
                    },
                )
                self.assertEqual(
                    b"status_failed",
                    response.data,
                    msg="manager submitting sentinel kivifolio",
                )

                response = self.client.get("/sign_out")

                # Logged in as admin
                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                response = self.client.post(
                    "/add_to_sentinel",
                    data={
                        "strategy": "cluster_live",
                        "action": "Add",
                        "service": "kivifolio",
                    },
                )
                self.assertEqual(
                    b"success",
                    response.data,
                    msg="admin submitting sentinel kivifolio",
                )

    def test_get_slaves(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                self.add_strategy(
                    strategy_name="cluster_test",
                    developer="test_manager",
                    segment="FUTSTK",
                    exchange_name="NSE",
                    backtest_start_date=pd.Timestamp(2018, 1, 1),
                    book_long="LC3",
                    book_short="SC1",
                    strat_state=2,
                    submission_day=datetime.datetime.now(),
                    trigger_coeff=100,
                    limit_coeff=156,
                    expiration_time=datetime.time(14, 45),
                    long_short=-1,
                    comments="uses multiple indicators",
                )

                self.add_strategy(
                    strategy_name="slave1",
                    developer="test_developer",
                    segment="OPTIDX",
                    exchange_name="NSE",
                    backtest_start_date=pd.Timestamp(2018, 1, 1),
                    book_long="LC3",
                    book_short="SC1",
                    strat_state=2,
                    submission_day=datetime.datetime.now(),
                    trigger_coeff=100,
                    limit_coeff=156,
                    expiration_time=datetime.time(14, 45),
                    long_short=-1,
                    cluster_mapping=["cluster_test"],
                    comments="uses multiple indicators",
                )
                self.add_strategy(
                    strategy_name="slave2",
                    developer="test_admin",
                    segment="OPTIDX",
                    exchange_name="NSE",
                    backtest_start_date=pd.Timestamp(2018, 1, 1),
                    book_long="LC3",
                    book_short="SC1",
                    strat_state=2,
                    submission_day=datetime.datetime.now(),
                    trigger_coeff=100,
                    limit_coeff=156,
                    expiration_time=datetime.time(14, 45),
                    long_short=-1,
                    cluster_mapping=["cluster_test"],
                    comments="uses multiple indicators",
                )

                # Logged in as developer
                response = self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                response = self.client.post(
                    "/get_slaves",
                    data={
                        "cluster": "cluster_test",
                    },
                )
                self.assertEqual(
                    b"failed",
                    response.data,
                    msg="invalid access to get_slaves",
                )
                response = self.client.get("/sign_out")

                # Logged in as manager
                response = self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )
                response = self.client.post(
                    "/get_slaves",
                    data={
                        "cluster": "cluster_test",
                    },
                )
                self.assertEqual(
                    ["slave1"],
                    json.loads(response.data.decode()),
                    msg="slave list of cluster test",
                )
                response = self.client.post(
                    "/get_slaves",
                    data={
                        "cluster": "non_existent_cluster",
                    },
                )
                self.assertEqual(
                    "failed",
                    response.data.decode(),
                    msg="get_slave request for invalid cluster",
                )

                response = self.client.get("/sign_out")

                # Logged in as admin
                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                response = self.client.post(
                    "/get_slaves",
                    data={
                        "cluster": "cluster_test",
                    },
                )
                self.assertEqual(
                    set(["slave1", "slave2"]),
                    set(json.loads(response.data.decode())),
                    msg="slave list of cluster test",
                )

    def test_get_cluster_backtest(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    from app.strat_add import strat_add_views

                    def mock_get_file_data_from_minio(path: str):
                        file = open("./tests/test_data/cluster_backtest.csv", "rb")
                        data = file.read()
                        file.close()
                        return data

                    strat_add_views.get_file_data_from_minio = (
                        mock_get_file_data_from_minio
                    )

                    def mock_upload_dataframe_to_minio(
                        path: str, dataframe: pd.DataFrame
                    ):
                        self.assertEqual(
                            ["user", "cluster", "slaves", "state"],
                            dataframe.columns.tolist(),
                            "dataframe recieved is invalid",
                        )

                    strat_add_views.upload_dataframe_to_minio = (
                        mock_upload_dataframe_to_minio
                    )

                    def mock_check_object_exists(prefix: str):
                        return True

                    strat_add_views.check_object_exists = mock_check_object_exists

                    self.add_strategy(
                        strategy_name="cluster_test",
                        developer="test_manager",
                        segment="FUTSTK",
                        exchange_name="NSE",
                        backtest_start_date=pd.Timestamp(2018, 1, 1),
                        book_long="LC3",
                        book_short="SC1",
                        strat_state=2,
                        submission_day=datetime.datetime.now(),
                        trigger_coeff=100,
                        limit_coeff=156,
                        expiration_time=datetime.time(14, 45),
                        long_short=-1,
                        cluster_mapping=["cluster_kailash"],
                        comments="uses multiple indicators",
                    )

                    # Logged in as developer
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_developer, "password": self.password},
                    )

                    response = self.client.get(
                        "/get_cluster_backtest",
                    )
                    template, context = templates[0]
                    self.assertEqual(template.name, "strat_add/cluster_backtest.html")
                    self.assertEqual([], context["cluster_backtest_dict"])
                    response = self.client.get("/sign_out")

                    # Logged in as manager
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_manager, "password": self.password},
                    )
                    response = self.client.get(
                        "/get_cluster_backtest",
                    )
                    template, context = templates[1]
                    self.assertEqual(
                        [
                            {
                                "user": "test_manager",
                                "cluster": "cluster_test",
                                "slaves": "slave1:slave2",
                                "state": 0,
                            }
                        ],
                        context["cluster_backtest_dict"],
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as admin
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )
                    response = self.client.get(
                        "/get_cluster_backtest",
                    )
                    template, context = templates[2]
                    self.assertEqual(200, response.status_code)
                    self.assertEqual(
                        [
                            {
                                "user": "test_manager",
                                "cluster": "cluster_test",
                                "slaves": "slave1:slave2",
                                "state": 0,
                            }
                        ],
                        context["cluster_backtest_dict"],
                    )

    def test_post_cluster_backtest(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    from app.strat_add import strat_add_views

                    def mock_get_file_data_from_minio(path: str):
                        file = open("./tests/test_data/cluster_backtest.csv", "rb")
                        data = file.read()
                        file.close()
                        return data

                    strat_add_views.get_file_data_from_minio = (
                        mock_get_file_data_from_minio
                    )

                    def mock_upload_dataframe_to_minio(
                        path: str, dataframe: pd.DataFrame
                    ):
                        self.assertEqual(
                            ["user", "cluster", "slaves", "state"],
                            dataframe.columns.tolist(),
                            "dataframe recieved is invalid",
                        )

                    strat_add_views.upload_dataframe_to_minio = (
                        mock_upload_dataframe_to_minio
                    )

                    def mock_check_object_exists(prefix: str):
                        return True

                    strat_add_views.check_object_exists = mock_check_object_exists

                    self.add_strategy(
                        strategy_name="cluster_test",
                        developer="test_manager",
                        segment="FUTSTK",
                        exchange_name="NSE",
                        backtest_start_date=pd.Timestamp(2018, 1, 1),
                        book_long="LC3",
                        book_short="SC1",
                        strat_state=2,
                        submission_day=datetime.datetime.now(),
                        trigger_coeff=100,
                        limit_coeff=156,
                        expiration_time=datetime.time(14, 45),
                        long_short=-1,
                        cluster_mapping=["cluster_kailash"],
                        comments="uses multiple indicators",
                    )

                    self.add_strategy(
                        strategy_name="cluster_restart",
                        developer="test_admin",
                        segment="OPTSTK",
                        exchange_name="NSE",
                        backtest_start_date=pd.Timestamp(2018, 1, 1),
                        book_long="LC3",
                        book_short="SC1",
                        strat_state=2,
                        submission_day=datetime.datetime.now(),
                        trigger_coeff=100,
                        limit_coeff=156,
                        expiration_time=datetime.time(14, 45),
                        long_short=-1,
                        cluster_mapping=["cluster_kailash"],
                        comments="uses multiple indicators",
                    )

                    # Logged in as developer
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_developer, "password": self.password},
                    )

                    response = self.client.post(
                        "/post_cluster_backtest",
                        data={
                            "cluster": "non_existent_cluster",
                            "slaves": "slave1:slave2",
                        },
                        follow_redirects=True,
                    )
                    template, context = templates[0]
                    self.assertEqual([], context["cluster_backtest_dict"])

                    response = self.client.get("/sign_out")

                    # Logged in as manager
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_manager, "password": self.password},
                    )
                    response = self.client.post(
                        "/post_cluster_backtest",
                        data={"cluster": "", "slaves": "", "submit": True},
                        follow_redirects=True,
                    )
                    self.assertIn("Invalid Form Data", response.data.decode())

                    response = self.client.post(
                        "/post_cluster_backtest",
                        data={"cluster": "cluster_test", "slaves": "", "submit": True},
                        follow_redirects=True,
                    )
                    self.assertIn(
                        "Please select atleast one slave", response.data.decode()
                    )

                    response = self.client.post(
                        "/post_cluster_backtest",
                        data={
                            "cluster": "cluster_test",
                            "slaves": "slave1:slave2",
                            "submit": True,
                        },
                        follow_redirects=True,
                    )
                    template, context = templates[1]
                    self.assertEqual(
                        [
                            {
                                "user": "test_manager",
                                "cluster": "cluster_test",
                                "slaves": "slave1:slave2",
                                "state": 0,
                            }
                        ],
                        context["cluster_backtest_dict"],
                    )

                    response = self.client.get("/sign_out")

                    # Logged in as admin
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )
                    response = self.client.post(
                        "/post_cluster_backtest",
                        data={
                            "cluster": "cluster_test",
                            "slaves": "slave1:slave2",
                            "submit": True,
                        },
                        follow_redirects=True,
                    )
                    template, context = templates[2]
                    self.assertEqual(
                        [
                            {
                                "user": "test_manager",
                                "cluster": "cluster_test",
                                "slaves": "slave1:slave2",
                                "state": 0,
                            }
                        ],
                        context["cluster_backtest_dict"],
                    )

                    response = self.client.post(
                        "/post_cluster_backtest",
                        data={
                            "cluster": "cluster_restart",
                            "slaves": "slave1:slave2",
                            "is_restart": True,
                            "submit": True,
                        },
                    )
                    self.assertNotEqual(
                        "failed",
                        response,
                        "custom cluster backtest restart request for failed backtest",
                    )

                    response = self.client.post(
                        "/post_cluster_backtest",
                        data={
                            "cluster": "cluster_restart",
                            "slaves": "slave1",
                            "is_restart": True,
                            "submit": True,
                        },
                    )
                    self.assertEqual(
                        b"failed",
                        response.data,
                        "custom cluster backtest restart request for completed backtest",
                    )

                    response = self.client.post(
                        "/post_cluster_backtest",
                        data={
                            "cluster": "cluster_restart",
                            "slaves": "slave2",
                            "is_restart": True,
                            "submit": True,
                        },
                    )
                    self.assertEqual(
                        b"failed",
                        response.data,
                        "custom cluster backtest restart request for pending backtest",
                    )

    def test_delete_cluster_backtest(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                from app.strat_add import strat_add_views

                def mock_get_file_data_from_minio(path: str):
                    file = open("./tests/test_data/cluster_backtest.csv", "rb")
                    data = file.read()
                    file.close()
                    return data

                strat_add_views.get_file_data_from_minio = mock_get_file_data_from_minio

                def mock_upload_dataframe_to_minio(path: str, dataframe: pd.DataFrame):
                    self.assertEqual(
                        ["user", "cluster", "slaves", "state"],
                        dataframe.columns.tolist(),
                        "dataframe recieved is invalid",
                    )

                strat_add_views.upload_dataframe_to_minio = (
                    mock_upload_dataframe_to_minio
                )

                def mock_check_object_exists(prefix: str):
                    return True

                strat_add_views.check_object_exists = mock_check_object_exists

                def mock_delete_file_from_minio(path: str):
                    pass

                strat_add_views.delete_file_from_minio = mock_delete_file_from_minio

                self.add_strategy(
                    strategy_name="cluster_test",
                    developer="test_manager",
                    segment="FUTSTK",
                    exchange_name="NSE",
                    backtest_start_date=pd.Timestamp(2018, 1, 1),
                    book_long="LC3",
                    book_short="SC1",
                    strat_state=2,
                    submission_day=datetime.datetime.now(),
                    trigger_coeff=100,
                    limit_coeff=156,
                    expiration_time=datetime.time(14, 45),
                    long_short=-1,
                    cluster_mapping=["cluster_kailash"],
                    comments="uses multiple indicators",
                )

                # Logged in as developer
                response = self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                response = self.client.post(
                    "/delete_cluster_backtest",
                    data={
                        "cluster": "non_existent_cluster",
                        "slaves": "slave1",
                    },
                )
                self.assertEqual(
                    b"failed",
                    response.data,
                    msg="Attempt to delete request non existent cluster",
                )
                response = self.client.post(
                    "/delete_cluster_backtest",
                    data={"cluster": "cluster_test", "slaves": "slave1:slave2"},
                )
                self.assertEqual(
                    b"failed",
                    response.data,
                    msg="Unauthorized attempt to delete request",
                )

                response = self.client.get("/sign_out")

                # Logged in as manager
                response = self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )
                response = self.client.post(
                    "/delete_cluster_backtest",
                    data={
                        "cluster": "cluster_test",
                        "slaves": "slave1:slave2",
                    },
                )
                self.assertEqual(
                    b"success",
                    response.data,
                )

                response = self.client.get("/sign_out")

                # Logged in as admin
                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                response = self.client.post(
                    "/delete_cluster_backtest",
                    data={
                        "cluster": "cluster_test",
                        "slaves": "slave1:slave2",
                    },
                )
                self.assertEqual(
                    b"success",
                    response.data,
                )

    def test_custom_cluster_performance_metrics(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:

                    def mock_get_file_data_from_minio(path: str):
                        if "csv" in path:
                            file = open(
                                "./tests/test_data/cluster_backtest.csv",
                                "rb",
                            )
                        else:
                            file = open(
                                "./tests/test_data/custom_cluster_performance_backtest_results.html",
                                "rb",
                            )
                        data = file.read()
                        file.close()
                        return data

                    strat_add_views.get_file_data_from_minio = (
                        mock_get_file_data_from_minio
                    )

                    def mock_check_object_exists(prefix: str):
                        return True

                    strat_add_views.check_object_exists = mock_check_object_exists

                    self.add_strategy(
                        strategy_name="cluster_test",
                        developer="test_manager",
                        segment="FUTSTK",
                        exchange_name="NSE",
                        backtest_start_date=pd.Timestamp(2018, 1, 1),
                        book_long="LC3",
                        book_short="SC1",
                        strat_state=2,
                        submission_day=datetime.datetime.now(),
                        trigger_coeff=100,
                        limit_coeff=156,
                        expiration_time=datetime.time(14, 45),
                        long_short=-1,
                        cluster_mapping=["cluster_kailash"],
                        comments="uses multiple indicators",
                    )

                    self.add_strategy(
                        strategy_name="cluster_dead",
                        developer="test_manager",
                        segment="FUTSTK",
                        exchange_name="NSE",
                        backtest_start_date=pd.Timestamp(2018, 1, 1),
                        book_long="LC3",
                        book_short="SC1",
                        strat_state=3,
                        submission_day=datetime.datetime.now(),
                        trigger_coeff=100,
                        limit_coeff=156,
                        expiration_time=datetime.time(14, 45),
                        long_short=-1,
                        cluster_mapping=["cluster_kailash"],
                        comments="uses multiple indicators",
                    )

                    # Logged in as developer
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_developer, "password": self.password},
                    )
                    response = self.client.get(
                        "/custom_cluster_performance/cluster_test/slave1:slave2"
                    )
                    self.assertEqual(
                        response.status_code, 403, msg=self.message_response_code
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as manager
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_manager, "password": self.password},
                    )
                    response = self.client.get(
                        "/custom_cluster_performance/cluster_test/slave1:slave2"
                    )
                    template, context = templates[0]
                    self.assertTrue(
                        template.name == "strat_add/performance_metric.html",
                        msg=self.message_template,
                    )
                    self.assertTrue(
                        "performance" in context,
                        msg="cluster performance file doesn't match for manager",
                    )
                    self.assertIn(
                        "<html>",
                        context["performance"],
                        "Invalid cluster performance file for manager",
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as admin
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )
                    response = self.client.get(
                        "/custom_cluster_performance/cluster_test/slave1:slave2"
                    )
                    template, context = templates[1]
                    self.assertTrue(
                        "performance" in context,
                        msg="cluster performance file doesn't match for admin",
                    )
                    self.assertIn(
                        "html",
                        context["performance"],
                        "Invalid cluster performance file for admin",
                    )
                    response = self.client.get(
                        "/custom_cluster_performance/non_existent_cluster/slave1"
                    )
                    self.assertEqual(
                        response.status_code, 404, msg=self.message_response_code
                    )
                    with self.assertRaises(ValueError) as context:
                        self.client.get(
                            "/custom_cluster_performance/test_strat_1/slave1"
                        )
                        self.assertEqual(str(context.exception), "Invalid Cluster")
                    with self.assertRaises(ValueError) as context:
                        self.client.get(
                            "/custom_cluster_performance/cluster_dead/slave1"
                        )
                        self.assertEqual(str(context.exception), "Invalid Cluster")

    def test_check_var_report(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                message = "var report checking failed for {}!"
                # Logged in as developer
                response = self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                response = self.client.post(
                    "/strat_expand/var_report",
                    data={"strategy": "test_strat_3", "week": "current"},
                )
                self.assertEqual(
                    b"failed", response.data, msg=message.format("developer")
                )
                Strategy.query.filter_by(
                    strategy_name="test_strat_3"
                ).first().strat_state = 2
                db.session.commit()
                response = self.client.post(
                    "/strat_expand/var_report",
                    data={"strategy": "test_strat_3", "week": "current"},
                )
                self.assertEqual(
                    b"success", response.data, msg=message.format("developer")
                )

                # Logged in as manager
                response = self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )
                response = self.client.post(
                    "/strat_expand/var_report",
                    data={"strategy": "test_strat_1", "week": "current"},
                )
                self.assertEqual(
                    b"failed", response.data, msg=message.format("manager")
                )
                response = self.client.get("/sign_out")

                # Logged in as admin
                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                response = self.client.post(
                    "/strat_expand/var_report",
                    data={"strategy": "test_strat_1", "week": "current"},
                )
                self.assertEqual(b"success", response.data, msg=message.format("admin"))

                Strategy.query.filter_by(
                    strategy_name="test_strat_2"
                ).first().strat_state = 3
                db.session.commit()
                response = self.client.post(
                    "/strat_expand/var_report",
                    data={"strategy": "test_strat_2", "week": "current"},
                )
                self.assertEqual(b"success", response.data, msg=message.format("admin"))
                response = self.client.post(
                    "/strat_expand/var_report",
                    data={"strategy": "test_non_existent", "week": "current"},
                )
                self.assertEqual(
                    b"failed", response.data, msg="non existent strategy passed!"
                )

    def test_show_var_report(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    # Logged in as developer
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_developer, "password": self.password},
                    )
                    response = self.client.get(
                        "/strat_expand/var_report/test_strat_3/current"
                    )
                    self.assertEqual(
                        response.status_code, 403, msg=self.message_response_code
                    )
                    Strategy.query.filter_by(
                        strategy_name="test_strat_3"
                    ).first().strat_state = 2
                    db.session.commit()
                    message = "var report does not match for {}!"
                    response = self.client.get(
                        "/strat_expand/var_report/test_strat_3/current"
                    )
                    template, context = templates[0]
                    self.assertTrue(
                        "report" in context, msg=message.format("developer")
                    )
                    self.assertTrue(
                        template.name == "strat_add/report.html",
                        msg=self.message_template,
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as manager
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_manager, "password": self.password},
                    )
                    response = self.client.get(
                        "/strat_expand/var_report/test_strat_2/current"
                    )
                    template, context = templates[1]
                    self.assertTrue("report" in context, msg=message.format("manager"))
                    self.assertTrue(
                        template.name == "strat_add/report.html",
                        msg=self.message_template,
                    )

                    response = self.client.get(
                        "/strat_expand/var_report/test_strat_1/current"
                    )
                    self.assertEqual(
                        response.status_code, 403, msg=self.message_response_code
                    )
                    response = self.client.get("/sign_out")

                    # Logged in as manager
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )
                    response = self.client.get(
                        "/strat_expand/var_report/test_strat_1/current"
                    )
                    template, context = templates[2]
                    self.assertTrue("report" in context, msg=message.format("admin"))
                    self.assertTrue(
                        template.name == "strat_add/report.html",
                        msg=self.message_template,
                    )
                    response = self.client.get(
                        "/strat_expand/var_report/test_non_existent/current"
                    )
                    self.assertEqual(
                        response.status_code, 404, msg=self.message_response_code
                    )
                    with self.assertRaises(Exception) as context:
                        self.client.get(
                            "/strat_expand/var_report/test_strat_1/2023_10_20"
                        )
                        self.assertEqual(str(context.exception), "File not found!")
                    response = self.client.get(
                        "/strat_expand/var_report/test_strat_1/10-Oct-2023"
                    )
                    template, context = templates[3]
                    self.assertTrue("report" in context)
                    self.assertTrue(template.name == "strat_add/report.html")
                    self.assertEqual(response.status_code, 200)
                    response = self.client.get("/sign_out")

    def test_policy_page(self) -> None:
        with self.app.app_context():
            from app.strat_add import strat_add_views

            def mock_get_file_data_from_minio(path: str):
                file = open(f"./tests/test_data/{path}", "rb")
                data = file.read()
                file.close()
                return data

            strat_add_views.get_file_data_from_minio = mock_get_file_data_from_minio

            def mock_upload_dataframe_to_minio(path: str, dataframe: pd.DataFrame):
                self.assertEqual(
                    ["path", "title"],
                    dataframe.columns.tolist(),
                    "dataframe recieved is invalid",
                )

            strat_add_views.upload_dataframe_to_minio = mock_upload_dataframe_to_minio

            def mock_check_object_exists(prefix: str):
                return True

            strat_add_views.check_object_exists = mock_check_object_exists

            def mock_get_all_md_files(path, folder_name):
                return {"policies/policy2.md": ("Policy1", b"# POLICY1")}

            strat_add_views.get_all_md_files = mock_get_all_md_files

            def mock_update_content_on_minio(path, content):
                pass

            strat_add_views.update_content_on_minio = mock_update_content_on_minio

            # Logged in as developer
            response = self.client.post(
                "/login",
                data={"email": self.email_developer, "password": self.password},
            )

            response = self.client.get("/policy")
            self.assertEqual(response.status_code, 200)
            response = self.client.post(
                "/policy",
                data={
                    "path": "policies/policy2.md",
                    "title": "policy2",
                    "content": "# modified",
                },
            )
            self.assertEqual(response.status_code, 403)

            response = self.client.get("/sign_out")
            # Logged in as admin
            response = self.client.post(
                "/login",
                data={"email": self.email_admin, "password": self.password},
            )
            response = self.client.post(
                "/policy",
                data={
                    "path": "policies/policy2.md",
                    "title": "policy2",
                    "content": "# modified",
                },
            )
            self.assertEqual(response.status_code, 200)

            def mock_check_object_exists(prefix: str):
                return False

            strat_add_views.check_object_exists = mock_check_object_exists
            response = self.client.post(
                "/policy",
                data={
                    "path": "policies/policy2.md",
                    "title": "policy2",
                    "content": "# modified",
                },
            )
            self.assertEqual(response.status_code, 200)

    def test_html_to_markdown(self) -> None:
        with self.app.app_context():

            def mock_get_file_data_from_minio(path: str):
                file = open(f"./tests/test_data/{path}", "rb")
                data = file.read()
                file.close()
                return data

            strat_add_views.get_file_data_from_minio = mock_get_file_data_from_minio
            # Logged in as developer
            response = self.client.post(
                "/login",
                data={"email": self.email_developer, "password": self.password},
            )
            response = self.client.post(
                "/convert_to_md_format",
                data={"path": "policies/policy2.md"},
            )
            self.assertEqual(response.status_code, 200)
            response = self.client.get("/sign_out")

    def test_fetch_and_upload_html_file(self) -> None:
        with self.app.app_context():
            from app.strat_add import strat_add_views

            def mock_get_file_data_from_minio(path: str):
                file = open(f"./tests/test_data/{path}", "rb")
                data = file.read()
                file.close()
                return data

            def mock_upload_html_file_to_minio(path, file):
                pass

            def mock_check_object_exists(prefix):
                return True

            strat_add_views.get_file_data_from_minio = mock_get_file_data_from_minio
            strat_add_views.upload_html_file_on_minio = mock_upload_html_file_to_minio
            strat_add_views.check_object_exists = mock_check_object_exists

            # Logged in as developer
            response = self.client.post(
                "/login",
                data={"email": self.email_developer, "password": self.password},
            )
            response = self.client.post(
                "/fetch_html_file",
                data={"path": "new_areas_to_work/title1/test.html"},
            )
            self.assertEqual(response.status_code, 200)
            self.assertTrue("dummy" in response.data.decode())
            response = self.client.get("/sign_out")
            response = self.client.post(
                "/login",
                data={"email": self.email_developer, "password": self.password},
            )
            response = self.client.post(
                "/upload_html_files",
                data={"path": "new_areas_to_work/title1/title1.md", "files": ""},
            )
            self.assertEqual(response.status_code, 403)
            response = self.client.post(
                "/delete_html_file",
                data={"path": "new_areas_to_work/title1/title1.md"},
            )
            self.assertEqual(response.status_code, 403)
            response = self.client.get("/sign_out")
            response = self.client.post(
                "/login",
                data={"email": self.email_admin, "password": self.password},
            )
            response = self.client.post(
                "/upload_html_files",
                data={"path": "new_areas_to_work/title1/title1.md", "files": ""},
            )
            self.assertEqual(response.status_code, 200)

    def test_delete_md_file(self) -> None:
        with self.app.app_context():
            from app.strat_add import strat_add_views

            def mock_get_file_data_from_minio(path: str):
                file = open(f"./tests/test_data/{path}", "rb")
                data = file.read()
                file.close()
                return data

            strat_add_views.get_file_data_from_minio = mock_get_file_data_from_minio

            def mock_upload_dataframe_to_minio(path: str, dataframe: pd.DataFrame):
                self.assertEqual(
                    ["path", "title"],
                    dataframe.columns.tolist(),
                    "dataframe recieved is invalid",
                )

            strat_add_views.upload_dataframe_to_minio = mock_upload_dataframe_to_minio

            def mock_check_object_exists(prefix: str):
                return True

            strat_add_views.check_object_exists = mock_check_object_exists

            def mock_delete_file_from_minio(path: str):
                pass

            strat_add_views.delete_file_from_minio = mock_delete_file_from_minio

            # Logged in as developer
            response = self.client.post(
                "/login",
                data={"email": self.email_developer, "password": self.password},
            )
            response = self.client.post(
                "/delete_md_file",
                data={"path": "policies/policy2.md", "type": "policy"},
            )
            self.assertEqual(response.status_code, 403)

            response = self.client.get("/sign_out")

            # Logged in as manager
            response = self.client.post(
                "/login",
                data={"email": self.email_manager, "password": self.password},
            )
            response = self.client.post(
                "/delete_md_file",
                data={"path": "policies/policy2.md", "type": "policy"},
            )
            self.assertEqual(response.status_code, 403)

            response = self.client.get("/sign_out")

            # Logged in as admin
            response = self.client.post(
                "/login",
                data={"email": self.email_admin, "password": self.password},
            )
            response = self.client.post(
                "/delete_md_file",
                data={"path": "policies/policy2.md", "type": "policy"},
            )
            self.assertEqual(response.status_code, 200)

            def mock_upload_dataframe_to_minio(path: str, dataframe: pd.DataFrame):
                self.assertEqual(
                    ["path", "title", "description"],
                    dataframe.columns.tolist(),
                    "dataframe recieved is invalid",
                )

            strat_add_views.upload_dataframe_to_minio = mock_upload_dataframe_to_minio

            response = self.client.post(
                "/delete_md_file",
                data={"path": "new_areas_to_work/title2/title2.md", "type": "new_area"},
            )
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.data, b"success")
            response = self.client.post(
                "/delete_md_file",
                data={"path": "new_areas_to_work/title3/title3.md", "type": "new_area"},
            )
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.data, b"empty")

            def mock_check_object_exists(prefix: str):
                return False

            strat_add_views.check_object_exists = mock_check_object_exists
            response = self.client.post(
                "/delete_md_file",
                data={"path": "new_areas_to_work/title2/title2.md", "type": "new_area"},
            )
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.data, b"failed")

    def test_reset_strategy(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():

                def mock_get_file_data_from_minio(path: str):
                    file = open(
                        "./tests/test_data/reworked_strategies_NSE.csv",
                        "rb",
                    )
                    data = file.read()
                    file.close()
                    return data

                strat_add_views.get_file_data_from_minio = mock_get_file_data_from_minio

                def mock_check_object_exists(prefix: str):
                    if "reworked_strategies" in prefix and "NSE" in prefix:
                        return True
                    else:
                        return False

                strat_add_views.check_object_exists = mock_check_object_exists

                def mock_update_content_on_minio(path, content):
                    pass

                strat_add_views.update_content_on_minio = mock_update_content_on_minio

                Strategy.query.filter_by(
                    strategy_name="test_strat_2"
                ).first().strat_state = 2

                def mock_get_strats_not_in_test_env():
                    return [
                        "test_strat_2"
                    ]  # Assuming test_start_2 has a newer rework running in test mode

                strat_add_views.get_strats_not_in_test_env = (
                    mock_get_strats_not_in_test_env
                )

                self.add_strategy(
                    strategy_name="test_strat_nse",
                    developer="test_developer",
                    segment="CASH",
                    exchange_name="NSE",
                    backtest_start_date=pd.Timestamp(2011, 1, 1),
                    book_long="LC1",
                    book_short="SC1",
                    strat_state=2,
                    submission_day=datetime.datetime.now(),
                    trigger_coeff=100,
                    limit_coeff=156,
                    expiration_time=datetime.time(15, 15),
                    long_short=-1,
                    cluster_mapping=["cluster_test", "cluster_test_2"],
                )
                self.add_strategy(
                    strategy_name="top_level_strategy",
                    developer="test_developer",
                    segment="CASH",
                    exchange_name="NSE",
                    backtest_start_date=pd.Timestamp(2011, 1, 1),
                    book_long="LC1",
                    book_short="SC1",
                    strat_state=2,
                    submission_day=datetime.datetime.now(),
                    trigger_coeff=100,
                    limit_coeff=156,
                    expiration_time=datetime.time(15, 15),
                    long_short=-1,
                )
                # Logged in as developer
                response = self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                ## Test for invalid environment
                response = self.client.post(
                    "/reset_strategy",
                    data={
                        "strategy": "test_strat_nse",
                        "action": "reset",
                        "environment": "INVALID",
                    },
                )
                self.assertEqual(
                    b"invalid environment",
                    response.data,
                    msg="Invalid environment should return appropriate error",
                )
                ## Test for reseting/unreseting cluster strategy
                response = self.client.post(
                    "/reset_strategy",
                    data={
                        "strategy": "cluster_strategy",
                        "action": "reset",
                        "environment": "LIVE",
                    },
                )
                self.assertEqual(
                    b"Resetting/Unresetting not allowed for cluster strategies",
                    response.data,
                    msg="Cluster strategy reset restriction not enforced",
                )
                ## Test for reseting/unreseting non-existent strategy
                response = self.client.post(
                    "/reset_strategy",
                    data={
                        "strategy": "non_existent_strat",
                        "action": "reset",
                        "environment": "LIVE",
                    },
                )
                self.assertEqual(
                    b"failed",
                    response.data,
                    msg="developer resetting for manager strategy!",
                )
                ## Test for reseting/unreseting top level strategy
                response = self.client.post(
                    "/reset_strategy",
                    data={
                        "strategy": "top_level_strategy",
                        "action": "reset",
                        "environment": "LIVE",
                    },
                )
                self.assertEqual(
                    b"Resetting/Unresetting not allowed for top level strategies",
                    response.data,
                    msg="Resetting top level strategy restriction not enforced",
                )
                ## Test for reseting/unreseting non TEST/LIVE strategy
                response = self.client.post(
                    "/reset_strategy",
                    data={
                        "strategy": "test_strat_1",
                        "action": "reset",
                        "environment": "LIVE",
                    },
                )
                self.assertEqual(
                    b"Resetting/Unresetting allowed only for LIVE/TEST strategies",
                    response.data,
                    msg="Resetting non TEST/LIVE strategy restriction not enforced",
                )
                ## Test for reseting/unreseting old rework running in live and new rework in TEST
                response = self.client.post(
                    "/reset_strategy",
                    data={
                        "strategy": "test_strat_2",
                        "action": "reset",
                        "environment": "TEST",
                    },
                )
                self.assertEqual(
                    b"This Live strategy does not run in the test environment (New rework present in test environment).",
                    response.data,
                    msg="Resetting LIVE strategies not running in test environment restriction not enforced",
                )
                ## Test for reseting/unreseting unauthorised strategies
                response = self.client.post(
                    "/reset_strategy",
                    data={
                        "strategy": "test_reworked",
                        "action": "reset",
                        "environment": "LIVE",
                    },
                )
                self.assertEqual(
                    b"failed",
                    response.data,
                    msg="developer resetting unauthorised strategy!",
                )
                ## Test for a valid reset request
                response = self.client.post(
                    "/reset_strategy",
                    data={
                        "strategy": "test_strat_nse",
                        "action": "reset",
                        "environment": "TEST",
                    },
                )
                self.assertEqual(
                    b"success",
                    response.data,
                )
                ## Test for duplicate request
                Strategy.query.filter_by(
                    strategy_name="test_strat_3"
                ).first().strat_state = 2
                response = self.client.post(
                    "/reset_strategy",
                    data={
                        "strategy": "test_strat_3",
                        "action": "reset",
                        "environment": "LIVE",
                    },
                )
                self.assertEqual(
                    b"duplicate_strategy",
                    response.data,
                    msg="strategy already set for reset",
                )
                ## Test for valid unreset request
                response = self.client.post(
                    "/reset_strategy",
                    data={
                        "strategy": "test_strat_3",
                        "action": "unreset",
                        "environment": "LIVE",
                    },
                )
                self.assertEqual(
                    b"success",
                    response.data,
                )
                ## Test for unsetting strategy which is not set for reset
                response = self.client.post(
                    "/reset_strategy",
                    data={
                        "strategy": "test_strat_nse",
                        "action": "unreset",
                        "environment": "LIVE",
                    },
                )
                self.assertEqual(
                    b"empty_strategy",
                    response.data,
                    msg="unresetting strategy not set for reset",
                )
                ## Test for invalid action
                response = self.client.post(
                    "/reset_strategy",
                    data={
                        "strategy": "test_strat_nse",
                        "action": "invalid_action",
                        "environment": "LIVE",
                    },
                )
                self.assertEqual(
                    b"invalid/unrecognised action: invalid_action",
                    response.data,
                    msg="invalid action restriction not enforced",
                )
                response = self.client.get("/sign_out")

                # Logged in as manager
                response = self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )
                ## Test for resetting/unresetting unauthorized strategy
                Strategy.query.filter_by(
                    strategy_name="test_strat_1"
                ).first().strat_state = 2
                response = self.client.post(
                    "/reset_strategy",
                    data={
                        "strategy": "test_strat_1",
                        "action": "reset",
                        "environment": "LIVE",
                    },
                )
                self.assertEqual(
                    b"failed",
                    response.data,
                    msg="manager resetting admin strategy!",
                )
                ## Test for valid authorised request
                response = self.client.post(
                    "/reset_strategy",
                    data={
                        "strategy": "test_strat_2",
                        "action": "reset",
                        "environment": "LIVE",
                    },
                )
                self.assertEqual(
                    b"success",
                    response.data,
                    msg="manager resetting strategy",
                )
                ## Test for valid authorised request
                response = self.client.post(
                    "/reset_strategy",
                    data={
                        "strategy": "test_strat_nse",
                        "action": "reset",
                        "environment": "LIVE",
                    },
                )
                self.assertEqual(
                    b"success",
                    response.data,
                    msg="manager unresetting for developer strategy!",
                )

                response = self.client.get("/sign_out")

                # Logged in as admin
                response = self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                ## Test for valid authorised request
                response = self.client.post(
                    "/reset_strategy",
                    data={
                        "strategy": "test_strat_1",
                        "action": "reset",
                        "environment": "LIVE",
                    },
                )
                self.assertEqual(
                    b"success",
                    response.data,
                    msg="admin resetting strategy",
                )
                ## Test for valid authorised request
                response = self.client.post(
                    "/reset_strategy",
                    data={
                        "strategy": "test_strat_nse",
                        "action": "reset",
                        "environment": "LIVE",
                    },
                )
                self.assertEqual(
                    b"success",
                    response.data,
                    msg="admin resetting strategy developer strategy",
                )

                response = self.client.get("/sign_out")

    @patch("app.utility.risk_util.update_risk_limits")
    @patch("app.utility.risk_util.fetch_risk_limits")
    def test_risk_management_get(self, mock_fetch_limits, mock_update_limits) -> None:
        mock_fetch_limits.return_value = (
            pd.DataFrame(
                {
                    "variable_name": [
                        "OptidxSellLimit",
                        "OptidxBuyLimit",
                        "OptstkSellLimit",
                        "OptstkBuyLimit",
                        "FutidxLimit",
                        "FutstkLimit",
                        "CashLimit",
                    ],
                    "variable_value": [500, 1000, 1500, 2000, 2500, 3000, 3500],
                }
            ),
            None,
        )
        mock_update_limits.return_value = None
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    # Logged in as developer
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_developer, "password": self.password},
                    )
                    response = self.client.get("/risk_management")
                    self.assertEqual(response.status_code, 403)
                    response = self.client.get("/sign_out")

                    # Logged in as manager
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_manager, "password": self.password},
                    )
                    response = self.client.get("/risk_management")
                    self.assertEqual(response.status_code, 403)
                    response = self.client.get("/sign_out")

                    # Logged in as admin
                    response = self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )
                    response = self.client.get("/risk_management")
                    template, context = templates[0]
                    self.assertEqual("strat_add/risk_management.html", template.name)
                    self.assertIn("form", context)
                    self.assertIn("current_limits", context)
                    response = self.client.get("/sign_out")

    @patch("app.utility.risk_util.update_risk_limits")
    @patch("app.utility.risk_util.fetch_risk_limits")
    def test_risk_management_post_valid(
        self, mock_fetch_limits, mock_update_limits
    ) -> None:
        mock_fetch_limits.return_value = (
            pd.DataFrame(
                {
                    "variable_name": [
                        "OptidxSellLimit",
                        "OptidxBuyLimit",
                        "OptstkSellLimit",
                        "OptstkBuyLimit",
                        "FutidxLimit",
                        "FutstkLimit",
                        "CashLimit",
                    ],
                    "variable_value": [500, 1000, 1500, 2000, 2500, 3000, 3500],
                }
            ),
            None,
        )
        mock_update_limits.return_value = None
        with self.app.app_context():
            with self.app.test_request_context():
                self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                response = self.client.post(
                    "/risk_management",
                    data={
                        "optidx_sell_limit": 1000,
                        "optidx_buy_limit": 1500,
                        "optstk_sell_limit": 2000,
                        "optstk_buy_limit": 2500,
                        "futidx_limit": 3000,
                        "futstk_limit": 3500,
                        "cash_limit": 4000,
                    },
                )
                self.assertEqual(response.status_code, 200)
                response = self.client.get("/sign_out")

    @patch("app.utility.risk_util.update_risk_limits")
    @patch("app.utility.risk_util.fetch_risk_limits")
    def test_risk_management_post_invalid(
        self, mock_fetch_limits, mock_update_limits
    ) -> None:
        mock_fetch_limits.return_value = (
            pd.DataFrame(
                {
                    "variable_name": [
                        "OptidxSellLimit",
                        "OptidxBuyLimit",
                        "OptstkSellLimit",
                        "OptstkBuyLimit",
                        "FutidxLimit",
                        "FutstkLimit",
                        "CashLimit",
                    ],
                    "variable_value": [500, 1000, 1500, 2000, 2500, 3000, 3500],
                }
            ),
            None,
        )
        mock_update_limits.return_value = None
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    self.client.post(
                        "/login",
                        data={"email": self.email_admin, "password": self.password},
                    )
                    # Invalid data: negative limit
                    self.client.post(
                        "/risk_management",
                        data={
                            "optidx_sell_limit": -1000,
                            "optidx_buy_limit": 1500,
                            "optstk_sell_limit": 1600,
                            "optstk_buy_limit": 2500,
                            "futidx_limit": 3000,
                            "futstk_limit": 3500,
                            "cash_limit": 4000,
                        },
                    )
                    template, context = templates[0]
                    self.assertEqual("strat_add/risk_management.html", template.name)
                    self.assertTrue(context["form"].errors)
                    # Invalid data: not an integer
                    self.client.post(
                        "/risk_management",
                        data={
                            "optidx_sell_limit": 1000,
                            "optidx_buy_limit": 1500,
                            "optstk_sell_limit": "invalid",
                            "optstk_buy_limit": 2500,
                            "futidx_limit": 3000,
                            "futstk_limit": 3500,
                            "cash_limit": 4000,
                        },
                    )
                    template, context = templates[1]
                    self.assertEqual("strat_add/risk_management.html", template.name)
                    self.assertTrue(context["form"].errors)
                    self.client.get("/sign_out")

    @patch("app.strat_add.strat_add_views.fetch_deleted_tradelogs_from_minio")
    @patch("app.strat_add.strat_add_views.add_deleted_tradelogs_on_minio")
    def test_delete_tradelog_page(
        self,
        mock_add_deleted_tradelogs_on_minio,
        mock_fetch_deleted_tradelogs_from_minio,
    ) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                with captured_templates(self.app) as templates:
                    strat = Strategy.query.filter_by(
                        strategy_name="test_strat_3"
                    ).first()
                    old_state = strat.strat_state
                    strat.strat_state = 2
                    db.session.commit()

                    self.client.post(
                        "/login",
                        data={"email": self.email_developer, "password": self.password},
                    )

                    response = self.client.get("/delete_tradelog")
                    self.assertEqual(response.status_code, 200)
                    template, context = templates[0]
                    self.assertEqual(template.name, "strat_add/delete_tradelog.html")

                    form = context["form"]
                    self.assertIn(
                        "test_strat_3", [choice for choice in form.strategies.choices]
                    )

                    form_data = {"strategies": ["test_strat_3"]}
                    response = self.client.post("/delete_tradelog", data=form_data)

                    self.assertEqual(response.status_code, 200)
                    mock_add_deleted_tradelogs_on_minio.assert_called_with(
                        strategies_to_add=["test_strat_3"],
                        accessible_strategies=form.strategies.choices,
                    )

                    strat.strat_state = old_state
                    db.session.commit()

                    self.client.post("/sign_out")

                    self.client.post(
                        "/login",
                        data={"email": "email_unauthorized", "password": self.password},
                    )
                    response = self.client.post("/delete_tradelog", data=form_data)
                    self.assertEqual(response.status_code, 403)

                    self.client.get("/sign_out")


if __name__ == "__main__":
    unittest.main()
