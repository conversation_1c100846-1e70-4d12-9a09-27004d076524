import datetime as dt
import numpy as np
import pandas as pd
from balte import indicator
from balte.strategy import Strategy
import datetime
import pdb


class mexico(Strategy):
    def __init__(self):
        super(mexico, self).__init__()  # Dont change
        self.start_date = pd.Timestamp(
            2017, 1, 1
        )  # YYYY-MM-DD. See pandas doc for more info on timestamps
        self.end_date = pd.Timestamp(2021, 12, 31)
        self.add_timestamps(continuous=("9:35", "15:25"))
        self.data.eq = self.add_data(
            "eq", lookback_initialize=2, lookback_next=2
        )  # Load fno data with no Lookback
        self.data.iseq = self.add_data(
            "iseq", lookback_initialize=2, lookback_next=2
        )  # Load fno data with no Lookback
        self.data.fut_margin = self.add_data(
            "fut_margin", lookback_initialize=2, lookback_next=2
        )
        self.data.opt = self.add_data("opt", lookback_initialize=5, lookback_next=3)

    def initialize(self):
        pass

    def next(self, today, timestamp):
        try:
            curr_close = self.data.eq["Close", timestamp]
            close_15_min = self.data.eq["Close", timestamp - pd.Timedelta(minutes=5)]

            self.opt.option_filter(strike=50000, strike_sign="=")
            self.opt.stock_option_filter()

            pdb.set_trace()

            pending_trades = self.get_pending_entries()

            self.opt.optcom_filter()
            for index, trades in pending_trades.iterrows():
                self.cancel_entry_order(trades.order_id)
                exp_time = datetime.datetime.combine(
                    datetime.datetime.today(), trades.expiration_time
                ) + datetime.timedelta(minutes=50)
                self.order_entry(
                    "eq",
                    trades.ID,
                    -1e5,
                    limit_price=curr_close.loc[today, trades.ID] * 1.005,
                    expiration_time=exp_time.strftime("%H:%M"),
                )

            x = close_15_min.loc[today] / curr_close.loc[today] - 1 > 0.02
            x = indicator.Indicator(x[x == True])
            res = x.filter(self.data.iseq.df.loc[today]).s
            if timestamp.time() <= dt.time(14, 10):
                exp_time = timestamp + pd.Timedelta(minutes=50)
                for x in res.reset_index()["ID"]:
                    self.order_entry(
                        "eq",
                        x,
                        -1e5,
                        limit_price=curr_close.loc[today, x] * 1.005,
                        expiration_time=exp_time.strftime("%H:%M"),
                    )

            if timestamp.time() == dt.time(15, 15):
                self.order_exit_all()
        except:
            print("Error")

        finally:
            print("Run this finally")
