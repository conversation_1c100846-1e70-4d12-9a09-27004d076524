import unittest
from app.models import db
from app.utility.strat_review_util import (
    check_authenticity,
    validate_download_request,
    transfer_to_file_server,
    get_correlation_pending_strats,
    get_correlation_live_strats,
    get_fresh_correlation,
    reject_strategy,
    kill_strategy,
    accept_strategy,
    post_acceptance_process,
    dfs,
    start_downstream_service,
    clean_review,
    is_all_empty_review,
    check_strategy_for_invalid_filters,
    get_all_clusters_in_hierarchy,
    update_cluster_parameters,
)
from app.models import Strategy, StrategyReview
import datetime
import pandas as pd
from pandas.testing import assert_frame_equal
from tests.base_test_fixture import BaseTestFixture
from app.utility import strat_review_util


class TestReviewUtil(BaseTestFixture):
    def setUp(self) -> None:
        super().setUp()
        Strategy.query.filter_by(
            strategy_name="test_strat_2"
        ).first().reworked_strategy = "test_strat_1"
        db.session.commit()
        self.file = None

        def mock_copy_objects_minio(*args, **kwargs):
            pass

        strat_review_util.copy_objects_minio = mock_copy_objects_minio

    def test_correct_masking(self) -> None:
        with self.app.app_context():

            def mock_get_files_from_minio(*args, **kwargs):
                if "cluster_name" not in kwargs.keys():
                    kwargs["cluster_name"] = ""
                file_path = f"./tests/test_data/{kwargs['strategy_name']}{kwargs['cluster_name']}{kwargs['post_fix']}"
                self.app.logger.warning(file_path)
                if self.file:
                    self.file.close()
                self.file = open(file_path, "rb")
                return self.file.read()

            def mock_upload_dataframe_to_minio(*args, **kwargs):
                pass

            strat_review_util.get_files_from_minio = mock_get_files_from_minio
            strat_review_util.upload_dataframe_to_minio = mock_upload_dataframe_to_minio

            pending_result = get_fresh_correlation(
                "demo_strategy",
                pd.Timestamp(2020, 1, 1),
                [("test_strat_1", pd.Timestamp(2020, 1, 2))],
            )
            live_result = get_fresh_correlation(
                "demo_strategy",
                pd.Timestamp(2020, 1, 1),
                [("test_strat_2", pd.Timestamp(2020, 1, 2))],
                refersh_live=True,
            )

            # check if correlation df containes the supplied strategies
            for i in range(2):
                self.app.logger.info(pending_result[i])
                self.assertTrue(pending_result[i].empty)
                self.assertTrue(live_result[i].empty)

    def test_get_fresh_correlation(self) -> None:
        with self.app.app_context():

            def mock_get_files_from_minio(*args, **kwargs):
                if "cluster_name" not in kwargs.keys():
                    kwargs["cluster_name"] = ""
                file_path = f"./tests/test_data/{kwargs['strategy_name']}{kwargs['cluster_name']}{kwargs['post_fix']}"
                file = open(file_path, "rb")
                self.files.append(file)
                return file.read()

            def mock_upload_dataframe_to_minio(*args, **kwargs):
                pass

            def mock_masking(*args, **kwargs):
                return args[0]

            strat_review_util.get_files_from_minio = mock_get_files_from_minio
            strat_review_util.upload_dataframe_to_minio = mock_upload_dataframe_to_minio
            strat_review_util._mask_correlation_df = mock_masking

            correlated_tl = set(
                pd.read_parquet(
                    "./tests/test_data/demo_strategy_correlation_info.parquet"
                ).index
            )
            pending_result = get_fresh_correlation(
                "demo_strategy",
                pd.Timestamp(2020, 1, 1),
                [("test_strat_1", pd.Timestamp(2020, 1, 2))],
            )
            live_result = get_fresh_correlation(
                "demo_strategy",
                pd.Timestamp(2020, 1, 1),
                [("test_strat_2", pd.Timestamp(2020, 1, 2))],
                refersh_live=True,
            )
            # check to ensure one strategy to check for is appended in the correleated_tl
            self.assertEqual(len(correlated_tl) + 1, len(set(live_result[0].index)))

            # check if correlation df containes the supplied strategies
            for i in range(2):
                self.assertTrue(pending_result[i].index.__contains__("test_strat_1"))
                self.assertTrue(live_result[i].index.__contains__("test_strat_2"))

    def test_check_authenticity(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                message = "{} authenticity check failed!"
                # Logged in as developer
                self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                is_authentic = check_authenticity(strategy_list=[])
                self.assertFalse(is_authentic, msg=message.format("developer"))

                self.client.get("/sign_out")

                # Logged in as manager
                self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )
                is_authentic = check_authenticity(strategy_list=["test_strat_3"])
                self.assertTrue(is_authentic, msg=message.format("manager"))

                is_authentic = check_authenticity(strategy_list=["test_strat_1"])
                self.assertFalse(is_authentic, msg=message.format("manager"))

                self.client.get("/sign_out")

                # Logged in as admin
                self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                is_authentic = check_authenticity(
                    strategy_list=["test_strat_2", "test_strat_3"]
                )
                self.assertTrue(is_authentic, msg=message.format("admin"))

    def test_validate_download_request(self) -> None:
        with self.app.app_context():
            private_key_file = open("tests/test_data/private_key.pem", "rb")
            _, isValid = validate_download_request(
                private_key_file=private_key_file,
                strategy_name="test_strat_1",
                status="PENDING",
            )
            private_key_file.close()
            self.assertTrue(isValid, msg="download validation failed!")
            private_key_file = open("tests/test_data/private_key_incorrect.pem", "rb")
            _, isValid = validate_download_request(
                private_key_file=private_key_file,
                strategy_name="test_strat_1",
                status="PENDING",
            )
            private_key_file.close()
            self.assertFalse(isValid, msg="download validation failed!")

    def test_transfer_to_file_server(self) -> None:
        with self.app.app_context():
            from app.utility import strat_review_util

            test_data = {
                "strategy": ["test_strat_1", "test_strat_2", "test_strat_3"],
                "trigger_coeff": [2.5, 9.7, 9.7],
                "limit_coeff": [3, 10, 10],
                "expiration_time": ["15:15", "15:15", "14:45"],
            }
            test_data_df = pd.DataFrame.from_dict(test_data)

            def mock_uploader(cmd: str):
                from pandas import read_csv

                temp_file_name = cmd.split(sep=" ")[2]
                df = read_csv(temp_file_name)
                assert_frame_equal(left=df, right=test_data_df)

            strat_review_util.os.system = mock_uploader
            transfer_to_file_server(df=test_data_df, path_to_upload="")
            test_data_df.columns = [0, 1, 2, 3]

            def mock_uploader(cmd: str):
                temp_file_name = cmd.split(sep=" ")[2]
                df = pd.read_csv(temp_file_name, header=None)
                assert_frame_equal(left=df, right=test_data_df)

            strat_review_util.os.system = mock_uploader
            transfer_to_file_server(df=test_data_df, path_to_upload="", header=False)

    def test_get_correlation_pending_strats(self) -> None:
        with self.app.app_context():
            corr_index, error_strats = get_correlation_pending_strats(
                strategy_list=[
                    ["test_strat_1", pd.Timestamp(2018, 1, 1).year],
                    ["test_strat_2", pd.Timestamp(2018, 1, 1).year],
                    ["test_strat_3", pd.Timestamp(2018, 1, 1).year],
                ]
            )
            self.assertEqual(
                corr_index["test_strat_1"]["test_strat_2"],
                [-0.01, "pending"],
                msg="correlation does not match!",
            )
            self.assertEqual(
                error_strats, ["test_strat_3"], msg="error strategies do not match!"
            )

    def test_get_correlation_live_strats(self) -> None:
        with self.app.app_context():
            from app.utility import strat_review_util

            def mock_mtm_fetcher(strategy_name: str, status: str):
                mtm = pd.read_csv(f"./tests/test_data/{strategy_name}_MTM.log")
                mtm["date"] = pd.to_datetime(mtm["date"])
                mtm = mtm.set_index("date")["mtm"]
                return mtm

            strat_review_util.get_mtm_from_minio = mock_mtm_fetcher
            corr_index, error_strats = get_correlation_live_strats(
                strategy_list=[
                    ["test_strat_3", pd.Timestamp(2018, 1, 1).year],
                ],
                submitted_strategy_list=[
                    ["test_strat_5", pd.Timestamp(2018, 1, 1).year],
                ],
                identifier="live",
            )
            self.assertEqual(len(corr_index), 0, msg="error in correlation dictionary!")
            self.assertEqual(
                set(error_strats),
                {"test_strat_5"},
                msg="error strategies do not match!",
            )
            corr_index, error_strats = get_correlation_live_strats(
                strategy_list=[
                    ["test_strat_1", pd.Timestamp(2018, 1, 1).year],
                    ["test_strat_2", pd.Timestamp(2018, 1, 1).year],
                    ["test_strat_3", pd.Timestamp(2018, 1, 1).year],
                ],
                submitted_strategy_list=[
                    ["test_strat_4", pd.Timestamp(2018, 1, 1).year],
                    ["test_strat_5", pd.Timestamp(2018, 1, 1).year],
                ],
                identifier="live",
            )
            message_correlation = "correlation does not match!"
            self.assertEqual(
                corr_index["test_strat_1"]["test_strat_4"],
                [-0.00, "live"],
                msg=message_correlation,
            )
            self.assertEqual(
                corr_index["test_strat_2"]["test_strat_4"],
                [0.05, "live"],
                msg=message_correlation,
            )
            self.assertEqual(
                set(error_strats),
                {"test_strat_5", "test_strat_3"},
                msg="error strategies do not match!",
            )

    def test_reject_strategy(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                message_rejected = "{} rejected a {} strategy!"
                message_failed_to_reject = "{} failed to reject a {} strategy!"
                message_error_rejecting = (
                    "rejection failed but strat state still changed!"
                )
                # Logged in as developer
                self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                isRejected = reject_strategy(strategy="test_strat_4")
                self.assertFalse(
                    isRejected, msg=message_rejected.format("developer", "admin")
                )
                self.assertNotEqual(
                    Strategy.query.filter_by(strategy_name="test_strat_4")
                    .first()
                    .strat_state,
                    4,
                    msg=message_error_rejecting,
                )
                isRejected = reject_strategy(strategy="test_strat_2")
                self.assertFalse(
                    isRejected, msg=message_rejected.format("developer", "manager")
                )
                self.assertNotEqual(
                    Strategy.query.filter_by(strategy_name="test_strat_2")
                    .first()
                    .strat_state,
                    4,
                    msg=message_error_rejecting,
                )
                isRejected = reject_strategy(strategy="test_strat_3")
                self.assertFalse(
                    isRejected, msg=message_rejected.format("developer", "developer")
                )
                self.assertNotEqual(
                    Strategy.query.filter_by(strategy_name="test_strat_3")
                    .first()
                    .strat_state,
                    4,
                    msg=message_error_rejecting,
                )
                self.client.get("/sign_out")

                # Logged in as manager
                self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )
                isRejected = reject_strategy(strategy="test_strat_4")
                self.assertFalse(
                    isRejected, msg=message_rejected.format("manager", "admin")
                )
                self.assertNotEqual(
                    Strategy.query.filter_by(strategy_name="test_strat_4")
                    .first()
                    .strat_state,
                    4,
                    msg=message_error_rejecting,
                )
                isRejected = reject_strategy(strategy="test_strat_2")
                self.assertTrue(
                    isRejected,
                    msg=message_failed_to_reject.format("manager", "manager"),
                )
                self.assertEqual(
                    Strategy.query.filter_by(strategy_name="test_strat_2")
                    .first()
                    .strat_state,
                    4,
                    msg=message_failed_to_reject.format("manager", "manager"),
                )
                isRejected = reject_strategy(strategy="test_strat_3")
                self.assertTrue(
                    isRejected,
                    msg=message_failed_to_reject.format("manager", "developer"),
                )
                self.assertEqual(
                    Strategy.query.filter_by(strategy_name="test_strat_3")
                    .first()
                    .strat_state,
                    4,
                    msg=message_failed_to_reject.format("manager", "developer"),
                )
                self.client.get("/sign_out")

                # Logged in as admin
                self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                isRejected = reject_strategy(strategy="test_strat_4")
                self.assertTrue(
                    isRejected, msg=message_failed_to_reject.format("admin", "admin")
                )
                self.assertEqual(
                    Strategy.query.filter_by(strategy_name="test_strat_4")
                    .first()
                    .strat_state,
                    4,
                    msg=message_failed_to_reject.format("admin", "admin"),
                )
                Strategy.query.filter_by(
                    strategy_name="test_strat_2"
                ).first().strat_state = 1
                db.session.commit()
                isRejected = reject_strategy(strategy="test_strat_2")
                self.assertTrue(
                    isRejected, msg=message_failed_to_reject.format("admin", "manager")
                )
                self.assertEqual(
                    Strategy.query.filter_by(strategy_name="test_strat_2")
                    .first()
                    .strat_state,
                    4,
                    msg=message_failed_to_reject.format("admin", "manager"),
                )
                Strategy.query.filter_by(
                    strategy_name="test_strat_3"
                ).first().strat_state = 1
                isRejected = reject_strategy(strategy="test_strat_3")
                self.assertTrue(
                    isRejected,
                    msg=message_failed_to_reject.format("admin", "developer"),
                )
                self.assertEqual(
                    Strategy.query.filter_by(strategy_name="test_strat_3")
                    .first()
                    .strat_state,
                    4,
                    msg=message_failed_to_reject.format("admin", "developer"),
                )
                # Rejecting test strategies
                Strategy.query.filter_by(
                    strategy_name="test_strat_3"
                ).first().strat_state = 5
                isRejected = reject_strategy(strategy="test_strat_3")
                self.assertTrue(
                    isRejected,
                    msg=message_failed_to_reject.format("admin", "developer"),
                )
                self.assertEqual(
                    Strategy.query.filter_by(strategy_name="test_strat_3")
                    .first()
                    .strat_state,
                    4,
                    msg=message_failed_to_reject.format("admin", "developer"),
                )

                # Rejecting strategies fails when copy_objects_minio() fails

                def mock_copy_objects_minio_failure(
                    strategy: str, source: str, dest: str
                ):
                    raise Exception("Failed to reject!")

                strat_review_util.copy_objects_minio = mock_copy_objects_minio_failure
                isValid = reject_strategy(strategy="test_strat_1")
                self.assertFalse(
                    isValid,
                    msg="copy from minio process failed, still strategy was rejected!",
                )
                self.client.get("/sign_out")

    def test_kill_strategy(self) -> None:
        with self.app.app_context():
            from app.utility import strat_review_util

            def mock_transfer_to_file_server(
                df: pd.DataFrame, path_to_upload: str, header: bool = True
            ):
                if "limit_order" in path_to_upload:
                    self.assertEqual(len(df), 2, msg="shape of limit order incorrect!")
                    self.assertFalse(
                        "test_strat_2" in df["strategy"].to_list(),
                        msg="limit order list not updated properly!",
                    )
                else:
                    self.assertEqual(
                        df.to_list(),
                        ["test_strat_3"],
                        msg="slaves not updated properly!",
                    )

            strat_review_util.current_app.config["FILE_SERVER"] = "./tests/test_data"
            strat_review_util.transfer_to_file_server = mock_transfer_to_file_server
            self.add_strategy(
                strategy_name="cluster_test",
                developer="test_admin",
                segment="CASH",
                exchange_name="NSE",
                backtest_start_date=pd.Timestamp(2012, 1, 1),
                book_long="LC1",
                book_short="SC1",
                strat_state=2,
                submission_day=datetime.datetime.now(),
                trigger_coeff=412,
                limit_coeff=200,
                expiration_time=datetime.time(15, 15),
                long_short=-1,
            )
            self.add_strategy(
                strategy_name="cluster_test_1",
                developer="test_admin",
                segment="CASH",
                exchange_name="NSE",
                backtest_start_date=pd.Timestamp(2012, 1, 1),
                book_long="LC1",
                book_short="SC1",
                strat_state=2,
                submission_day=datetime.datetime.now(),
                trigger_coeff=412,
                limit_coeff=200,
                expiration_time=datetime.time(15, 15),
                long_short=-1,
            )
            Strategy.query.filter_by(
                strategy_name="test_strat_1"
            ).first().strat_state = 2
            db.session.commit()

            cluster_list, msg_for_team = kill_strategy(
                strategy=Strategy.query.filter_by(strategy_name="test_strat_2").first()
            )
            self.assertEqual(
                cluster_list,
                ["cluster_test"],
                msg="strategy not identified in cluster!",
            )
            self.assertEqual(
                Strategy.query.filter_by(strategy_name="test_strat_2")
                .first()
                .strat_state,
                3,
                msg="strategy state not changed!",
            )
            self.assertEqual(
                msg_for_team,
                "test_strat_2 is removed from live",
                msg="teams message does not match!",
            )
            cluster_list, msg_for_team = kill_strategy(
                strategy=Strategy.query.filter_by(strategy_name="test_strat_2").first()
            )
            self.assertEqual(cluster_list, [], msg="error in already dead strategy!")
            self.assertEqual(
                msg_for_team,
                "test_strat_2 is already dead",
                msg="error in teams message!",
            )
            Strategy.query.filter_by(
                strategy_name="test_strat_3"
            ).first().segment = "CASH"
            db.session.commit()

    def test_accept_strategy(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                from app.utility import strat_review_util

                def mock_post_acceptance_process(strategy: Strategy, mode: str):
                    if strategy.strategy_name == "test_strat_3":
                        return ["test_cluster_1", "test_cluster_2"], "test_reworked"
                    else:
                        return [
                            "test_cluster_01",
                            "test_cluster_02",
                        ], "test_reworked_01"

                def mock_update_cluster_parameters(strategy: Strategy):
                    return

                strat_review_util.post_acceptance_process = mock_post_acceptance_process
                strat_review_util.update_cluster_parameters = (
                    mock_update_cluster_parameters
                )

                message_strats_info = "strats info does not match!"
                message_validity = "{} accepted a {} strategy!"
                message_failed_accept = "{} failed to accept {} strategy!"
                message_error_accepting = (
                    "acceptance failed but strat state still changed!"
                )
                # Logged in as developer
                self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                isValid, strats_info = accept_strategy(strategy="test_strat_1")
                self.assertFalse(
                    isValid, msg=message_validity.format("developer", "admin")
                )
                self.assertNotEqual(
                    Strategy.query.filter_by(strategy_name="test_strat_1")
                    .first()
                    .strat_state,
                    2,
                    msg=message_error_accepting,
                )
                isValid, strats_info = accept_strategy(strategy="test_strat_2")
                self.assertFalse(
                    isValid, msg=message_validity.format("developer", "manager")
                )
                self.assertNotEqual(
                    Strategy.query.filter_by(strategy_name="test_strat_2")
                    .first()
                    .strat_state,
                    2,
                    msg=message_error_accepting,
                )
                isValid, strats_info = accept_strategy(strategy="test_strat_3")
                self.assertFalse(
                    isValid, msg=message_validity.format("developer", "developer")
                )
                self.assertNotEqual(
                    Strategy.query.filter_by(strategy_name="test_strat_3")
                    .first()
                    .strat_state,
                    2,
                    msg=message_error_accepting,
                )
                self.client.get("/sign_out")

                # Logged in as manager
                self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )
                isValid, strats_info = accept_strategy(strategy="test_strat_1")
                self.assertFalse(
                    isValid, msg=message_validity.format("manager", "admin")
                )
                self.assertNotEqual(
                    Strategy.query.filter_by(strategy_name="test_strat_1")
                    .first()
                    .strat_state,
                    2,
                    msg=message_error_accepting,
                )

                isValid, strats_info = accept_strategy(strategy="test_strat_3")
                self.assertTrue(
                    isValid, msg=message_failed_accept.format("manager", "developer")
                )
                self.assertEqual(
                    strats_info,
                    {
                        "cluster_list": ["test_cluster_1", "test_cluster_2"],
                        "reworked_strategy": "test_reworked",
                    },
                    msg=message_strats_info,
                )
                strategy = Strategy.query.filter_by(
                    strategy_name="test_strat_3"
                ).first()
                self.assertEqual(
                    strategy.strat_state,
                    2,
                    msg=message_failed_accept.format("manager", "developer"),
                )
                self.assertEqual(
                    strategy.live_start_day,
                    pd.Timestamp.today(),
                    msg=message_failed_accept.format("manager", "developer"),
                )
                self.assertEqual(
                    len(strategy.cluster_mapping),
                    4,
                    msg=message_failed_accept.format("manager", "developer"),
                )

                isValid, strats_info = accept_strategy(strategy="test_strat_2")
                self.assertTrue(
                    isValid, msg=message_failed_accept.format("manager", "manager")
                )
                self.assertEqual(
                    strats_info,
                    {
                        "cluster_list": ["test_cluster_01", "test_cluster_02"],
                        "reworked_strategy": "test_reworked_01",
                    },
                    msg=message_strats_info,
                )
                strategy = Strategy.query.filter_by(
                    strategy_name="test_strat_2"
                ).first()
                self.assertEqual(
                    strategy.strat_state,
                    2,
                    msg=message_failed_accept.format("manager", "manager"),
                )
                self.assertEqual(
                    strategy.live_start_day,
                    pd.Timestamp.today(),
                    msg=message_failed_accept.format("manager", "manager"),
                )

                Strategy.query.filter_by(
                    strategy_name="test_strat_3"
                ).first().strat_state = 1
                db.session.commit()

                # accepting a strategy into test mode
                isValid, strats_info = accept_strategy(
                    strategy="test_strat_3", mode="test"
                )
                self.assertTrue(
                    isValid, msg=message_failed_accept.format("manager", "developer")
                )
                self.assertEqual(
                    strats_info,
                    {
                        "cluster_list": ["test_cluster_1", "test_cluster_2"],
                        "reworked_strategy": "test_reworked",
                    },
                    msg=message_strats_info,
                )
                strategy = Strategy.query.filter_by(
                    strategy_name="test_strat_3"
                ).first()
                old_strategy = Strategy.query.filter_by(
                    strategy_name="test_reworked"
                ).first()
                self.assertEqual(
                    strategy.strat_state,
                    5,
                    msg=message_failed_accept.format("manager", "developer"),
                )
                self.assertEqual(
                    old_strategy.strat_state,
                    2,
                    msg="original startegy's state affected when rework pushed to test",
                )
                self.assertEqual(
                    strategy.live_start_day,
                    pd.Timestamp.today(),
                    msg=message_failed_accept.format("manager", "developer"),
                )

                self.client.get("/sign_out")
                Strategy.query.filter_by(
                    strategy_name="test_strat_2"
                ).first().strat_state = 1
                Strategy.query.filter_by(
                    strategy_name="test_strat_3"
                ).first().strat_state = 1
                db.session.commit()

                # Logged in as admin
                self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                isValid, strats_info = accept_strategy(strategy="test_strat_1")
                self.assertTrue(
                    isValid, msg=message_failed_accept.format("admin", "admin")
                )
                self.assertEqual(
                    strats_info,
                    {
                        "cluster_list": ["test_cluster_01", "test_cluster_02"],
                        "reworked_strategy": "test_reworked_01",
                    },
                    msg=message_strats_info,
                )
                strategy = Strategy.query.filter_by(
                    strategy_name="test_strat_1"
                ).first()
                self.assertEqual(
                    strategy.strat_state,
                    2,
                    msg=message_failed_accept.format("admin", "admin"),
                )
                self.assertEqual(
                    strategy.live_start_day,
                    pd.Timestamp.today(),
                    msg=message_failed_accept.format("admin", "admin"),
                )

                isValid, strats_info = accept_strategy(strategy="test_strat_3")
                self.assertTrue(
                    isValid, msg=message_failed_accept.format("admin", "developer")
                )
                self.assertEqual(
                    strats_info,
                    {
                        "cluster_list": ["test_cluster_1", "test_cluster_2"],
                        "reworked_strategy": "test_reworked",
                    },
                    msg=message_strats_info,
                )
                strategy = Strategy.query.filter_by(
                    strategy_name="test_strat_3"
                ).first()
                self.assertEqual(
                    strategy.strat_state,
                    2,
                    msg=message_failed_accept.format("admin", "developer"),
                )
                self.assertEqual(
                    strategy.live_start_day,
                    pd.Timestamp.today(),
                    msg=message_failed_accept.format("admin", "developer"),
                )

                isValid, strats_info = accept_strategy(strategy="test_strat_2")
                self.assertTrue(
                    isValid, msg=message_failed_accept.format("admin", "manager")
                )
                self.assertEqual(
                    strats_info,
                    {
                        "cluster_list": ["test_cluster_01", "test_cluster_02"],
                        "reworked_strategy": "test_reworked_01",
                    },
                    msg=message_strats_info,
                )
                strategy = Strategy.query.filter_by(
                    strategy_name="test_strat_2"
                ).first()
                self.assertEqual(
                    strategy.strat_state,
                    2,
                    msg=message_failed_accept.format("admin", "manager"),
                )
                self.assertEqual(
                    strategy.live_start_day,
                    pd.Timestamp.today(),
                    msg=message_failed_accept.format("admin", "manager"),
                )

                Strategy.query.filter_by(
                    strategy_name="test_strat_3"
                ).first().strat_state = 5
                db.session.commit()

                # accepting a test strategy into live mode
                isValid, strats_info = accept_strategy(strategy="test_strat_3")
                self.assertTrue(
                    isValid, msg=message_failed_accept.format("admin", "developer")
                )
                self.assertEqual(
                    strats_info,
                    {
                        "cluster_list": ["test_cluster_1", "test_cluster_2"],
                        "reworked_strategy": "test_reworked",
                    },
                    msg=message_strats_info,
                )
                strategy = Strategy.query.filter_by(
                    strategy_name="test_strat_3"
                ).first()
                self.assertEqual(
                    strategy.strat_state,
                    2,
                    msg=message_failed_accept.format("admin", "developer"),
                )
                self.assertEqual(
                    strategy.live_start_day,
                    pd.Timestamp.today(),
                    msg=message_failed_accept.format("admin", "developer"),
                )

                def mock_post_acceptance_process_failure(strategy: Strategy):
                    raise Exception("Failed to accept!")

                strat_review_util.post_acceptance_process = (
                    mock_post_acceptance_process_failure
                )
                isValid, strats_info = accept_strategy(strategy="test_strat_4")
                self.assertFalse(
                    isValid,
                    msg="post acceptance process failed, still strategy was accepted!",
                )
                self.assertNotEqual(
                    Strategy.query.filter_by(strategy_name="test_strat_4")
                    .first()
                    .strat_state,
                    2,
                    msg=message_error_accepting,
                )

    def test_post_acceptance_process(self) -> None:
        with self.app.app_context():
            from app.utility import strat_review_util

            def mock_transfer_to_file_server(
                df: pd.DataFrame, path_to_upload: str, header: bool = True
            ):
                if "limit_order" in path_to_upload:
                    self.assertEqual(len(df), 3, msg="shape of limit order incorrect!")
                    self.assertTrue(
                        "test_strat_2" in df["strategy"].to_list(),
                        msg="limit order list not updated properly!",
                    )
                else:
                    self.assertTrue(
                        set(df.to_list()) == set(["test_strat_3", "test_strat_2"]),
                        msg="slaves not updated properly!",
                    )

            def mock_kill_strategy(strategy: Strategy):
                return ["cluster_test"], "test_strat_1 removed from live strategies!"

            mock_copy_objects_minio = unittest.mock.MagicMock()
            mock_send_message_to_kafka = unittest.mock.MagicMock()

            strat_review_util.transfer_to_file_server = mock_transfer_to_file_server
            strat_review_util.kill_strategy = mock_kill_strategy
            strat_review_util.copy_objects_minio = mock_copy_objects_minio
            strat_review_util.send_message_to_kafka = mock_send_message_to_kafka
            strat_review_util.current_app.config["FILE_SERVER"] = "./tests/test_data"

            # Mode = "live" and strategy state is pending
            cluster_list, strategy_reworked = post_acceptance_process(
                strategy=Strategy.query.filter_by(strategy_name="test_strat_2").first(),
                mode="live",
            )
            self.assertEqual(
                cluster_list,
                ["cluster_test"],
                msg="cluster removal of reworked strategy failed!",
            )
            self.assertEqual(
                strategy_reworked,
                "test_strat_1",
                msg="reworked strategy not identified!",
            )
            self.assertEqual(
                mock_copy_objects_minio.call_count,
                1,
                msg="copy_objects_minio not called correctly!",
            )
            mock_send_message_to_kafka.assert_called_with(
                text="test_strat_1 removed from live strategies!"
            )

            # Mode = "live" and strategy state is test
            Strategy.query.filter_by(
                strategy_name="test_strat_2"
            ).first().strat_state = 5
            db.session.commit()
            cluster_list, strategy_reworked = post_acceptance_process(
                strategy=Strategy.query.filter_by(strategy_name="test_strat_2").first(),
                mode="live",
            )
            self.assertEqual(
                cluster_list,
                ["cluster_test"],
                msg="cluster removal of reworked strategy failed!",
            )
            self.assertEqual(
                strategy_reworked,
                "test_strat_1",
                msg="reworked strategy not identified!",
            )
            self.assertEqual(
                mock_copy_objects_minio.call_count,
                2,
                msg="copy_objects_minio not called correctly!",
            )

            # Mode = "test"
            Strategy.query.filter_by(
                strategy_name="test_strat_2"
            ).first().strat_state = 1
            db.session.commit()
            cluster_list, strategy_reworked = post_acceptance_process(
                strategy=Strategy.query.filter_by(strategy_name="test_strat_2").first(),
                mode="test",
            )
            self.assertIsNone(
                cluster_list,
                msg="Cluster list should be None in 'test' mode!",
            )
            self.assertEqual(
                strategy_reworked,
                "test_strat_1",
                msg="Reworked strategy not identified in 'test' mode!",
            )
            self.assertEqual(
                mock_copy_objects_minio.call_count,
                2,
                msg="copy_objects_minio not called correctly!",
            )
            mock_send_message_to_kafka.assert_called_with(
                text="New Rework Strategy: test_strat_2 being pushed to test. Original strategy: test_strat_1 not killed in live."
            )

    def test_dfs(self) -> None:
        with self.app.app_context():
            visited = [False] * 5
            new_state = dfs(0, visited, "12200")
            self.assertEqual(new_state, "00000")

            visited = [False] * 5
            new_state = dfs(2, visited, "21022")
            self.assertEqual(new_state, "21000")

            visited = [False, False, False, True, False]
            new_state = dfs(2, visited, "21022")
            self.assertEqual(new_state, "21020")

            visited = [False, False, False, True, False]
            new_state = dfs(2, visited, "21032")
            self.assertEqual(new_state, "21030")

            visited = [False, False, False, True, False]
            new_state = dfs(4, visited, "21023")
            self.assertEqual(new_state, "21020")

    def test_start_downstream_service(self) -> None:
        with self.app.app_context():
            service_states = start_downstream_service(0, "12202")
            self.assertEqual(service_states, "00000")

            service_states = start_downstream_service(1, "21220")
            self.assertEqual(service_states, "20220")

            service_states = start_downstream_service(2, "02021")
            self.assertEqual(service_states, "02000")

            service_states = start_downstream_service(2, "02031")
            self.assertEqual(service_states, "02030")

            service_states = start_downstream_service(3, "02031")
            self.assertEqual(service_states, "02001")

    def test_clean_review(self) -> None:
        with self.app.app_context():
            review = StrategyReview(strategy_name="test_strat_1", to_do="improve speed")
            review = clean_review(review=review)
            self.assertEqual(
                review.timecheck, "", msg="review form not cleaned properly"
            )

    def test_is_all_empty(self) -> None:
        with self.app.app_context():
            review = StrategyReview(strategy_name="test_strat_1", to_do="improve speed")
            review = clean_review(review=review)
            self.assertFalse(
                is_all_empty_review(review=review), msg="review checking at fault"
            )

            review = StrategyReview(
                strategy_name="test_strat_1",
            )
            review = clean_review(review=review)
            self.assertTrue(
                is_all_empty_review(review=review), msg="review checking at fault"
            )

    def test_check_strategy_for_invalid_filters(self) -> None:
        with self.app.app_context():
            python_file = open("./tests/test_data/invalid_strat.py", "rb")
            filters = check_strategy_for_invalid_filters(python_file)
            self.assertEqual(
                set(filters),
                set(
                    [
                        "option_filter",
                        "stock_option_filter",
                        "optcom_filter",
                        "set_trace",
                        "Only KeyError Exception is allowed",
                        "finally",
                    ]
                ),
            )
            python_file.close()
            python_file = open("./tests/test_data/test.py", "rb")
            filters = check_strategy_for_invalid_filters(python_file)
            self.assertEqual(filters, [])
            python_file.close()

    def test_get_all_clusters_in_hierarchy(self) -> None:
        with self.app.app_context():
            self.add_strategy(
                strategy_name="cluster_test",
                developer="test_manager",
                segment="CASH",
                exchange_name="NSE",
                backtest_start_date=pd.Timestamp(2009, 1, 1),
                book_long="LC1",
                book_short="SC2",
                strat_state=1,
                submission_day=datetime.datetime.now(),
                trigger_coeff=500,
                limit_coeff=300,
                expiration_time=datetime.time(15, 15),
                long_short=-1,
                cluster_mapping=["cluster_kailash"],
                overlap_days=25,
            )
            self.add_strategy(
                strategy_name="cluster_kailash",
                developer="test_manager",
                segment="CASH",
                exchange_name="NSE",
                backtest_start_date=pd.Timestamp(2009, 1, 1),
                book_long="LC1",
                book_short="SC2",
                strat_state=1,
                submission_day=datetime.datetime.now(),
                trigger_coeff=500,
                limit_coeff=300,
                expiration_time=datetime.time(15, 15),
                long_short=-1,
                cluster_mapping=["cluster_options"],
                overlap_days=25,
            )
            self.add_strategy(
                strategy_name="cluster_options",
                developer="test_manager",
                segment="CASH",
                exchange_name="NSE",
                backtest_start_date=pd.Timestamp(2009, 1, 1),
                book_long="LC1",
                book_short="SC2",
                strat_state=1,
                submission_day=datetime.datetime.now(),
                trigger_coeff=500,
                limit_coeff=300,
                expiration_time=datetime.time(15, 15),
                long_short=-1,
                cluster_mapping=[],
                overlap_days=25,
            )
            strat = Strategy.query.get_or_404("test_strat_2")
            all_clusters = get_all_clusters_in_hierarchy(strat)
            self.assertEqual(len(all_clusters), 3)
            self.assertEqual(
                {"cluster_options", "cluster_test", "cluster_kailash"},
                {cluster.strategy_name for cluster in all_clusters},
            )

    def test_update_cluster_parameters(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                self.add_strategy(
                    strategy_name="cluster_test",
                    developer="test_manager",
                    segment="CASH",
                    exchange_name="NSE",
                    backtest_start_date=pd.Timestamp(2009, 1, 1),
                    book_long="LC1",
                    book_short="SC2",
                    strat_state=1,
                    submission_day=datetime.datetime.now(),
                    trigger_coeff=500,
                    limit_coeff=300,
                    expiration_time=datetime.time(15, 15),
                    long_short=-1,
                    cluster_mapping=["cluster_kailash"],
                    overlap_days=25,
                )
                self.add_strategy(
                    strategy_name="cluster_kailash",
                    developer="test_manager",
                    segment="CASH",
                    exchange_name="NSE",
                    backtest_start_date=pd.Timestamp(2009, 1, 1),
                    book_long="LC1",
                    book_short="SC2",
                    strat_state=1,
                    submission_day=datetime.datetime.now(),
                    trigger_coeff=500,
                    limit_coeff=300,
                    expiration_time=datetime.time(15, 15),
                    long_short=-1,
                    cluster_mapping=[],
                    overlap_days=25,
                )
                # Logged in as admin
                self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                strat = Strategy.query.get_or_404("test_strat_2")
                strat.overlap_days = 40
                db.session.commit()
                update_cluster_parameters(strat)
                # overlap days updated for both cluster kailash and cluster_test
                cluster_strat = Strategy.query.get_or_404("cluster_kailash")
                self.assertEqual(cluster_strat.overlap_days, 40)
                cluster_strat = Strategy.query.get_or_404("cluster_test")
                self.assertEqual(cluster_strat.overlap_days, 40)


if __name__ == "__main__":
    unittest.main()
