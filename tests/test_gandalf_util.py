import unittest
import pandas as pd
import numpy as np
from io import BytesIO
from app.utility.gandalf_util import (
    average_return,
    sharpe_ratio,
    calc_max_dd,
    calc_max_dd_days,
    calc_trading_days,
    get_all_dates_path,
)
from tests.base_test_fixture import BaseTestFixture


class TestGandalfUtil(BaseTestFixture):
    def setUp(self) -> None:
        super().setUp()
        path = "./tests/test_data/gandalf_MTM.log"
        self.file = open(path, "rb")
        self.data = self.file.read()
        self.data = pd.read_csv(BytesIO(self.data))
        self.data["date"] = pd.to_datetime(self.data["date"])
        self.data = self.data.set_index("date")["mtm"]

    def tearDown(self) -> None:
        self.file.close()

    def test_average_return(self):
        calc_values = average_return(self.data)
        true_values = [
            236098.6943541522,
            109250.02924840299,
            492081.80894669605,
            741175.7099115057,
            661181.9121054815,
            -103349.72824680383,
            405549.3725163206,
        ]
        for i in range(len(calc_values)):
            self.assertAlmostEqual(
                calc_values[calc_values.index[i]],
                true_values[i],
                places=8,
                msg=f"Average return failed for {calc_values.index[i]}",
            )

    def test_sharpe_ratio(self):
        calc_values = sharpe_ratio(self.data)
        true_values = [
            67.40426340511914,
            29.585792218325967,
            124.94014746700093,
            115.02872272065244,
            132.9875163036366,
            -48.71020788251429,
            78.82596682241399,
        ]
        for i in range(len(calc_values)):
            self.assertAlmostEqual(
                calc_values[calc_values.index[i]],
                true_values[i],
                places=8,
                msg=f"Sharpe ratio failed for {calc_values.index[i]}",
            )

    def test_calc_max_dd(self):
        calc_values = calc_max_dd(self.data)
        true_values = [
            505138.3227234816,
            1094207.11554046,
            880365.0302533153,
            602823.9570558681,
            633704.3708851757,
            776255.9917768505,
            1143593.4467156176,
        ]
        for i in range(len(calc_values)):
            self.assertAlmostEqual(
                calc_values[calc_values.index[i]],
                true_values[i],
                places=8,
                msg=f"MaxDD failed for {calc_values.index[i]}",
            )

    def test_calc_trading_days(self):
        calc_values = calc_trading_days(self.data)
        true_values = [245, 244, 251, 247, 247, 88, 1322]
        for i in range(len(calc_values)):
            self.assertEqual(
                calc_values[calc_values.index[i]],
                true_values[i],
                msg=f"Trading Days failed for {calc_values.index[i]}",
            )

    def test_calc_max_dd_days(self):
        ALL_DATES = np.load("./tests/test_data/ALL_DATES.npy", allow_pickle=True)
        day_dict = {}
        for date_index, date in enumerate(ALL_DATES):
            day_dict[date] = date_index

        calc_values = calc_max_dd_days(self.data, day_dict)
        true_values = [106, 80, 26, 45, 24, 54, 126]
        for i in range(len(calc_values)):
            self.assertEqual(
                calc_values[calc_values.index[i]],
                true_values[i],
                msg=f"MaxDD Days failed for {calc_values.index[i]}",
            )

    def test_get_all_dates_path(self):
        with self.app.app_context():
            msg = "Invalid all dates path for exchange {} segment {}"
            # Exchange is NSE and segment is not OPTCUR/FUTCUR
            all_dates_path = get_all_dates_path(exchange="NSE", segment="CASH")
            self.assertEqual(
                "support_files/ALL_DATES.npy", all_dates_path, msg.format("NSE", "CASH")
            )
            # Exchange is NSE and segment is OPTCUR/FUTCUR
            all_dates_path = get_all_dates_path(exchange="NSE", segment="OPTCUR")
            self.assertEqual(
                "support_files/ALL_DATES_FX.npy",
                all_dates_path,
                msg.format("NSE", "OPTCUR"),
            )
            all_dates_path = get_all_dates_path(exchange="NSE", segment="FUTCUR")
            self.assertEqual(
                "support_files/ALL_DATES_FX.npy",
                all_dates_path,
                msg.format("NSE", "FUTCUR"),
            )
            # Exchange is NCDEX
            all_dates_path = get_all_dates_path(exchange="NCDEX", segment="OPTCOM")
            self.assertEqual(
                "support_files/ALL_DATES_NCDEX.npy",
                all_dates_path,
                msg.format("NCDEX", "OPTCOM"),
            )
            # Exchange is MCX
            all_dates_path = get_all_dates_path(exchange="MCX", segment="OPTCOM")
            self.assertEqual(
                "support_files/ALL_DATES_MCX.npy",
                all_dates_path,
                msg.format("MCX", "OPTCOM"),
            )
            # Exchange is KRX
            all_dates_path = get_all_dates_path(exchange="KRX", segment="OPTIDX_KRX")
            self.assertEqual(
                "support_files/ALL_DATES_KRX.npy",
                all_dates_path,
                msg.format("KRX", "OPTIDX_KRX"),
            )
            # Exchange is COMM (example use: MCX is mentioned as COMM in deadsheets)
            all_dates_path = get_all_dates_path(exchange="COMM", segment="OPTCOM")
            self.assertEqual(
                "support_files/ALL_DATES_MCX.npy",
                all_dates_path,
                msg.format("COMM", "OPTCOM"),
            )
            # Exchange is GIFT
            all_dates_path = get_all_dates_path(exchange="GIFT", segment="FUTIDX_GIFT")
            self.assertEqual(
                "support_files/ALL_DATES_GIFT.npy",
                all_dates_path,
                msg.format("GIFT", "FUTIDX_GIFT"),
            )
            # Exchange is US
            all_dates_path = get_all_dates_path(exchange="US", segment="OPTIDX_US")
            self.assertEqual(
                "support_files/ALL_DATES_INTERNATIONAL",
                all_dates_path,
                msg.format("US", "OPTIDX_US"),
            )
