import datetime
import unittest
from typing import List

import pandas as pd
from app import create_app
from app.models import (
    DeadSheet,
    PendingBacktests,
    Role,
    StrategyClusterMapping,
    Status,
    Strategy,
    StrategyMetaData,
    StrategyReview,
    User,
    ClusterMappingStatus,
    db,
)

from app.strat_add import strat_add_views
from app.utility import strat_review_util
from config import TestingConfig
from flask import Flask
from sqlalchemy.engine import Row
from werkzeug.security import generate_password_hash


class BaseTestFixture(unittest.TestCase):
    def setUp(self) -> None:
        self.app = create_app(config=TestingConfig)
        self.client = self.app.test_client()
        self.email_admin = "<EMAIL>"
        self.email_developer = "<EMAIL>"
        self.email_manager = "<EMAIL>"
        self.password = "test@123"
        self.message_template = "wrong template rendered!"
        self.message_response_code = "response code incorrect!"
        self.files = []
        with self.app.app_context():

            def mock_get_files_from_minio(
                status: str, strategy_name: str, post_fix: str, cluster_name: str = ""
            ):
                try:
                    with open(f"tests/test_data/test_strat_1{post_fix}", "rb") as file:
                        file_data = file.read()

                    return file_data
                except Exception:
                    raise Exception("File not found!")

            def mock_get_file_data_from_minio(path: str):
                with open("tests/test_data/backtest_sentinel.csv", "rb") as file:
                    file_data = file.read()

                return file_data

            def mock_delete_from_minio(strategy_name: str, source_path: str):
                pass

            def mock_send_message_teams(text: str):
                pass

            def mock_send_message_kafka(text: str):
                pass

            def mock_get_live_pnl_curve(
                strategy_name: str,
                status: str,
                start_day: datetime.date,
                dead_trades: List[Row],
                week: str,
            ) -> str:
                return ""

            def mock_get_gandalf(
                dead_trades: List[Row], exchange: str = "NSE", segment: str = "OPTIDX"
            ) -> pd.DataFrame:
                return pd.DataFrame()

            def mock_check_object_exists(prefix: str):
                return True

            def mock_upload_dataframe_to_minio(path: str, dataframe: pd.DataFrame):
                self.assertEqual(
                    ["strategy_name", "user"],
                    dataframe.columns.tolist(),
                    "dataframe recieved is invalid",
                )

            strat_add_views.check_object_exists = mock_check_object_exists
            strat_add_views.upload_dataframe_to_minio = mock_upload_dataframe_to_minio
            strat_add_views.get_file_data_from_minio = mock_get_file_data_from_minio
            strat_add_views.delete_from_minio = mock_delete_from_minio
            strat_add_views.get_files_from_minio = mock_get_files_from_minio
            strat_add_views.send_message_to_kafka = mock_send_message_kafka
            strat_add_views.get_live_pnl_curve = mock_get_live_pnl_curve
            strat_add_views.get_gandalf = mock_get_gandalf
            strat_review_util.get_files_from_minio = mock_get_files_from_minio
            strat_review_util.delete_from_minio = mock_delete_from_minio
            strat_review_util.send_message_to_kafka = mock_send_message_kafka
            db.create_all()
            status_pending = Status(id=1, state_description="PENDING")
            status_live = Status(id=2, state_description="LIVE")
            status_dead = Status(id=3, state_description="DEAD")
            status_rejected = Status(id=4, state_description="REJECTED")
            status_test = Status(id=5, state_description="TEST")
            mapping_status_live = ClusterMappingStatus(
                id=0, state_description="LIVE_ENV"
            )
            mapping_status_test = ClusterMappingStatus(
                id=1, state_description="TEST_ENV"
            )
            role_admin = Role(id=1, name="ADMIN")
            role_manager = Role(id=2, name="MANAGER")
            role_developer = Role(id=3, name="DEVELOPER")
            db.session.add_all(
                [
                    status_pending,
                    status_live,
                    status_dead,
                    status_rejected,
                    status_test,
                    mapping_status_live,
                    mapping_status_test,
                    role_admin,
                    role_manager,
                    role_developer,
                ]
            )
            db.session.commit()
            self.app.config["WTF_CSRF_ENABLED"] = False
            self.populate_db_backtests()
            self.populate_db_deadsheet()
            self.populate_db_reviews()
            self.populate_db_strategies()
            self.populate_db_metadata()
            self.add_user(
                password=self.password,
                username="test_admin",
                email=self.email_admin,
                role_id=1,
            )
            self.add_user(
                password=self.password,
                username="test_manager",
                email=self.email_manager,
                role_id=2,
            )
            self.add_user(
                password=self.password,
                username="test_developer",
                email=self.email_developer,
                role_id=3,
                manager="test_manager",
            )

    def add_user(
        self,
        password: str,
        username: str,
        email: str,
        role_id: int,
        manager: str = None,
    ) -> None:
        with self.app.app_context():
            encrypted_password = generate_password_hash(
                password, method="pbkdf2:sha1", salt_length=8
            )
            test_user = User(
                username=username,
                email=email,
                password_enc=encrypted_password,
                role_id=role_id,
                manager=manager,
            )
            db.session.add(test_user)
            db.session.commit()

    def add_strategy(
        self,
        strategy_name: str,
        developer: str,
        segment: str,
        exchange_name: str,
        backtest_start_date: pd.Timestamp,
        book_long: str,
        book_short: str,
        strat_state: int,
        submission_day: pd.Timestamp,
        trigger_coeff: int,
        limit_coeff: int,
        expiration_time: datetime.time,
        long_short: int,
        cluster_mapping: List[str] = None,
        comments: str = None,
        strategy_reworked: str = None,
        live_start_day: pd.Timestamp = None,
        overlap_days: int = 20,
    ) -> None:
        with self.app.app_context():
            strategy = Strategy(
                strategy_name=strategy_name,
                developer=developer,
                segment=segment,
                exchange_name=exchange_name,
                backtest_start_date=backtest_start_date,
                book_long=book_long,
                book_short=book_short,
                strat_state=strat_state,
                submission_day=submission_day,
                trigger_coeff=trigger_coeff,
                limit_coeff=limit_coeff,
                expiration_time=expiration_time,
                long_short=long_short,
                comments=comments,
                reworked_strategy=strategy_reworked,
                live_start_day=live_start_day,
                overlap_days=overlap_days,
            )
            if cluster_mapping is not None:
                for cluster in cluster_mapping:
                    strategy.cluster_mapping.append(
                        StrategyClusterMapping(
                            cluster_name=cluster,
                            strategy_name=strategy_name,
                            mapping_status=ClusterMappingStatus.get_status(
                                description="LIVE_ENV"
                            ),
                        )
                    )
                    strategy.cluster_mapping.append(
                        StrategyClusterMapping(
                            cluster_name=cluster,
                            strategy_name=strategy_name,
                            mapping_status=ClusterMappingStatus.get_status(
                                description="TEST_ENV"
                            ),
                        )
                    )
            db.session.add(strategy)
            db.session.commit()

    def add_review(
        self,
        strategy_name: str,
        timecheck: str = None,
        correlation_check: str = None,
        trade_distribution_check: str = None,
        risk_analysis: str = None,
        num_days_trading: str = None,
        comments: str = None,
        to_change: str = None,
    ) -> None:
        with self.app.app_context():
            strategy_review = StrategyReview(
                strategy_name=strategy_name,
                timecheck=timecheck,
                correlation_check=correlation_check,
                trade_distribution_check=trade_distribution_check,
                risk_analysis=risk_analysis,
                num_days_trading=num_days_trading,
                comments=comments,
                to_change=to_change,
            )
            db.session.add(strategy_review)
            db.session.commit()

    def add_backtest(
        self,
        request_time: pd.Timestamp,
        strategy_name: str,
        service_state: str = "00000",
    ) -> None:
        with self.app.app_context():
            new_backtest = PendingBacktests(
                request_time=request_time,
                strategy_name=strategy_name,
                service_state=service_state,
            )
            db.session.add(new_backtest)
            db.session.commit()

    def add_deadtrade(
        self,
        logit_id: int = None,
        entry_timestamp: pd.Timestamp = None,
        segment: str = None,
        symbol: str = None,
        expiry: pd.Timestamp = None,
        total_quantity: float = None,
        strategy: str = None,
        entry_price: float = None,
        type: str = None,
        strike: float = None,
        exit_timestamp: pd.Timestamp = None,
        exit_price: float = None,
        stt: float = None,
        brokerage: float = None,
        others: float = None,
        exposure: float = None,
        PnL: float = None,
        slave_strat: str = None,
        slave_dev: str = None,
        book_long: str = None,
        book_short: str = None,
        exchange: str = None,
        exit_date: pd.Timestamp = None,
        entry_date: pd.Timestamp = None,
    ) -> None:
        with self.app.app_context():
            deadtrade = DeadSheet(
                logit_id=logit_id,
                entry_timestamp=entry_timestamp,
                segment=segment,
                symbol=symbol,
                expiry=expiry,
                total_quantity=total_quantity,
                strategy=strategy,
                entry_price=entry_price,
                type=type,
                strike=strike,
                exit_timestamp=exit_timestamp,
                exit_price=exit_price,
                stt=stt,
                brokerage=brokerage,
                others=others,
                exposure=exposure,
                PnL=PnL,
                slave_strat=slave_strat,
                slave_dev=slave_dev,
                book_long=book_long,
                book_short=book_short,
                exchange=exchange,
                exit_date=exit_date,
                entry_date=entry_date,
            )
            db.session.add(deadtrade)
            db.session.commit()

    def add_meta_values(
        self,
        strategy_name: str,
        max_dd_submit: float = None,
        monthly_sharpe_submit: float = None,
        ret_dd_submit: float = None,
        max_dd_monte: float = None,
        percentile_dd: float = None,
        curr_backtest_dd: float = None,
        last_sentinel_run: pd.Timestamp = None,
        monthly_sharpe_post: float = None,
        monthly_ret_post: float = None,
        monthly_ret_submit: float = None,
        monthly_sharpe_live: float = None,
        monthly_ret_live: float = None,
        max_dd_live: float = None,
        ret_dd_live: float = None,
        trading_days_live: float = None,
        daily_sharpe_live: float = None,
    ) -> None:
        with self.app.app_context():
            strategy_meta_values = StrategyMetaData(
                strategy_name=strategy_name,
                max_dd_submit=max_dd_submit,
                monthly_sharpe_submit=monthly_sharpe_submit,
                ret_dd_submit=ret_dd_submit,
                max_dd_monte=max_dd_monte,
                percentile_dd=percentile_dd,
                curr_backtest_dd=curr_backtest_dd,
                last_sentinel_run=last_sentinel_run,
                monthly_ret_post=monthly_ret_post,
                monthly_ret_submit=monthly_ret_submit,
                monthly_sharpe_post=monthly_sharpe_post,
                monthly_sharpe_live=monthly_sharpe_live,
                monthly_ret_live=monthly_ret_live,
                max_dd_live=max_dd_live,
                ret_dd_live=ret_dd_live,
                trading_days_live=trading_days_live,
                daily_sharpe_live=daily_sharpe_live,
            )
            db.session.add(strategy_meta_values)
            db.session.commit()

    def populate_db_backtests(self) -> None:
        with self.app.app_context():
            self.add_backtest(
                request_time=datetime.datetime.now(),
                strategy_name="test_strat_1",
            )
            self.add_backtest(
                request_time=datetime.datetime.now(),
                strategy_name="test_strat_1",
            )
            self.add_backtest(
                request_time=datetime.datetime.now(),
                strategy_name="test_strat_2",
                service_state="22222",
            )
            self.add_backtest(
                request_time=datetime.datetime.now(),
                strategy_name="test_strat_3",
                service_state="10000",
            )
            self.add_backtest(
                request_time=datetime.datetime.now(),
                strategy_name="test_strat_4",
                service_state="11111",
            )

    def populate_db_reviews(self) -> None:
        with self.app.app_context():
            self.add_review(
                strategy_name="test_strat_1",
                timecheck="PASS",
                correlation_check="PASS",
                trade_distribution_check="PASS",
                risk_analysis="FAILED",
                num_days_trading="PASS",
                comments="Too slow",
            )
            self.add_review(
                strategy_name="test_strat_2",
                timecheck="FAIL",
                trade_distribution_check="PASS",
                risk_analysis="FAILED",
                num_days_trading="PASS",
            )

    def populate_db_deadsheet(self) -> None:
        with self.app.app_context():
            self.add_deadtrade(
                strategy="test_strat_2",
                exchange="NSE",
                slave_strat="ant_arm",
                exit_timestamp=datetime.datetime(2020, 5, 4, 10, 55),
                PnL=2570.1880154499277,
            )
            self.add_deadtrade(
                strategy="test_strat_2",
                exchange="NSE",
                slave_strat="ant_arm",
                exit_timestamp=datetime.datetime(2020, 5, 4, 10, 59),
                PnL=-17.51095815,
            )
            self.add_deadtrade(
                strategy="test_strat_2",
                exchange="NSE",
                slave_strat="ant_arm",
                exit_timestamp=datetime.datetime(2020, 5, 5, 10, 55),
                PnL=579.2917156000001,
            )
            self.add_deadtrade(
                strategy="test_strat_2",
                exchange="NSE",
                slave_strat="ant_arm",
                exit_timestamp=datetime.datetime(2020, 5, 6, 10, 55),
                PnL=4820.356280025,
            )
            self.add_deadtrade(
                segment="FUTIDX",
                exchange="NSE",
                slave_dev="test_manager",
                exit_timestamp=datetime.datetime(2020, 5, 6, 10, 55),
                exit_date=datetime.date(2020, 5, 6),
                PnL=4820.356280025,
            )

    def populate_db_strategies(self) -> None:
        with self.app.app_context():
            self.add_strategy(
                strategy_name="test_strat_1",
                developer="test_admin",
                segment="CASH",
                exchange_name="NSE",
                backtest_start_date=pd.Timestamp(2011, 1, 1),
                book_long="LC1",
                book_short="SC1",
                strat_state=1,
                submission_day=datetime.datetime.now(),
                trigger_coeff=100,
                limit_coeff=156,
                expiration_time=datetime.time(15, 15),
                long_short=-1,
                cluster_mapping=["cluster_test", "cluster_test_2"],
            )
            self.add_strategy(
                strategy_name="test_strat_2",
                developer="test_manager",
                segment="CASH",
                exchange_name="NSE",
                backtest_start_date=pd.Timestamp(2009, 1, 1),
                book_long="LC1",
                book_short="SC2",
                strat_state=1,
                submission_day=datetime.datetime.now(),
                trigger_coeff=500,
                limit_coeff=300,
                expiration_time=datetime.time(15, 15),
                long_short=-1,
                cluster_mapping=["cluster_test"],
                overlap_days=25,
            )
            self.add_strategy(
                strategy_name="test_strat_3",
                developer="test_developer",
                segment="FUTSTK",
                exchange_name="NSE",
                backtest_start_date=pd.Timestamp(2018, 1, 1),
                book_long="LC3",
                book_short="SC1",
                strat_state=1,
                submission_day=datetime.datetime.now(),
                trigger_coeff=100,
                limit_coeff=156,
                expiration_time=datetime.time(14, 45),
                long_short=-1,
                cluster_mapping=["cluster_kailash", "cluster_kailash_2"],
                comments="uses multiple indicators",
            )
            self.add_strategy(
                strategy_name="test_strat_4",
                developer="test_admin",
                segment="OPTIDX",
                exchange_name="NSE",
                backtest_start_date=pd.Timestamp(2011, 1, 1),
                book_long="LC0",
                book_short="SC1",
                strat_state=1,
                submission_day=datetime.datetime.now(),
                trigger_coeff=100,
                limit_coeff=156,
                expiration_time=datetime.time(13, 20),
                long_short=-1,
                cluster_mapping=["cluster_options"],
                overlap_days=10,
            )
            self.add_strategy(
                strategy_name="test_reworked",
                developer="test_manager",
                segment="CASH",
                exchange_name="NSE",
                backtest_start_date=pd.Timestamp(2009, 1, 1),
                book_long="LC1",
                book_short="SC2",
                strat_state=2,
                submission_day=datetime.datetime.now(),
                trigger_coeff=500,
                limit_coeff=300,
                expiration_time=datetime.time(15, 15),
                long_short=-1,
                cluster_mapping=["cluster_kailash"],
                overlap_days=25,
            )

    def populate_db_metadata(self) -> None:
        self.add_meta_values(
            strategy_name="test_strat_1",
            max_dd_submit=0.23,
            monthly_sharpe_submit=0.15,
            ret_dd_submit=0.32,
            max_dd_monte=0.56,
            percentile_dd=0.98,
            monthly_ret_live=145000,
            monthly_sharpe_live=0.1,
            max_dd_live=0,
            ret_dd_live=0.25,
            trading_days_live=55,
            daily_sharpe_live=10,
        )
        self.add_meta_values(
            strategy_name="test_strat_3",
            max_dd_submit=0.34,
            monthly_sharpe_submit=0.43,
            ret_dd_submit=0.65,
            max_dd_monte=0.25,
            percentile_dd=0.67,
            curr_backtest_dd=0.32,
            last_sentinel_run=pd.Timestamp(2022, 1, 1),
            monthly_ret_live=-34534.56,
            monthly_sharpe_live=-0.4,
            max_dd_live=89010,
            trading_days_live=132,
            daily_sharpe_live=4,
        )

    def tearDown(self) -> None:
        self.app = Flask(__name__)
        db.init_app(self.app)
        for file in self.files:
            if not file.closed:
                file.close()
        with self.app.app_context():
            db.drop_all()
