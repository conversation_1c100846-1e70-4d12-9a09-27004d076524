import unittest
from app.models import db, DeadSheet
from app.models import Strategy, PendingBacktests, StrategyAccess
from app.utility.strat_detail_util import (
    get_all_md_files,
    get_backend_service_state,
    get_backend_service_result,
    get_review_comments,
    get_strategies,
    get_charts_from_minio,
    get_mtm_from_minio,
    get_strategies_dd_percent,
    get_accessible_strategies,
    get_user_lists,
    remove_backtests,
    remove_access,
    get_live_pnl_curve,
    get_gandalf,
    get_available_historical_dates,
    get_strats_not_in_test_env,
    get_resetted_strats,
)
from tests.base_test_fixture import BaseTestFixture
import datetime
import pandas as pd
import numpy as np
from io import BytesIO


class TestStratDetails(BaseTestFixture):
    def setUp(self) -> None:
        super().setUp()
        Strategy.query.filter_by(strategy_name="test_strat_2").first().strat_state = 3
        Strategy.query.filter_by(strategy_name="test_strat_3").first().strat_state = 2
        db.session.commit()

    def test_get_backend_service_result(self) -> None:
        with self.app.app_context():
            pending_strats = Strategy.query.all()
            pending_strats_list = [strat.strategy_name for strat in pending_strats]
            backtest_results = get_backend_service_result(
                pending_strats=pending_strats_list
            )
            message = "backtest results fetching failed for {}!"
            self.assertEqual(
                backtest_results["test_strat_1"],
                "Pending",
                msg=message.format("test_strat_1"),
            )
            self.assertEqual(
                backtest_results["test_strat_2"],
                "DONE",
                msg=message.format("test_strat_2"),
            )
            self.assertEqual(
                backtest_results["test_strat_3"],
                "ERROR",
                msg=message.format("test_strat_3"),
            )
            new_backtest = PendingBacktests(
                request_time=datetime.datetime.now(),
                strategy_name="test_strat_1",
                service_state="22100",
            )
            db.session.add(new_backtest)
            db.session.commit()

            backtest_results = get_backend_service_result(
                pending_strats=["test_strat_1"]
            )
            self.assertEqual(
                backtest_results["test_strat_1"],
                "ERROR",
                msg=message.format("test_strat_1"),
            )

    def test_get_backend_service_state(self) -> None:
        with self.app.app_context():
            pending_strats = Strategy.query.all()
            pending_strats_list = [strat.strategy_name for strat in pending_strats]
            backtest_state = get_backend_service_state(
                pending_strats=pending_strats_list
            )
            message = "backtest results fetching failed for {}!"

            self.assertTrue(isinstance(backtest_state, dict))
            self.assertEqual(
                backtest_state["test_strat_1"],
                "00000",
                msg=message.format("test_strat_1"),
            )

            self.assertEqual(
                backtest_state["test_strat_4"],
                "11111",
                msg=message.format("test_strat_1"),
            )

    def test_get_review_comments(self) -> None:
        with self.app.app_context():
            pending_strats = Strategy.query.all()
            strat_review_comments_available = get_review_comments(
                strat_list=pending_strats
            )
            message = "{} review not found!"
            self.assertTrue(
                strat_review_comments_available["test_strat_1"],
                msg=message.format("test_strat_1"),
            )
            self.assertTrue(
                strat_review_comments_available["test_strat_2"],
                msg=message.format("test_strat_2"),
            )
            self.assertFalse(
                strat_review_comments_available["test_strat_3"],
                msg="test_strat_3 review was found but actually not there!",
            )

    def test_get_strategies_dd_percent(self) -> None:
        with self.app.app_context():
            pending_strats = Strategy.query.all()
            strat_with_dd = get_strategies_dd_percent(pending_strats)
            self.assertTrue(len(strat_with_dd), 4)
            self.assertTrue(
                isinstance(strat_with_dd, dict),
            )
            strategy = Strategy.query.filter_by(strategy_name="test_strat_3").first()
            self.assertTrue(strat_with_dd[strategy][0] - 0.94 < 0.01)
            self.assertTrue(strat_with_dd[strategy][1] - 1.28 < 0.01)

    def test_get_charts_from_minio(self) -> None:
        with self.app.app_context():
            from app.utility import strat_detail_util

            def mock_get_files_from_minio(
                status: str, strategy_name: str, post_fix: str, cluster_name: str = ""
            ):
                path = f"./tests/test_data/{strategy_name}{post_fix}"
                file = open(path, "rb")
                data = file.read()
                file.close()
                return data

            strat_detail_util.get_files_from_minio = mock_get_files_from_minio

            retrieval_url = get_charts_from_minio(strategy_name="test_strat_1")
            self.assertEqual(len(retrieval_url), 4, msg="length does not match!")
            self.assertEqual(
                ["dd", "monte", "ret", "kde"],
                list(retrieval_url.keys()),
                msg="keys do not match!",
            )
            retrieval_url = get_charts_from_minio(strategy_name="test_strat_2")
            self.assertEqual(len(retrieval_url), 0, msg="length does not match!")
            self.assertEqual(
                [],
                list(retrieval_url.keys()),
                msg="keys do not match!",
            )

    def test_get_mtm_from_minio(self) -> None:
        with self.app.app_context():
            mtm = get_mtm_from_minio(strategy_name="test_strat_1", status="pending")
            message = "mtm not fetched properly!"
            self.assertEqual(len(mtm), 1208, msg=message)
            self.assertAlmostEqual(mtm[0], -1493.3204115191068, msg=message)

    def test_get_live_pnl_curve(self) -> None:
        with self.app.app_context():

            from app.utility import strat_detail_util

            def mock_get_mtm_from_minio(strategy_name: str, status: str, week: str):
                path = "./tests/test_data/test_strat_2_MTM.log"
                if week == "2023_10_10":
                    path = "./tests/test_data/test_strat_2_2023_10_10_MTM.log"
                file = open(path, "rb")
                data = file.read()
                file.close()
                data = pd.read_csv(BytesIO(data))
                data["date"] = pd.to_datetime(data["date"])
                data = data.set_index("date")["mtm"]
                return data

            strat_detail_util.get_mtm_from_minio = mock_get_mtm_from_minio
            dead_trades = DeadSheet.get_strategy_dead_trades("test_strat_2")
            live_pnl_html = get_live_pnl_curve(
                strategy_name="test_strat_2",
                status="pending",
                start_day=datetime.date(2020, 2, 10),
                dead_trades=dead_trades,
            )
            self.assertIn("html", live_pnl_html)
            # get historical curve
            live_pnl_past = get_live_pnl_curve(
                strategy_name="test_strat_2",
                status="pending",
                start_day=datetime.date(2020, 2, 10),
                dead_trades=dead_trades,
                week="2023_10_10",
            )
            self.assertIn("html", live_pnl_past)

    def test_get_gandalf(self) -> None:
        with self.app.app_context():

            from app.utility import gandalf_util

            def mock_get_file_data_from_minio(path: str):
                file = open("./tests/test_data/ALL_DATES.npy", "rb")
                data = file.read()
                file.close()
                return data

            gandalf_util.get_file_data_from_minio = mock_get_file_data_from_minio
            dead_trades = DeadSheet.get_strategy_dead_trades("test_strat_2")
            gandalf = get_gandalf(dead_trades, exchange="NSE", segment="CASH")
            self.assertEqual(
                gandalf.loc[2020, "TradingDays"], 3, msg="Trading Days in gandalf"
            )
            self.assertEqual(
                gandalf.loc[2020, "TradeCount"], 4, msg="Trading Count in gandalf"
            )

    def test_get_strategies(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                message = "{} strats length does not match!"
                message_fetched = "{} strats records incorrectly fetched!"

                # Logged in as developer
                self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                strats_pending = get_strategies(state_description="PENDING")
                strats_live = get_strategies(state_description="LIVE")
                strats_dead = get_strategies(state_description="DEAD")
                strats_rejected = get_strategies(state_description="REJECTED")
                self.assertEqual(len(strats_pending), 0, msg=message.format("pending"))
                self.assertEqual(len(strats_live), 1, msg=message.format("live"))
                self.assertEqual(len(strats_dead), 0, msg=message.format("dead"))
                self.assertEqual(
                    len(strats_rejected), 0, msg=message.format("rejected")
                )

                strats_pending_list = [strat.strategy_name for strat in strats_pending]
                strats_live_list = [strat.strategy_name for strat in strats_live]
                strats_dead_list = [strat.strategy_name for strat in strats_dead]
                strats_rejected_list = [
                    strat.strategy_name for strat in strats_rejected
                ]
                self.assertEqual(
                    strats_pending_list,
                    [],
                    msg=message_fetched.format("pending"),
                )
                self.assertEqual(
                    strats_live_list,
                    ["test_strat_3"],
                    msg=message_fetched.format("live"),
                )
                self.assertEqual(
                    strats_dead_list, [], msg=message_fetched.format("dead")
                )
                self.assertEqual(
                    strats_rejected_list, [], msg=message_fetched.format("rejected")
                )
                self.client.get("/sign_out")

                # Logged in as manager
                self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )
                strats_pending = get_strategies(state_description="PENDING")
                strats_live = get_strategies(state_description="LIVE")
                strats_dead = get_strategies(state_description="DEAD")
                strats_rejected = get_strategies(state_description="REJECTED")
                self.assertEqual(len(strats_pending), 0, msg=message.format("pending"))
                self.assertEqual(len(strats_live), 2, msg=message.format("live"))
                self.assertEqual(len(strats_dead), 1, msg=message.format("dead"))
                self.assertEqual(
                    len(strats_rejected), 0, msg=message.format("rejected")
                )

                strats_pending_list = [strat.strategy_name for strat in strats_pending]
                strats_live_list = [strat.strategy_name for strat in strats_live]
                strats_dead_list = [strat.strategy_name for strat in strats_dead]
                strats_rejected_list = [
                    strat.strategy_name for strat in strats_rejected
                ]
                self.assertEqual(
                    strats_pending_list,
                    [],
                    msg=message_fetched.format("pending"),
                )
                self.assertEqual(
                    strats_live_list,
                    ["test_strat_3", "test_reworked"],
                    msg=message_fetched.format("live"),
                )
                self.assertEqual(
                    strats_dead_list,
                    ["test_strat_2"],
                    msg=message_fetched.format("dead"),
                )
                self.assertEqual(
                    strats_rejected_list, [], msg=message_fetched.format("rejected")
                )
                self.client.get("/sign_out")

                # Logged in as admin
                self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                strats_pending = get_strategies(state_description="PENDING")
                strats_live = get_strategies(state_description="LIVE")
                strats_dead = get_strategies(state_description="DEAD")
                strats_rejected = get_strategies(state_description="REJECTED")
                self.assertEqual(len(strats_pending), 2, msg=message.format("pending"))
                self.assertEqual(len(strats_live), 2, msg=message.format("live"))
                self.assertEqual(len(strats_dead), 1, msg=message.format("dead"))
                self.assertEqual(
                    len(strats_rejected), 0, msg=message.format("rejected")
                )

                strats_pending_list = [strat.strategy_name for strat in strats_pending]
                strats_live_list = [strat.strategy_name for strat in strats_live]
                strats_dead_list = [strat.strategy_name for strat in strats_dead]
                strats_rejected_list = [
                    strat.strategy_name for strat in strats_rejected
                ]
                self.assertEqual(
                    strats_pending_list,
                    [
                        "test_strat_1",
                        "test_strat_4",
                    ],
                    msg=message.format("pending"),
                )
                self.assertEqual(
                    strats_live_list,
                    ["test_strat_3", "test_reworked"],
                    msg=message.format("live"),
                )
                self.assertEqual(
                    strats_dead_list, ["test_strat_2"], msg=message.format("dead")
                )
                self.assertEqual(
                    strats_rejected_list, [], msg=message.format("rejected")
                )

    def test_get_accessible_strategies(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                _ = self.client.post(
                    "/strat_expand/add_user",
                    data={
                        "strategy_name": "test_strat_1",
                        "username": "test_developer",
                    },
                )
                self.client.get("/sign_out")

                # Logged in as developer
                self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )

                strats = get_accessible_strategies()
                self.assertEqual(
                    [strat.strategy_name for strat in strats],
                    ["test_strat_1"],
                    msg="accessible strats not fetched properly!",
                )

    def test_get_user_lists(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                message_user_list = "user_list incorrectly fetched!"
                message_users = "new users not fetched properly!"
                self.client.post(
                    "/login",
                    data={"email": self.email_admin, "password": self.password},
                )
                _ = self.client.post(
                    "/strat_expand/add_user",
                    data={
                        "strategy_name": "test_strat_1",
                        "username": "test_admin",
                    },
                )
                strategy = Strategy.query.filter_by(
                    strategy_name="test_strat_1"
                ).first()
                user_list, users = get_user_lists(strategy=strategy)
                self.assertEqual(user_list, [], msg=message_user_list)
                self.assertEqual(
                    set(users),
                    set(["test_developer", "test_manager"]),
                    msg=message_users,
                )
                self.client.get("/sign_out")

                # Logged in as developer
                self.client.post(
                    "/login",
                    data={"email": self.email_developer, "password": self.password},
                )
                user_list, users = get_user_lists(strategy=strategy)
                self.assertEqual(user_list, ["test_admin"], msg=message_user_list)
                self.assertEqual(users, ["test_manager"], msg=message_users)
                self.client.get("/sign_out")

                # Logged in as manager
                self.client.post(
                    "/login",
                    data={"email": self.email_manager, "password": self.password},
                )
                _ = self.client.post(
                    "/strat_expand/add_user",
                    data={
                        "strategy_name": "test_strat_3",
                        "username": "test_developer",
                    },
                )
                strategy = Strategy.query.filter_by(
                    strategy_name="test_strat_3"
                ).first()
                user_list, users = get_user_lists(strategy=strategy)
                self.assertEqual(user_list, ["test_developer"], msg=message_user_list)
                self.assertEqual(users, [], msg=message_users)
                self.client.get("/sign_out")

    def test_remove_backtests(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                remove_backtests(strategy_name="test_strat_1")
                self.assertEqual(
                    len(
                        PendingBacktests.query.filter_by(
                            strategy_name="test_strat_1"
                        ).all()
                    ),
                    0,
                    msg="backtests not removed properly",
                )

    def test_remove_access(self) -> None:
        with self.app.app_context():
            with self.app.test_request_context():
                strat_access = StrategyAccess(
                    strategy_name="test_strat_1", username="test_developer"
                )
                db.session.add(strat_access)
                db.session.commit()
                remove_access(strategy_name="test_strat_1")
                self.assertEqual(
                    len(
                        StrategyAccess.query.filter_by(
                            strategy_name="test_strat_1"
                        ).all()
                    ),
                    0,
                    msg="backtests not removed properly",
                )

    def test_get_available_historical_dates(self) -> None:
        with self.app.app_context():

            from app.utility import strat_detail_util

            def mock_list_files_in_folder(folder_name: str):
                if folder_name == "submitted_strats/developer_portfolio":
                    return [
                        "developer_portfolio/developer_portfolio_backtest_results.html",
                        "developer_portfolio/developer_portfolio_2023_10_11_backtest_results.html",
                        "developer_portfolio/developer_portfolio_var_report.html",
                        "developer_portfolio/developer_portfolio_2023_11_12_var_report.html",
                        "developer_portfolio/developer_portfolio_2023_11_19_var_report.html",
                    ]
                else:
                    return [
                        "manager_portfolio/manager_portfolio_var_report.html",
                        "manager_portfolio/manager_portfolio_2023_09_12_var_report.html",
                    ]

            strat_detail_util.list_files_in_folder = mock_list_files_in_folder
            files = get_available_historical_dates(["developer_portfolio"])
            assert len(files) == 1
            assert (type(files)) == dict
            assert (type(files["developer_portfolio"])) == list
            assert files["developer_portfolio"] == ["11-Oct-2023"]

            files = get_available_historical_dates(
                ["developer_portfolio", "manager_portfolio"], suffix="var_report"
            )
            assert len(files) == 2
            assert len(files["developer_portfolio"]) == 2
            assert files["manager_portfolio"] == ["12-Sep-2023"]

    def test_get_all_md_files(self) -> None:
        with self.app.app_context():
            from app.utility import strat_detail_util

            def mock_list_files_in_folder(folder_name: str):
                return [
                    "policies/policy2.md",
                    "policies/policy3.md",
                    "policies/policies.csv",
                ]

            def mock_get_file_data_from_minio(path: str):
                file = open(f"./tests/test_data/{path}", "rb")
                data = file.read()
                file.close()
                return data

            def mock_check_object_exists(prefix: str):
                return True

            strat_detail_util.list_files_in_folder = mock_list_files_in_folder
            strat_detail_util.get_file_data_from_minio = mock_get_file_data_from_minio
            strat_detail_util.check_object_exists = mock_check_object_exists
            files = get_all_md_files(
                path="policies/policies.csv", folder_name="policies"
            )
            assert len(files) == 2

    def test_get_strats_not_in_test_env(self) -> None:
        with self.app.app_context():

            Strategy.query.filter_by(
                strategy_name="test_strat_2"
            ).first().strat_state = 2
            Strategy.query.filter_by(
                strategy_name="test_reworked"
            ).first().reworked_strategy = "test_strat_2"
            Strategy.query.filter_by(
                strategy_name="test_reworked"
            ).first().strat_state = 5

            strats = get_strats_not_in_test_env()

            self.assertEqual(
                ["test_strat_2"],
                strats,
                msg="Live strats not in test env not fetched correctly",
            )

    def test_get_resetted_strats(self) -> None:
        with self.app.app_context():
            from app.utility import strat_detail_util

            def mock_get_file_data_from_minio(path: str):
                file = open(
                    "./tests/test_data/reworked_strategies_NSE.csv",
                    "rb",
                )
                data = file.read()
                file.close()
                return data

            def mock_check_object_exists(prefix: str):
                return True

            strat_detail_util.get_file_data_from_minio = mock_get_file_data_from_minio
            strat_detail_util.check_object_exists = mock_check_object_exists

            strats = get_resetted_strats(environment="LIVE")

            self.assertEqual(
                set(["test_strat_3"]),
                set(strats),
                msg="Resetted strats not read correctly",
            )


if __name__ == "__main__":
    unittest.main()
