{% extends "/base.html" %}
{% block content %}
    <main>
        <link rel="stylesheet"
              href="{{ url_for('static', filename='css/user_profile.css') }}"/>
        <br/>
        <div class="container col-xl-10 col-xxl-8 px-4 py-5"
             style="margin-top: -4em">
            <div class="row align-items-center g-lg-5 py-5">
                <div class="col-md-10 mx-auto col-lg-5">
                    <form method="post"
                          action="{{ url_for('auth.change_password') }}"
                          class="p-4 p-md-5 border rounded-3 bg-light">
                        {{ form.hidden_tag() }}
                        <div style="text-align : center;">
                            <p class="fw-bold">
                                {% if msg %}
                                    {{ msg | safe }}
                                {% else %}
                                    View your credentials
                                {% endif %}
                            </p>
                        </div>
                        <br />
                        <h3>
                            Email: <span>{{ current_user.email }}</span>
                        </h3>
                        <h3>
                            Username: <span>{{ current_user.username }}</span>
                        </h3>
                        {% if current_user.role.name == "ADMIN" %}
                            <h3>
                                Role: <span>ADMIN</span>
                            </h3>
                        {% elif current_user.role.name == "MANAGER" %}
                            <h3>
                                Role: <span>MANAGER</span>
                            </h3>
                        {% else %}
                            <h3>
                                Role: <span>DEVELOPER</span>
                            </h3>
                            <h3>
                                Manager: <span>{{ current_user.manager }}</span>
                            </h3>
                        {% endif %}
                        <div class="form-floating mb-3">
                            {% if form.new_password.errors %}
                                {{ form.new_password(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in errors %}<span>{{ error }}</span>{% endfor %}
                                </div>
                            {% else %}
                                {{ form.new_password(class="form-control") }}
                            {% endif %}
                            <label for="floatingInput">{{ form.new_password.label }}</label>
                        </div>
                        <div class="form-floating mb-3">
                            {% if form.confirm_new_password.errors %}
                                {{ form.confirm_new_password(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in errors %}<span>{{ error }}</span>{% endfor %}
                                </div>
                            {% else %}
                                {{ form.confirm_new_password(class="form-control") }}
                            {% endif %}
                            <label for="floatingPassword">{{ form.confirm_new_password.label }}</label>
                        </div>
                        <br />
                        <button class="w-100 btn btn-lg btn-primary" type="submit">
                            <span style="pointer-events: none;">{{ form.submit.label }}</span>
                        </button>
                        <br/>
                        <br/>
                        <a class="w-100 btn btn-lg btn-primary"
                           style="background: #aa0b0b;
                                  border: #aa0b0b"
                           href="{{ url_for('auth.sign_out') }}">
                            <img src="{{ url_for('static', filename='images/logout.png')}}"
                                 style="width : 30px"/>
                        Sign Out</a>
                    </form>
                    <div class="popup"
                         id="sucess_popup"
                         style="visibility : {{ vis_success }}">
                        <img src="{{ url_for('static', filename='images/tick.png')}}"/>
                        <h3>Success</h3>
                        <p>Your password was changed successfully</p>
                        <button type="button" id="sucess_close">OK</button>
                    </div>
                    <div class="popup" id="fail_popup" style="visibility : {{ vis_fail }}">
                        <img src="{{ url_for('static', filename='images/failed.png')}}"/>
                        <h3 style="margin-top:2em;">Failed</h3>
                        <p>Your password was not changed</p>
                        <button type="button" id="fail_close">OK</button>
                    </div>
                    <script>
                        sucess_close = document.getElementById("sucess_close");
                        fail_close = document.getElementById("fail_close");
                        sucess_popup = document.getElementById("sucess_popup");
                        fail_popup = document.getElementById("fail_popup");
                        sucess_close.onclick = function() {
                            sucess_popup.style.visibility = "hidden";
                        }
                        fail_close.onclick = function() {
                            fail_popup.style.visibility = "hidden";
                        }
                    </script>
                </div>
            </div>
        </div>
    </main>
{% endblock content %}
