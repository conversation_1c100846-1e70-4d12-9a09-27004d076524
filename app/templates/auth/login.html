{% extends "/base.html" %}
{% block content %}
    <div class="container col-xl-10 col-xxl-8 px-4 py-5">
        <div class="row align-items-center g-lg-5 py-5">
            <div class="col-md-10 mx-auto col-lg-5">
                <form method="post"
                      action="{{ url_for('auth.login') }}"
                      class="p-4 p-md-5 border rounded-3 bg-light">
                    {{ form.hidden_tag() }}
                    <p class="fw-bold">
                        {% if msg %}
                            {{ msg | safe }}
                        {% else %}
                            Add your credentials
                        {% endif %}
                    </p>
                    <br />
                    <div class="form-floating mb-3">
                        {% if form.email.errors %}
                            {{ form.email(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in errors %}<span>{{ error }}</span>{% endfor %}
                            </div>
                        {% else %}
                            {{ form.email(class="form-control") }}
                        {% endif %}
                        <label for="floatingInput">{{ form.email.label }}</label>
                    </div>
                    <div class="form-floating mb-3">
                        {% if form.password.errors %}
                            {{ form.password(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in errors %}<span>{{ error }}</span>{% endfor %}
                            </div>
                        {% else %}
                            {{ form.password(class="form-control") }}
                        {% endif %}
                        <label for="floatingPassword">{{ form.password.label }}</label>
                    </div>
                    <br />
                    <div class="checkbox mb-3">
                        {% if form.remember_me.errors %}
                            {{ form.remember_me(class="form-check-input is-invalid", type="checkbox") }}
                            <div class="invalid-feedback">
                                {% for error in errors %}<span>{{ error }}</span>{% endfor %}
                            </div>
                        {% else %}
                            {{ form.remember_me(class="form-check-input", type="checkbox") }}
                        {% endif %}
                        {{ form.remember_me.label(class="form-check-label") }}
                    </div>
                    <button class="w-100 btn btn-lg btn-primary" type="submit">
                        <span style="pointer-events: none;">{{ form.submit.label }}</span>
                    </button>
                </form>
            </div>
        </div>
    </div>
{% endblock content %}
