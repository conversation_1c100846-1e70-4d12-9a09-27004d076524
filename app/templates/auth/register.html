{% extends "/base.html" %}
{% block content %}
    <div class="container col-xl-10 col-xxl-8 px-4 py-5">
        <div class="row align-items-center g-lg-5 py-5">
            <div class="col-md-10 mx-auto col-lg-5">
                <form method="post"
                      action="{{ url_for('auth.register') }}"
                      class="p-4 p-md-5 border rounded-3 bg-light"
                      id="register_form">
                    {{ form.hidden_tag() }}
                    <div style="text-align : center">
                        <p class="fw-bold">
                            {% if msg %}
                                {{ msg | safe }}
                            {% else %}
                                Create a new account
                            {% endif %}
                        </p>
                        <br />
                    </div>
                    <div class="form-floating mb-3">
                        {% if form.username.errors %}
                            {{ form.username(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.username.errors %}<span>{{ error }}</span>{% endfor %}
                            </div>
                        {% else %}
                            {{ form.username(class="form-control") }}
                        {% endif %}
                        <label for="floatingInput">{{ form.username.label }}</label>
                    </div>
                    <div class="form-floating mb-3">
                        {% if form.email.errors %}
                            {{ form.email(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.email.errors %}<span>{{ error }}</span>{% endfor %}
                            </div>
                        {% else %}
                            {{ form.email(class="form-control") }}
                        {% endif %}
                        <label for="floatingInput">{{ form.email.label }}</label>
                    </div>
                    <div class="form-floating mb-3">
                        {% if form.password.errors %}
                            {{ form.password(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.password.errors %}<span>{{ error }}</span>{% endfor %}
                            </div>
                        {% else %}
                            {{ form.password(class="form-control", type="password") }}
                        {% endif %}
                        <label for="floatingPassword">{{ form.password.label }}</label>
                    </div>
                    <div class="form-floating mb-3">
                        {% if form.confirm_password.errors %}
                            {{ form.confirm_password(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.confirm_password.errors %}<span>{{ error }}</span>{% endfor %}
                            </div>
                        {% else %}
                            {{ form.confirm_password(class="form-control", type="password") }}
                        {% endif %}
                        <label for="floatingPassword">{{ form.confirm_password.label }}</label>
                    </div>
                    <div class="form-floating mb-3">
                        {% if form.roles.errors %}
                            <select name="{{ form.roles.name }}"
                                    id="{{ form.roles.id }} "
                                    class="form-control is-invalid">
                                <option disabled selected value>
                                    -- select an option --
                                </option>
                                {% for item in form.roles.choices %}
                                    <option value={{ item[0] }}>{{ item[1] }}
                                    </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                {% for error in form.roles.errors %}<span>{{ error }}</span>{% endfor %}
                            </div>
                        {% else %}
                            <select name="{{ form.roles.name }}"
                                    id="{{ form.roles.id }} "
                                    class="form-control">
                                <option disabled selected value>
                                    -- select an option --
                                </option>
                                {% for item in form.roles.choices %}
                                    <option value={{ item[0] }}>{{ item[1] }}
                                    </option>
                                {% endfor %}
                            </select>
                        {% endif %}
                        <label for="floatingInput">{{ form.roles.label }}</label>
                    </div>
                    <div class="form-floating mb-3" id="manager">
                        {% if form.manager.errors %}
                            <select name="{{ form.manager.name }}"
                                    id="{{ form.manager.id }} "
                                    class="form-control is-invalid">
                                <option disabled selected value>
                                    -- select an option --
                                </option>
                                {% for item in form.manager.choices %}
                                    <option value={{ item }}>{{ item }}
                                    </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                {% for error in form.manager.errors %}<span>{{ error }}</span>{% endfor %}
                            </div>
                        {% else %}
                            <select name="{{ form.manager.name }}"
                                    id="{{ form.manager.id }} "
                                    class="form-control">
                                <option disabled selected value>
                                    -- select an option --
                                </option>
                                {% for item in form.manager.choices %}
                                    <option value={{ item }}>{{ item }}
                                    </option>
                                {% endfor %}
                            </select>
                        {% endif %}
                        <label for="floatingInput">{{ form.manager.label }}</label>
                    </div>
                    <br />
                    <button class="w-100 btn btn-lg btn-primary" type="submit">
                        <span style="pointer-events: none;">{{ form.submit.label }}</span>
                    </button>
                    <hr class="my-4"/>
                </form>
            </div>
        </div>
    </div>
    <script src = "{{ url_for('static', filename='scripts/register.js') }}"></script>
{% endblock content %}
