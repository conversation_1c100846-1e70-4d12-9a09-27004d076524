{% extends "/base.html" %}
{% block content %}
    <main>
        <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
        <link rel="stylesheet"
              href="{{ url_for('static', filename='css/strat_review.css') }}"/>
        <div class="container p-5">
            <div class="d-flex align-items-center justify-content-center">
                <h1 style="font-size:30px;">User Management Page</h1>
            </div>
            <div style="text-align: right;">
                <a href="{{ url_for('auth.register')}}"
                   type="button"
                   class="btn btn-success btn-sm px-4 gap-3"
                   style="color: #fff;
                          font-weight : 600;
                          font-size:15px"> Register New User </a>
            </div>
            <table class="styled-table" id="strats">
                <caption> *Manage users </caption>
                <thead>
                    <tr style="font-size : 14px;">
                        <th>Username</th>
                        <th>Email</th>
                        <th>Role</th>
                        {% if current_user.role.name == "ADMIN" %}<th>Manager</th>{% endif %}
                        <th>Edit</th>
                        <th>Delete</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in user_list %}
                        <tr style="font-size : 14px;">
                            <td class="user_name">{{ user.username }}</td>
                            <td>{{ user.email }}</td>
                            <td>{{ user.role.name }}</td>
                            {% if current_user.role.name == "ADMIN" %}<td>{{ user.manager }}</td>{% endif %}
                            <td>
                                <button type="button"
                                        class="btn btn-warning"
                                        style="color: #000;
                                               font-weight : 600;
                                               font-size:13px"
                                        id="edit_btn"
                                        value="{{ user.username }}"
                                        name="edit_buttons">
                                    Edit
                                </button>
                            </td>
                            <td>
                                <button type="submit"
                                        class="btn btn-danger"
                                        style="color: #000;
                                               font-weight : 600;
                                               font-size:13px"
                                        value="{{ user.username }}"
                                        id="delete_btn"
                                        name="delete_buttons">
                                    Delete
                                </button>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <div class="popup" id="edit_toggler_popup" style="visibility:hidden;">
            <img src="{{ url_for('static', filename='images/edit.png')}}"/>
            <h3 id="username_edit"
                style="margin-top:1em;
                       margin-bottom: 1em;
                       font-size:16px"></h3>
            <div style="display:flex;">
                {% if current_user.role.name == "ADMIN" %}
                    <button type="button"
                            id="role_changer_btn"
                            style="width:50%;
                                   margin-right:0.5em;
                                   background:#669999">
                        Change Role
                    </button>
                    <button type="button"
                            id="pass_changer_btn"
                            style="width:50%;
                                   background:#669999">Change Password</button>
                {% else %}
                    <button type="button" id="role_changer_btn" style="display:none;">Change Role</button>
                    <button type="button"
                            id="pass_changer_btn"
                            style="width:100%;
                                   background:#669999">Change Password</button>
                {% endif %}
            </div>
            <button type="button" id="edit_user_close" style="margin-top:1em;">Cancel</button>
        </div>
        <div class="popup" id="edit_user_popup" style="visibility : hidden;">
            <img src="{{ url_for('static', filename='images/edit.png')}}"/>
            <div id="role_editter"
                 style="display:flex;
                        justify-content:space-around;
                        text-align : center;
                        flex-direction:column">
                <h3 id="edit_btn_username" style="margin-top:1em; font-size:16px;"></h3>
                <div style="display:flex;
                            justify-content:space-around;
                            text-align : center;
                            flex-direction:column">
                    <label for "role" style="margin-bottom:0.5em;">Select a role:</label>
                    <select name="role" id="role" style="margin-bottom:2em; text-align:center;">
                        {% for role in roles %}
                            <option value={{ role }}>{{ role }}
                            </option>
                        {% endfor %}
                    </select>
                    <div style="display:none; flex-direction:column;" id="manager_div">
                        <label for "manager" style="margin-bottom:0.5em;">Select a manager:</label>
                        <select name="manager"
                                id="manager"
                                style="margin-bottom:2em;
                                       text-align:center">
                            {% for manager in managers %}
                                <option value={{ manager }}>{{ manager }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div style="display:flex;">
                    <button type="button"
                            id="edit_confirm"
                            style="width:50%;
                                   margin-right:0.5em;
                                   background:#6fd649">Confirm</button>
                    <button type="button" id="edit_close" style="width:50%;">Cancel</button>
                </div>
            </div>
        </div>
        <div class="popup" id="password_change_popup" style="visibility : hidden">
            <img src="{{ url_for('static', filename='images/edit.png')}}"/>
            <div style="display:flex;
                        justify-content:center;
                        text-align : center;
                        flex-direction:column">
                <h3 id="pass_change_username" style="margin-top:1em; font-size:16px;">
                </h3>
                <input type="password"
                       id="password"
                       style="text-align:center;
                              border:2px solid black;
                              margin-left:0em;
                              margin-top:1em;
                              border-radius:4px 4px"
                       placeholder="Enter Password"/>
                <input type="password"
                       id="confirm_password"
                       style="text-align:center;
                              border:2px solid black;
                              margin-left:0em;
                              margin-top:1em;
                              border-radius:4px 4px"
                       placeholder="Confirm password"/>
                <div style="display:flex; margin-top:1em;">
                    <button type="button"
                            id="pass_change_confirm"
                            style="width:50%;
                                   margin-right:0.5em;
                                   background:#6fd649">
                        Confirm
                    </button>
                    <button type="button" id="pass_change_close" style="width:50%;">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
        <div class="popup" id="sucess_popup" style="visibility : hidden;">
            <img src="{{ url_for('static', filename='images/tick.png')}}"/>
            <h3>
                Success
            </h3>
            <p id="reason_success">
            </p>
            <button type="button" id="success_popup_close" style="background:#6fd649">
                OK
            </button>
        </div>
        <div class="popup" id="failure_popup" style="visibility : hidden;">
            <img src="{{ url_for('static', filename='images/failed.png')}}"/>
            <h3>
                Failed
            </h3>
            <p id="reason_failure">
            </p>
            <button type="button" id="failure_popup_close">
                OK
            </button>
        </div>
    </main>
    <script src="{{ url_for('static', filename='scripts/user_management.js') }}"></script>
{% endblock content %}
