{% extends "base.html" %}
{% block content %}
    <main>
        <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
        <link rel="stylesheet"
              href="{{ url_for('static', filename='css/slip_home.css') }}"/>
        <div class="page_heading">
            <h1>Slippage Analysis</h1>
        </div>
        <div class="dropdown-div">
            <select class="form-select"
                    aria-label="Default select example"
                    id="segment-dropdown">
                {% for segment in segment_list %}
                    {% if segment==current_segment %}
                        <option value='{{ segment }}' selected="selected">
                            {{ segment }}
                        </option>
                    {% else %}
                        <option value='{{ segment }}'>
                            {{ segment }}
                        </option>
                    {% endif %}
                {% endfor %}
            </select>
            <button type="button" id="timewise_graph">Time Graph</button>
        </div>
        <div class="slippage-docs">
            <p>
                For information regarding different kind of slippages, click
                <a href="http://*************:17995/slippage_type/" target="_blank">here!</a>
            </p>
        </div>
        <div class="table-wrapper">
            <div class="table-child">
                <h3>
                    <span>Overall Analysis</span>
                </h3>
                <div class="tbl-header">
                    <table>
                        <thead>
                            <tr>
                                <th>Duration</th>
                                <th>Overall Slippage</th>
                                <th>Buy Side Slippage</th>
                                <th>Sell Side Slippage</th>
                                <th>Notional (Cr.)</th>
                            </tr>
                        </thead>
                    </table>
                </div>
                <div class="tbl-content-first">
                    <table id="table_overall_slippage">
                        <tbody>
                            {% for duration, slippage_data in segment_overall_slip.items() %}
                                <tr>
                                    <td>{{ duration }}</td>
                                    <td>{{ slippage_data[0]|round(2) }}</td>
                                    <td>{{ slippage_data[1]|round(2) }}</td>
                                    <td>{{ slippage_data[2]|round(2) }}</td>
                                    <td>{{ slippage_data[3]|round(2) }}</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="table-child">
                <h3>
                    <span>{{ current_segment }} Slippages</span>
                </h3>
                <div class="tbl-header">
                    <table>
                        <thead>
                            <tr>
                                <th>Day</th>
                                <th>Signal Slippage</th>
                                <th>Execution Slippage</th>
                                <th>Trade Slippage</th>
                                <th>Notional (Cr.)</th>
                            </tr>
                        </thead>
                    </table>
                </div>
                <div class="tbl-content">
                    <table id="table_day_slippage">
                        <tbody>
                            {% for day, slippage_data in segment_slippage_dict.items() %}
                                <tr>
                                    <td>{{ day }}</td>
                                    <td>{{ slippage_data[0]|round(2) }}</td>
                                    <td>{{ slippage_data[1]|round(2) }}</td>
                                    <td>{{ slippage_data[2]|round(2) }}</td>
                                    <td>{{ slippage_data[3]|round(2) }}</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="table-child", id="slip_chart"></div>
            <div class="table-child", id="exp_chart"></div>
            </div>
        </div>
        <div class="popup" id="time_graph_popup" style="visibility: hidden;">
            <h3 style="margin-top:1em; margin-bottom: 1em; font-size:18px; font-weight: bold;"> {{ current_segment }}</h3>
            <div style="display:flex;">
                <div style="margin: 10px;">
                    <label for="date">Start date:</label>
                    <input type="date" id="start_date" name="start_date" class="styled-date" onchange="checkDateInput()"/>
                </div>
                <div style="margin: 10px;">
                    <label for="date">End date:</label>
                    <input type="date" id="end_date" name="end_date" class="styled-date" onchange="checkDateInput()"/>
                </div>
            </div>
            <button type="button" id="time_graph_generate" style="margin-top:1em; background: #29a329;" disabled>Generate</button>
            <button type="button" id="time_graph_popup_close" style="margin-top:1em; background: #b30000">Cancel</button>
        </div>
        <div class="popup" id="wrong_date_popup" style="visibility : hidden">
            <h4>Failed</h4>
            <p style="fnt-size:14px; font-weight:500;">Start date should be less than end date</p>
            <button type="button" id="wrong_date_popup_close" style="background: #b30000">OK</button>
        </div>
    </main>
    <script src='https://cdn.plot.ly/plotly-latest.min.js'></script>
    <script src="{{ url_for('static', filename='scripts/slip_home.js') }}"></script>
    <script type='text/javascript'>
        var graphs = {{ graph_dict['day_slippage'] | safe}};
        Plotly.plot('slip_chart', graphs, {}); 
        var graphs = {{ graph_dict['day_exposure'] | safe}};
        Plotly.plot('exp_chart', graphs, {});
    </script>
{% endblock content %}
