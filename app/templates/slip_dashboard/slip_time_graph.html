{% extends "base.html" %}
{% block content %}
    <main>
        <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
        <link rel="stylesheet"
              href="{{ url_for('static', filename='css/slip_home.css') }}"/>
        <div class="page_heading_secondary">
            <h1>{{ current_segment }} Slippage Across the Day</h1>
        </div>
        <div class="table-wrapper">
            <div class="table-child", id="buy_time_slip_chart">
            </div>
            <div class="table-child", id="buy_time_exp_chart">
            </div>
            <div class="table-child", id="sell_time_slip_chart">
            </div>
            <div class="table-child", id="sell_time_exp_chart">
            </div>
        </div>
    </main>
    <script src='https://cdn.plot.ly/plotly-latest.min.js'></script>
    <script type='text/javascript'>
        var graphs = {{ graph_dict['buy_timewise_slippage'] | safe}};
        Plotly.plot('buy_time_slip_chart', graphs, {});
        var graphs = {{ graph_dict['buy_timewise_exposure'] | safe}};
        Plotly.plot('buy_time_exp_chart', graphs, {});
        var graphs = {{ graph_dict['sell_timewise_slippage'] | safe}};
        Plotly.plot('sell_time_slip_chart', graphs, {});
        var graphs = {{ graph_dict['sell_timewise_exposure'] | safe}};
        Plotly.plot('sell_time_exp_chart', graphs, {});
    </script>
{% endblock content %}
