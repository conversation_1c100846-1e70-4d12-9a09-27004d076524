{% extends "base.html" %}
{% block content %}
    <main>
        <link rel="stylesheet"
              href="{{ url_for('static', filename='css/strategy_review_display.css') }}"/>
        <div style="margin-top:2em; text-align:center;">
            <h1 class="display-5 fw-bold" style="font-size : 30px;">To Do Dashboard</h1>
        </div>
        <div style="display:flex;
                    justify-content:center;
                    margin-top:1em;
                    padding-bottom:4em">
            <table class="my-table">
                <thead>
                    <tr>
                        <th>Strategy Name</th>
                        {% if current_user.role.name != "DEVELOPER" %}
                            <th>Developer</th>
                        {% endif %}
                        <th>To Do</th>
                    </tr>
                </thead>
                <tbody>
                    {% for strategy, todo in todo_list.items() %}
                        <tr>
                            {% if todo != None %}
                                <td style="font-weight:600;">{{ strategy.strategy_name }}</td>
                                {% if current_user.role.name != "DEVELOPER" %}
                                    <td>{{ strategy.developer }}</td>
                                {% endif %}
                                <td style="text-align:left;">{{ todo }}</td>
                            {% endif %}
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </main>
    <script src="{{ url_for('static', filename='scripts/strategy_review_display.js') }}"></script>
{% endblock content %}
