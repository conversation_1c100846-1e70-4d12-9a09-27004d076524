{% extends "base.html" %}
{% block content %}
    <main>
        <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
        <link href='https://fonts.googleapis.com/css?family=Expletus Sans'
              rel='stylesheet'/>
        <link rel="stylesheet"
              href="{{ url_for('static', filename='css/strat_review.css') }}"/>
        <div class="px-4 py-5 my-5 text-center">
            <h1 class="display-5 fw-bold" style="margin-top: -2em; font-size : 30px">Submitted Strategies</h1>
            <div class="d-grid gap-2 d-sm-flex justify-content-sm-center">Logged in as: {{ current_user.username }}</div>
            <a href="{{ url_for('strat_add.add_strategy') }}" id="strat_add_button" class="btn btn-warning mt-3" style="color: black;
            font-weight : 600;
            border: 0.5px solid black" >
                Add Strategy
            </a>
            <div>
                <div class="legend-container" style="margin-bottom: -10px; margin-right: 60px">
                    <ul class="legend">
                        <li><span class="color-item" style="background-color: #c6daf1;"></span>Strategies running in test-only mode</li>
                    </ul>
                </div>
            </div>
            
            <div class="px-5">
                <table id="data" class="styled-table">
                    <thead>
                        <tr style="font-size : 16px;">
                            <th>Strategy Name</th>
                            <th>Segment Name</th>
                            <th>Exchange name</th>
                            <th>Long/Short</th>
                            <th>Modify</th>
                            <th>Review Comments</th>
                            <th>Details</th>
                            <th>Delete</th>
                            <th>Clear Pickle</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for strategy in submitted_strategies %}
                            <tr style="font-size : 14px;">
                                <td class="strategy_name">{{ strategy.strategy_name }}</td>
                                <td>{{ strategy.segment }}</td>
                                <td>{{ strategy.exchange_name }}</td>
                                <td>{{ strategy.long_short }}</td>
                                <td>
                                    <a href="{{ url_for('strat_add.modify_strategy', strat_name=strategy.strategy_name) }}"
                                       type="button"
                                       class="btn btn-success btn-sm"
                                       style="color: #fff;
                                              font-weight : 600;
                                              border:none"> Modify </a>
                                </td>
                                <td>
                                    {% if review_comments[strategy.strategy_name] == True %}
                                        <a href="{{ url_for('strat_add.show_strategy_review', strategy_name=strategy.strategy_name) }}"
                                           title="View Comments"
                                           type="button"
                                           class="btn btn-info btn-sm"
                                           style="color: #fff;
                                                  font-weight : 600;
                                                  background: #371E29;
                                                  border:none"> Comments </a>
                                    {% else %}
                                        <button type="button"
                                                title="Review comments not available"
                                                class="btn btn-warning btn-sm"
                                                style="pointer-events:auto;
                                                       cursor: not-allowed;
                                                       color: #fff;
                                                       font-weight : 600;
                                                       background: #371E29;
                                                       border:none"
                                                disabled>
                                            Comments
                                        </button>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('strat_add.expand_strategy', strategy_name=strategy.strategy_name) }}"
                                       type="button"
                                       class="btn btn-primary btn-sm"
                                       target="_blank"
                                       style="color: #fff;
                                              font-weight : 600"> Expand </a>
                                </td>
                                <td>
                                    <form action="{{ url_for('strat_add.delete_strategy', strategy_name=strategy.strategy_name, calling_location='pending_strats') }}"
                                          method="post">
                                        <input class="btn btn-danger btn-sm"
                                               type="submit"
                                               style="font-weight:600"
                                               value="Delete"
                                               onclick="return confirm('Are you sure you want to delete this strategy?');"/>
                                    </form>
                                </td>
                                <td>N/A</td>
                            </tr>
                        {% endfor %}
                        <!-- Showing test strategies of current user  -->
                        {% for strategy in test_strategies %}
                            <tr style="font-size : 14px; background-color: #c6daf1;">
                                <td class="strategy_name">{{ strategy.strategy_name }}</td>
                                <td>{{ strategy.segment }}</td>
                                <td>{{ strategy.exchange_name }}</td>
                                <td>{{ strategy.long_short }}</td>
                                <td>N/A</td>
                                <td>
                                    {% if review_comments[strategy.strategy_name] == True %}
                                        <a href="{{ url_for('strat_add.show_strategy_review', strategy_name=strategy.strategy_name) }}"
                                           title="View Comments"
                                           type="button"
                                           class="btn btn-info btn-sm"
                                           style="color: #fff;
                                                  font-weight : 600;
                                                  background: #371E29;
                                                  border:none"> Comments </a>
                                    {% else %}
                                        <button type="button"
                                                title="Review comments not available"
                                                class="btn btn-warning btn-sm"
                                                style="pointer-events:auto;
                                                       cursor: not-allowed;
                                                       color: #fff;
                                                       font-weight : 600;
                                                       background: #371E29;
                                                       border:none"
                                                disabled>
                                            Comments
                                        </button>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('strat_add.expand_strategy', strategy_name=strategy.strategy_name) }}"
                                       type="button"
                                       class="btn btn-primary btn-sm"
                                       target="_blank"
                                       style="color: #fff;
                                              font-weight : 600"> Expand </a>
                                </td>
                                <td>N/A</td>
                                <td>
                                    {% if strategy.strategy_name in resetted_strats %}
                                    <button type="button"
                                            name="pickle_reset_btns"
                                            value={{strategy.strategy_name}}
                                            class="btn btn-secondary btn-sm">
                                            UnReset Test
                                    </button>
                                    {% else %}
                                    <button type="button"
                                            name="pickle_reset_btns"
                                            value={{strategy.strategy_name}}
                                            class="btn btn-primary btn-sm">
                                            Reset Test
                                    </button>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <br />
        </div>
        <div class="popup" id="sucess_popup" style="visibility : hidden;">
            <img src="{{ url_for('static', filename='images/tick.png')}}"/>
            <h3>
                Success
            </h3>
            <p id="reason_success">
            </p>
            <button type="button" id="success_popup_close" style="background:#6fd649">
                OK
            </button>
        </div>
        <div class="popup" id="failure_popup" style="visibility : hidden;">
            <img src="{{ url_for('static', filename='images/failed.png')}}"/>
            <h3>
                Failed
            </h3>
            <p id="reason_failure">
            </p>
            <button type="button" id="failure_popup_close">
                OK
            </button>
        </div>
    </main>
    <script src="{{ url_for('static', filename='scripts/utility.js') }}"></script>
    <script src="{{ url_for('static', filename='scripts/pending_strats.js') }}"></script>
{% endblock content %}
