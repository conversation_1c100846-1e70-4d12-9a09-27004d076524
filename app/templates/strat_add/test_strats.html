{% extends "base.html" %}
{% block content %}
  <main>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <link rel="stylesheet"
          href="{{ url_for('static', filename='css/strat_review.css') }}"/>
    <div class="container p-5">
      <div class="d-flex align-items-center justify-content-center">
        <h1 style="font-size:30px;">Test Mode Strategies</h1>
      </div>
    </div>
    <div style="padding:0em 4em; margin-top:-2em;">
      <div class="table-container">
      <div class="tbl-header">
      <table class="styled-table" id="strats">
        <thead>
          <tr style="font-size: 16px;">
            <th>
                <div class="checkbox-container">
                    <input type="checkbox" id="checkbox_all" style="border: 0.15em solid #000" />
                </div>
          </th>
          <th name="sortable_headers_review">Date</th>
          <th name="sortable_headers_review">Strategy Name</th>
          <th name="sortable_headers_review">Developer Name</th>
          <th name="sortable_headers_review">Segment Name</th>
          <th name="sortable_headers_review">Cluster (Test)</th>
          <th name="sortable_headers_review">Cluster (Live)</th>
          <th name="sortable_headers_review">Clear Pickle</th>
        </tr>
        </thead>
      </table>
    </div>
    <div class="tbl-content">
      <table id="strats_body">
        <tbody>
        <!-- Showing strategies in test-only mode -->
          {% for strategy in test_mode_strategies %}
          <tr style="font-size : 14px;">
            {% set access_count = strategy.strategy_access.all()|length %}
            <td>
                <div class="checkbox-container">
                    <input type="checkbox" class="check" name="checkbox_input"/>
                </div>
            </td>
            <td>{{ strategy.submission_day }}</td>
            <td name="strat_name" class="clickable_cell"value={{ strategy.strategy_name }}>{{ strategy.strategy_name }}
            {% if access_count > 0 %}
            <span class="numberCircle">{{ access_count }}</span>
            {% endif %}
            </td>
        <td>{{ strategy.developer }}</td>
        <td>{{ strategy.segment }}</td>
        <td>
            {% set flag = namespace(val='None') %}
            {% for cluster in strategy.cluster_mapping %}
                {% if cluster.mapping_state == 1 %}
                    {% set flag.val = '' %}
                    {{ cluster.cluster_name }}&nbsp;&nbsp;
                {% endif %}
            {% endfor %}
            {{flag.val}}
        </td>
        <td>
            {% set flag = namespace(val='None') %}
            {% for cluster in strategy.cluster_mapping %}
                {% if cluster.mapping_state == 0 %}
                    {% set flag.val = '' %}
                    {{ cluster.cluster_name }}&nbsp;&nbsp;
                {% endif %}
            {% endfor %}
            {{flag.val}}
        </td>
        <td>
            {% if strategy.strategy_name in resetted_strats %}
            <button type="button"
                    name="pickle_reset_btns"
                    value={{strategy.strategy_name}}
                    class="btn btn-secondary btn-sm">
                    UnReset Test
            </button>
            {% else %}
            <button type="button"
                    name="pickle_reset_btns"
                    value={{strategy.strategy_name}}
                    class="btn btn-primary btn-sm">
                    Reset Test
            </button>
            {% endif %}
        </td>
         </tr>
          {% endfor %}
        </tbody>
  </table>
    </div>
  </div>
  <div style="display:flex;
              justify-content: space-around;
              margin-top:2em;
              padding-bottom:4em">
    <a href="{{ url_for('strat_add.comments_dashboard') }}"
       type="button"
       class="btn btn-danger"
       style="color: #fff;
              font-weight : 600;
              font-size:15px"
       target="_blank"> Comments Dashboard </a>
    {% if current_user.role.name != "DEVELOPER" %}
      {% set display_download_btn = "block" %}
    {% else %}
      {% set display_download_btn = "none" %}
    {% endif %}
    <button type="button"
            class="btn btn-warning"
            id="btn-download"
            style="color: #000;
                   font-weight : 600;
                   font-size:15px;
                   display:{{ display_download_btn }}">
      Download
    </button>
  </div>
  <div class="popup" id="fail_popup" style="visibility : hidden">
    <img src="{{ url_for('static', filename='images/failed.png')}}"/>
    <h3 style="margin-top : 0.5em;">Failed</h3>
    <p id="fail_reason">Your request could not be processed</p>
    <button type="button" id="fail_close">OK</button>
  </div>
  <div class="popup" id="encryption_popup" style="visibility : hidden">
    <img src="{{ url_for('static', filename='images/key.png')}}"/>
    <h3 style="margin-top : 0.5em;">
      Encryption Key Required
    </h3>
    <p>
      Please upload your encryption key file
    </p>
    <input type="file" id="file" name="file"/>
    <br/>
    <br/>
    <div style="display:flex">
      <button type="button"
              id="encryption_submit"
              style="width:50%;
                     margin-right:0.5em;
                     background-color: #6fd649">
        Submit
      </button>
      <button type="button" id="encryption_close" style="width:50%;">
        Cancel
      </button>
    </div>
  </div>
</div>
 <!-- Success/failure popups for clear pickle buttons -->
<div class="popup" id="sucess_popup" style="visibility : hidden;">
    <img src="{{ url_for('static', filename='images/tick.png')}}"/>
    <h3>
        Success
    </h3>
    <p id="reason_success">
    </p>
    <button type="button" id="success_popup_close" style="background:#6fd649">
        OK
    </button>
</div>
<div class="popup" id="failure_popup" style="visibility : hidden;">
    <img src="{{ url_for('static', filename='images/failed.png')}}"/>
    <h3>
        Failed
    </h3>
    <p id="reason_failure">
    </p>
    <button type="button" id="failure_popup_close">
        OK
    </button>
    </div>
</main>
<script src="{{ url_for('static', filename='scripts/tablefilter/tablefilter.js') }}"></script>
<script src="{{ url_for('static', filename='scripts/utility.js') }}"></script>
<script src="{{ url_for('static', filename='scripts/test_strats.js') }}"></script>
{% endblock content %}
