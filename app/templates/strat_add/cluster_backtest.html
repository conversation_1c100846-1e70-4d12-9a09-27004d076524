{% extends "/base.html" %}
{% block content %}
    <link rel="stylesheet"
          href="{{ url_for('static', filename='css/cluster_backtest.css') }}"/>
    <div class="page_heading">
        <h1>Cluster Backtest</h1>
    </div>
    <div class="container col-xl-10 col-xxl-8 px-4 py-5">    
        <div style="width:100%; padding:0em 4em; margin-top:-2em;">
            <table class="styled-table" >
              <thead>
                <tr style="font-size : 16px;">
                  <th>Cluster</th>
                  <th>Requested By</th>
                  <th>Backtest Result</th>
                  <th>Details</th>
                  <th>Delete</th>
                  <th>Results</th>
                </tr>
              </thead>
              <tbody>
                {% for cluster_info in cluster_backtest_dict %}
                <tr style="font-size : 14px;">
                    <td title={{ cluster_info.slaves|replace(":",",")|safe }}>{{ cluster_info.cluster }}</td>
                    <td>{{ cluster_info.user }}</td>
                    <td><strong>
                    {% if cluster_info.state == 0 %}
                        <span class="text-primary">PENDING</span>
                    {% elif cluster_info.state == 1 %}
                        <span class="text-danger">FAILED</span>
                    {% else %}
                        <span class="text-success">COMPLETED</span>
                    {% endif %}
                    </strong>
                    </td>
                    <td>
                        {% if cluster_info.state == 0 %}
                            <button class="btn btn-primary unclickable-button" style="opacity: 0.5;pointer-events: none;">Expand</button>
                        {% elif cluster_info.state == 1 %}
                            <button class="btn btn-dark" id={{cluster_info.cluster}} value={{cluster_info.slaves}} name="restart_button">Restart</button>
                        {% else %}
                            <a href="{{ url_for('strat_add.custom_cluster_performance_metrics', cluster_name=cluster_info.cluster, slaves=cluster_info.slaves) }}"
                            type="button"
                            class="btn btn-primary"
                            target="_blank"
                            style="color: #fff;
                                font-weight : 600;
                                font-size:14px"> Expand </a>
                        {% endif %}
                    </td>
                    <td><button class="btn btn-danger" id={{cluster_info.cluster}} value={{cluster_info.slaves}} name="delete_cluster">Delete</button></td>
                    <td>
                        {% if cluster_info.state == 0 or cluster_info.state == 1 %}
                        <button class="btn btn unclickable-button"
                            style="opacity: 0.5; 
                            pointer-events: none;
                            font-weight : 600;
                            background-color: rgb(212, 186, 36);
                            font-size:14px">Download</button>

                        {% else %}
                        <button class="btn btn" id={{cluster_info.cluster}} value={{cluster_info.slaves}}
                            name="download" style="color: #fff;
                               font-weight : 600;
                               background-color: rgb(212, 186, 36);
                               font-size:14px">Download</button>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
        <div class="g-lg5 py-5" style="display: flex;justify-content: center;align-items: center;">
            <button class="btn btn-primary" id="add_cluster_backtest">Add Clusters to backtest</button></td>
        </div>
        <div class="row align-items-center" id="cluster_backtest_form" style="display:none">
            <div class="col-md-10 mx-auto col-lg-8">
                <form method="post"
                      enctype="multipart/form-data"
                      action="/post_cluster_backtest"
                      class="p-4 p-md-5 border rounded-3 bg-light"
                      id="cluster_backtest_form">
                    {{ form.hidden_tag() }}
                    <div style="text-align : center">
                        <p class="fw-bold">
                            Add Slaves for Backtest
                        </p>
                        <br />
                    </div>
                    <div class="form-floating mb-3">
                        
                        <select name="{{ form.cluster.name }}"
                                id="{{ form.cluster.id }}"
                                class="form-control">
                            <option disabled selected value>
                                -- select an option --
                            </option>

                            {% for item in form.cluster.choices %}
                                {% if item != '' %}
                                    {% if form.cluster.data == item %}
                                        <option value={{ item }} selected>{{ item }}
                                        </option>
                                    {% else %}
                                        <option value={{ item }}>{{ item }}
                                        </option>
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                        </select> 
                        
                        
                        <label >
                            {% if form.cluster.flags.required %}
                                {{ form.cluster.label(text=form.cluster.label.text + ' *') }}
                            {% else %}
                                {{ form.cluster.label }}
                            {% endif %}
                        </label>
                        <br />
                        
                    

                        <div style="display:none">
                            {{ form.slaves }}
                        </div>
                    </div>
                    
                    <div class="checkbox mb-3" id="slave_div"></div>
                    <button class="w-100 btn btn-lg btn-primary" id="submit_cluster_backtest" type="submit">
                        <span style="pointer-events: none;">{{ form.submit.label }}</span>
                    </button>
                    <hr class="my-4"/>
                </form>
            </div>
        </div>
    </div>


    <div class="popup" id="cluster_backtest_popup" style="visibility : hidden;">
        <img src="{{ url_for('static', filename='images/failed.png')}}"/>
        <h3>
            Failure
        </h3>
        <p id="reason_failure">
        </p>
        <button type="button" id="cluster_backtest_popup_close">
            OK
        </button>
    </div>
    
    <script src="{{ url_for('static', filename='scripts/cluster_backtest.js') }}"></script>
{% endblock content %}