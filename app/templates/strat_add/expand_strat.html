{% extends "base.html" %}
{% block scripts %}
{{super()}}
{% endblock scripts %}
{% block content %}
    <main>
        <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
        <link rel="stylesheet"
              href="{{ url_for('static', filename='css/strat_expand.css') }}"/>
        {% if (current_user.role.name != "DEVELOPER") %}
            {% set display_add_user = 'flex' %}
        {% else %}
            {% set display_add_user = 'none' %}
        {% endif %}
        <div class="user-tab" style="display:{{ display_add_user }}">
            <img src="{{ url_for('static', filename='images/plus.png')}}"
                    id="add-user"/>
            {% for user in user_list %}
                <div class="user">
                    <p>{{ user }}</p>
                    <button value={{ user }} class="close-icon">
                    </button>
                </div>
            {% endfor %}
        </div>
        <div style="text-align : center;
                    margin-top: 2em;
                    display:flex;
                    justify-content: space-around">
            {% if ((status != "pending") and (status != "test")) or ((current_user.role.name == "DEVELOPER") and (special_access == False)) %}
                {% set display_review_btn = 'none' %}
            {% else %}
                {% set display_review_btn = 'block' %}
            {% endif %}
            {% if (status == "pending") and ((current_user.role.name == "DEVELOPER") and (special_access == False)) %}
                {% set display_perf_kivi = 'none' %}
            {% else %}
                {% set display_perf_kivi = 'block' %}
            {% endif %}
            {% if status != "pending" or ((current_user.role.name == "DEVELOPER") and (special_access == False)) or
            (live_mode_clusters == []) %}
                {% set display_cluster = 'none' %}
            {% else %}
                {% set display_cluster = 'block' %}
            {% endif %}
            {% if status == "pending" %}
                {% set display_to_do = 'none' %}
            {% else %}
                {% set display_to_do = 'block' %}
            {% endif %}
            {% if ('cluster' in strategy.strategy_name) and (status == "live") and ((current_user.role.name == "ADMIN") or
            (current_user.role.name == "MANAGER")) %}
                {% set display_sentinel_kivi = 'block' %}
            {% else %}
                {% set display_sentinel_kivi = 'none' %}
            {% endif %}
            {% if ('cluster' in strategy.strategy_name) and (status == "live") %}
                {% set display_var_report = 'block' %}
            {% else %}
                {% set display_var_report = 'none' %}
            {% endif %}
            {% if status != "pending" or current_user.role.name == "DEVELOPER" %}
                {% set display_delete = 'none' %}
            {% else %}
                {% set display_delete = 'block' %}
            {% endif %}
            <button type="button"
                    class="btn btn-warning btn-sm"
                    id="to_do_button"
                    style="color: black;
                           font-weight : 600;
                           font-size:14px;
                           border:1px solid white;
                           display: {{ display_to_do }}">
                Add To Do
            </button>
            <a href="{{ url_for('strat_add.strategy_review', strategy_name=strategy.strategy_name) }}" target="_blank" type="button"
                class="btn btn-primary btn-sm" style="color: #fff;
                                  font-weight : 600;
                                  font-size:14px;
                                  display:{{ display_review_btn }}" id="review_btn"> Review Strategy </a>
            <div class="dropdown-div">
                {% if available_dates_perf and available_dates_perf|length > 0 %}
                <div class="dropdown" onmouseover="showDropdown(`perf_metrics`)" onmouseout="hideDropdown(`perf_metrics`)">
                    <button type="button" onclick="fetchPerformanceMetric(this)" name="current"
                        class="btn btn-info btn-sm dropdown-toggle" style="color: #fff;
                                       background: #99334d;
                                       font-weight : 600;
                                       font-size:14px;
                                       border:1px solid white;
                                       display: {{ display_perf_kivi }}">
                        Performance Metrics
                    </button>
                    <ul class="dropdown-menu" id="perf_metrics" style="width: 175px">
                        {% for date in available_dates_perf %}
                        <li>
                            <button class="dropdown-item" onclick="fetchPerformanceMetric(this)" name={{ date }}
                                value="{{current_user.username}}">
                                {{ date }}
                            </button>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
                {% else %}
                <button type="button" name="current" value="{{current_user.username}}" onclick="fetchPerformanceMetric(this)"
                    class="btn btn-info btn-sm"
                    style="color: #fff; background: #99334d; font-weight: 600; font-size: 14px; border: 1px solid white; display: {{ display_perf_kivi }}">
                    Performance Metrics
                </button>
                {% endif %}
            </div>
            <div class="dropdown-div" style="display: {{ display_var_report }}">
                {% if available_dates_var and available_dates_var|length > 0 %}
                <div class="dropdown" onmouseover="showDropdown(`var_report`)" onmouseout="hideDropdown(`var_report`)">
                    <button type="button" onclick="fetchVarReport(this)" name="current"
                        class="btn btn-info btn-sm dropdown-toggle" style="color: #fff;
                                       background: black;
                                       font-weight : 600;
                                       font-size:14px;
                                       border:1px solid white;">
                        VaR Report
                    </button>
                    <ul class="dropdown-menu" id="var_report" style="width: 175px">
                        {% for date in available_dates_var %}
                        <li class="cluster-performance">
                            <button class="dropdown-item" onclick="fetchVarReport(this)" name={{ date }}
                                value="{{current_user.username}}">
                                {{ date }}
                            </button>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
                {% else %}
                <button type="button" name="current" value="{{current_user.username}}" onclick="fetchVarReport(this)"
                    class="btn btn-info btn-sm"
                    style="color: #fff; background: black; font-weight: 600; font-size: 14px; border: 1px solid white;">
                    VaR Report
                </button>
                {% endif %}
            </div>
            <div class="dropdown" style="display: {{ display_cluster }};">
                <button type="button" class="btn btn-info btn-sm dropdown-toggle" id="cluster_performance_btn"
                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"
                    style="color:#fff; background: #338099; font-weight:600; font-size:14px; border:1px solid white; margin-left:2em;">
                    Cluster Performance Metrics
                </button>
                <ul class="dropdown-menu" aria-labelledby="cluster_performance_btn" style="width:calc(100% - 30px)">
                    {% for cluster in live_mode_clusters %}
                    <li class="cluster-performance"> <button class="dropdown-item cluster-peformance-metrics-list-item"
                            onclick="fetchClusterPerformance(this)" name={{cluster}}>{{ cluster
                            }}</button></li>
                    {% endfor %}
                </ul>
            </div>
            <button type="button" class="btn btn-info btn-sm" id="kivifolio_report_btn" style="color:#fff;
                            background: #101010;
                            font-weight:600;
                            font-size:14px;
                            border:1px solid white;
                            margin-left:2em;
                            display: {{ display_perf_kivi }}">
                Kivifolio Report
            </button>
            {% if strategy.strategy_name in sentinel_kivifolio_list %}
                {% set sentinel_kivifolio_class = 'btn-danger' %}
                {% set sentinel_kivifolio_txt = 'Remove Kivifolio' %}
            {% else %}
                {% set sentinel_kivifolio_class = 'btn-success' %}
                {% set sentinel_kivifolio_txt = 'Request Kivifolio' %}   
            {% endif %}
            <button type="button"
                class="btn {{sentinel_kivifolio_class}} btn-sm"
                value={{strategy.strategy_name}}
                id="sentinel_kivifolio_btn"
                style="color:#fff;
                    font-weight:600;
                    font-size:14px;
                    border:1px solid white;
                    margin-left:2em;
                    display: {{ display_sentinel_kivi }}">
                {{sentinel_kivifolio_txt}}
            </button>
            <form action="{{ url_for('strat_add.delete_strategy', strategy_name=strategy.strategy_name, calling_location='expand') }}" style="display: {{display_delete}}" method="post">
                <input class="btn btn-danger btn-sm"
                        type="submit"
                        style="font-weight:600"
                        value="Delete"
                        onclick="return confirm('Are you sure you want to delete this strategy?');"/>
            </form>
        </div>
        <div class="container">
            <div class="sub-container">
                <div class="box">
                    <h1 class="box-name">Details</h1>
                    <table class="styled-table">
                        <tr>
                            <th>Strategy Name</th>
                            {% if strategy.reworked_strategy != None %}<th>Reworked Strategy</th>{% endif %}
                            <th>Developer Name</th>
                            <th>Segment Name</th>
                            <th>Exchange Name</th>
                            {% if strategy.custom_slippage != None %}<th>Custom Slippage</th>{% endif %}
                            <th>Long/Short</th>
                            <th>Long Book Mapping</th>
                            <th>Short Book Mapping</th>
                            <th>Limit Price Coefficient</th>
                            <th>Trigger Price Coefficient</th>
                            <th>Expiration Time</th>
                            {% if status == "pending" %}
                                <th>Backtest Result</th>
                            {% else %}
                                <th>Live Start Day</th>
                                {% if status == "dead" %}<th>Last Run Day</th>{% endif %}
                            {% endif %}
                            {% if status == "pending" %}
                                <th id="live-cluster-mapping-header">Cluster Mapping</th>
                            {% else %}
                                <th id="live-cluster-mapping-header">Cluster Mapping (Live mode)</th>
                                <th id="test-cluster-mapping-header">Cluster Mapping (Test mode)</th>
                            {% endif %}
                            
                        </tr>
                        <tr>
                            <td>{{ strategy.strategy_name }}</td>
                            {% if strategy.reworked_strategy != None %}<td id="reworked_strategy" class="clickable_cell">{{ strategy.reworked_strategy }}</td>{% endif %}
                            <td>{{ strategy.developer }}</td>
                            <td>{{ strategy.segment }}</td>
                            <td>
                                {{ strategy.exchange_name }}
                            </td>
                            {% if strategy.custom_slippage != None %}<td>{{ strategy.custom_slippage }}</td>{% endif %}
                            <td>
                                {{ strategy.long_short }}
                            </td>
                            <td>
                                {{ strategy.book_long }}
                            </td>
                            <td>
                                {{ strategy.book_short }}
                            </td>
                            <td>
                                {{ strategy.limit_coeff }}
                            </td>
                            <td>
                                {{ strategy.trigger_coeff }}
                            </td>
                            <td>
                                {{ strategy.expiration_time }}
                            </td>
                            {% if status == "pending" %}
                                <td>
                                    {{ backtest_result }}
                                </td>
                            {% else %}
                                <td>
                                    {{ strategy.live_start_day }}
                                </td>
                                {% if status == "dead" %}
                                    <td>
                                        {{ strategy.last_run_day }}
                                    </td>
                                {% endif %}
                            {% endif %}
                            <td id="live-cluster-mapping-data">
                                {%if live_mode_clusters == []%}
                                    None
                                {%else%}
                                    {% for cluster in live_mode_clusters%}
                                        {{ cluster }}
                                        <br>
                                    {% endfor %}
                                {%endif%}
                            </td>                
                            {% if status != "pending" %}
                                <td id="test-cluster-mapping-data">
                                    {%if test_mode_clusters == []%}
                                        None
                                    {%else%}
                                        {% for cluster in test_mode_clusters%}
                                            {{ cluster }}
                                            <br>
                                        {% endfor %}
                                    {%endif%}
                                </td>
                            {% endif %}
                        </tr>
                    </table>
                </div>
            </div>
            {% if (meta_data != None) and ((current_user.role.name != "DEVELOPER") or (status == "live") or (special_access == True)) %}
                <div class="sub-container">
                    <div class="box">
                        <h1 class="box-name">
                            Meta Values
                        </h1>
                        <table class="styled-table">
                            <tr>
                                {% if meta_data.last_sentinel_run != None %}
                                    <th>
                                        Backtest update date
                                    </th>
                                {% endif %}
                                {% if meta_data.max_dd_submit!= None %}
                                    <th>
                                        Max Drawdown (inL)
                                    </th>
                                    <th>
                                        Kivi max DD (1.25X) (inL)
                                    </th>
                                {% endif %}
                                {% if meta_data.max_dd_monte != None %}
                                    <th>
                                        95<sup>th</sup> Percentile DD (inL)
                                    </th>
                                {% endif %}
                                {% if meta_data.percentile_dd != None %}
                                    <th>
                                        Percentile value of kivi max DD
                                    </th>
                                {% endif %}
                                {% if meta_data.monthly_sharpe_submit != None %}
                                    <th>
                                        Monthly Sharpe
                                    </th>
                                {% endif %}
                                {% if meta_data.monthly_ret_submit != None %}
                                    <th>
                                        Avg Monthly Returns
                                    </th>
                                {% endif %}
                                {% if meta_data.ret_dd_submit != None %}
                                    <th>
                                        Return Drawdown
                                    </th>
                                {% endif %}
                                {% if meta_data.inout_sample_bhatt_dist != None %}
                                    <th>
                                        In-Out sample Bhatt Dist.
                                    </th>
                                {% endif %}
                                {% if meta_data.monthly_sharpe_post != None %}
                                    <th>
                                        Monthly Sharpe in live
                                    </th>
                                {% endif %}
                                {% if meta_data.monthly_ret_post != None %}
                                    <th>
                                        Avg Monthly Returns in live
                                    </th>
                                {% endif %}
                                {% if meta_data.curr_backtest_dd != None %}
                                    <th>
                                        Current Backtested DD (inL)
                                    </th>
                                {% endif %}
                            </tr>
                            <tr>
                                {% if meta_data.last_sentinel_run != None %}
                                    <td>
                                        {{ meta_data.last_sentinel_run }}
                                    </td>
                                {% endif %}
                                {% if meta_data.max_dd_submit != None %}
                                    <td>
                                        {{ (meta_data.max_dd_submit / 1e5)|round(2) }}
                                    </td>
                                    <td>
                                        {{ ((1.25 * meta_data.max_dd_submit) / 1e5)|round(2) }}
                                    </td>
                                {% endif %}
                                {% if meta_data.max_dd_monte != None %}
                                    <td>
                                        {{ ((meta_data.max_dd_monte) / 1e5)|round(2) }}
                                    </td>
                                {% endif %}
                                {% if meta_data.percentile_dd != None %}
                                    <td>
                                        {{ meta_data.percentile_dd|round(2) }}
                                    </td>
                                {% endif %}
                                {% if meta_data.monthly_sharpe_submit != None %}
                                    <td>
                                        {{ meta_data.monthly_sharpe_submit|round(2) }}
                                    </td>
                                {% endif %}
                                {% if meta_data.monthly_ret_submit != None %}
                                    <td>
                                        {{ meta_data.monthly_ret_submit|round(2) }}
                                    </td>
                                {% endif %}
                                {% if meta_data.ret_dd_submit != None %}
                                    <td>
                                        {{ meta_data.ret_dd_submit|round(2) }}
                                    </td>
                                {% endif %}
                                {% if meta_data.inout_sample_bhatt_dist != None %}
                                    <td>
                                        {{ meta_data.inout_sample_bhatt_dist|round(4) }}
                                    </td>
                                {% endif %}
                                {% if meta_data.monthly_sharpe_post != None %}
                                    <td>
                                        {{ meta_data.monthly_sharpe_post|round(2) }}
                                    </td>
                                {% endif %}
                                {% if meta_data.monthly_ret_post != None %}
                                    <td>
                                        {{ meta_data.monthly_ret_post|round(2) }}
                                    </td>
                                {% endif %}
                                {% if meta_data.curr_backtest_dd != None %}
                                    <td>
                                        {{ (meta_data.curr_backtest_dd / 1e5)|round(2) }}
                                    </td>
                                {% endif %}
                            </tr>
                        </table>
                    </div>
                </div>
            {% endif %}
            {% if strategy.comments != "" and strategy.comments != None %}
                <div class="sub-container">
                    <div class="box">
                        <h1 class="box-name">
                            Comments
                        </h1>
                        <p>
                            {{ strategy.comments }}
                        </p>
                    </div>
                </div>
            {% endif %}
            {% if current_user.role.name != "DEVELOPER" or status == "live" or special_access == True %}
                {% for key, value in retrieval_url.items() %}
                    <div class="sub-container">
                        <div class="box">
                            <h1 class="box-name">
                                {{ title[key] }}
                            </h1>
                            <img src="{{ value }}"/>
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
        </div>
        <div class="popup" id="perf_fail_popup" style="visibility : hidden;">
            <img src="{{ url_for('static', filename='images/failed.png')}}"/>
            <h3>
                Failed
            </h3>
            <p id="metric_fail_reason">
            </p>
            <button type="button" id="perf_fail_close">
                OK
            </button>
        </div>
        <div class="popup" id="success_popup" style="visibility : hidden;">
            <img src="{{ url_for('static', filename='images/tick.png')}}"/>
            <h3>
                Success
            </h3>
            <p>
                To-do list updated
            </p>
            <button type="button" id="success_popup_close" style="background-color: #6fd649">
                OK
            </button>
        </div>
        <div class="popup" id="to_do_popup" style="visibility : hidden; width: 50%;">
            <img src="{{ url_for('static', filename='images/check-list.png')}}"/>
            <h3>
                To-Do List
            </h3>
            <textarea id="todo_list" 
                    style="height: 136px; 
                    overflow-y: hidden; width:100%;">{{ review_data.to_do }}</textarea>
            <div style="display:flex; margin-top: 0em;">
                <button type="button"
                        id="to_do_save"
                        style="width:50%;
                               margin-right:0.5em;
                               background-color: #6fd649">
                    Save
                </button>
                <button type="button" id="to_do_close" style="width:50%;">
                    Cancel
                </button>
            </div>
        </div>
        <div class="popup" id="add-user-popup" style="visibility : hidden;">
            <img src="{{ url_for('static', filename='images/add_user.png')}}"/>
            <h3>
                Add new users
            </h3>
            <div style=" margin-top:30px;" id="manager_div">
                <label for "new_user" style="margin-bottom:0.5em;"/>
                Add user
                <select name="new_user"
                        id="new_user"
                        style="margin-bottom:2em;
                               text-align:center">
                    {% for user in users %}
                        <option value={{ user }}>{{ user }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div style="display:flex; margin-top: -2em;">
                <button type="button"
                        id="add-user-submit"
                        style="width:50%;
                               margin-right:0.5em;
                               background-color: #6fd649">
                    Add
                </button>
                <button type="button" id="add-user-close" style="width:50%;">
                    Cancel
                </button>
            </div>
        </div>
        {% if current_user.role.name != "DEVELOPER" or status == "live" or special_access == True %}
            <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
            <div id="tagid">
            </div>
        {% else %}
            <div id="tagid" style="display:none;">
            </div>
        {% endif %}
        {% if status == "live" and slip_graphs!=None %}
            <div class="slippage_charts" id="slip_chart"></div>
            <div class="slippage_charts" id="exp_chart"></div>
        {% endif %}
    </main>
    <div id="profiler" style="visibility : hidden; display:none;" value={{ strategy.strategy_name }}>
    </div>
    <div id="cluster_mapping" style="visibility : hidden; display:none;" value={{ live_mode_clusters }}>
    </div>
    <script src='https://cdn.plot.ly/plotly-latest.min.js'></script>
    <script src="{{ url_for('static', filename='scripts/expand_strats.js') }}"></script>
    <script type='text/javascript'>
        var graphs = {{ slip_graphs[0] | safe}};
        Plotly.plot('slip_chart', graphs, {});
        var graphs = {{ slip_graphs[1] | safe}};
        Plotly.plot('exp_chart', graphs, {});
    </script>
{% endblock content %}
