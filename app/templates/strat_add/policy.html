{% extends "base.html" %}
{% block content %}
<main>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/policy.css') }}" />
    <script src="https://cdn.jsdelivr.net/npm/markdown-it/dist/markdown-it.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <div class="container-fluid px-4 py-3 my-3 text-center" style="padding-top: 0;">
        <h1 class="display-5 fw-bold" style="margin-bottom: 1rem;">Strategy Policies</h1>
        {% if current_user.role.name == "ADMIN" %}
        <button class="btn custom-btn" id="addPolicyBtn" onclick="addPolicy()">Add Policy</button>
        {% endif %}
    </div>

    <div class="container px-4">
        <div id="card-container" class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-3 justify-content-center">
            {% for key, value in cards.items() %}
            <div class="col mb-4">
                <div class="custom-card card square-card custom-border shadow">
                    <div class="card-body d-flex flex-column justify-content-center align-items-center">
                        <h2 class="custom-title card-title h2 text-center" onclick="viewMore(event)">{{ value[0] | safe
                            }}</h2>
                        <div class="full-content d-none">{{ value[1] | safe }}</div>
                        {% if current_user.role.name == "ADMIN" %}
                        <div class="button-group">
                            <button class="btn custom-btn" id="{{ key }}" onclick="editPolicy(this)">Edit
                                Policy</button>
                            <button class="btn custom-btn " id="{{ key }}" onclick="deletePolicy(this)">Delete
                                Policy</button>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    <div class="modal" id="editorModal" tabindex="-1" aria-labelledby="editorModalLabel" aria-hidden="true"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editorModalLabel">Edit Policy</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="policyHeading" class="form-label">Policy Name:</label>
                        <input type="text" class="form-control" id="policyHeading">
                    </div>
                    <textarea id="editor"></textarea>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="saveChanges()">Save changes</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="previewModalLabel">Policy Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <textarea id="previewMarkdown" style="display: none;"></textarea>
                    <!-- Hidden textarea for SimpleMDE -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    <div class="popup" id="policy_popup" style="visibility : hidden;">
        <img src="{{ url_for('static', filename='images/failed.png')}}" />
        <h3>
            Failure
        </h3>
        <p id="reason_failure">
        </p>
        <button type="button" id="policy_popup_close">
            OK
        </button>
    </div>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/simplemde/latest/simplemde.min.css">
    <script src="https://cdn.jsdelivr.net/simplemde/latest/simplemde.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='scripts/policy.js') }}"></script>
</main>
{% endblock %}