{% extends "base.html" %}
{% block content %}
    <main>
        <link rel="stylesheet"
              href="{{ url_for('static', filename='css/strat-add-form.css') }}"/>
        <div class="container col-xl-10 col-xxl-8 px-4 py-5">
            <div class="row align-items-center g-lg-5 py-5">
                <div class="col-md-10 mx-auto col-lg-8">
                    <form method="post"
                          enctype="multipart/form-data"
                          action=""
                          class="p-4 p-md-5 border rounded-3 bg-light"
                          id="strat_review_form">
                        {{ form.hidden_tag() }}
                        <div style="text-align : center">
                            <p class="fw-bold">
                                {% if msg %}
                                    {{ msg | safe }}
                                {% else %}
                                    {{ legend }}
                                {% endif %}
                            </p>
                            <br/>
                        </div>
                        <div class="form-floating mb-3">
                            {% if form.timecheck.errors %}
                                {{ form.timecheck(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.timecheck.errors %}<span>{{ error }}</span>{% endfor %}
                                </div>
                            {% else %}
                                {{ form.timecheck(class="form-control") }}
                            {% endif %}
                            <label for="floatingInput">
                                {% if form.timecheck.flags.required %}
                                    {{ form.timecheck.label(text=form.timecheck.label.text + ' *') }}
                                {% else %}
                                    {{ form.timecheck.label }}
                                {% endif %}
                            </label>
                        </div>
                        <div class="form-floating mb-3">
                            {% if form.correlation_check.errors %}
                                {{ form.correlation_check(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.correlation_check.errors %}<span>{{ error }}</span>{% endfor %}
                                </div>
                            {% else %}
                                {{ form.correlation_check(class="form-control") }}
                            {% endif %}
                            <label for="floatingInput">
                                {% if form.correlation_check.flags.required %}
                                    {{ form.correlation_check.label(text=form.correlation_check.label.text + ' *') }}
                                {% else %}
                                    {{ form.correlation_check.label }}
                                {% endif %}
                            </label>
                        </div>
                        <div class="form-floating mb-3">
                            {% if form.trade_distribution_check.errors %}
                                {{ form.trade_distribution_check(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.trade_distribution_check.errors %}<span>{{ error }}</span>{% endfor %}
                                </div>
                            {% else %}
                                {{ form.trade_distribution_check(class="form-control") }}
                            {% endif %}
                            <label for="floatingInput">
                                {% if form.trade_distribution_check.flags.required %}
                                    {{ form.trade_distribution_check.label(text=form.trade_distribution_check.label.text + ' *') }}
                                {% else %}
                                    {{ form.trade_distribution_check.label }}
                                {% endif %}
                            </label>
                        </div>
                        <div class="form-floating mb-3">
                            {% if form.risk_analysis.errors %}
                                {{ form.risk_analysis(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.risk_analysis.errors %}<span>{{ error }}</span>{% endfor %}
                                </div>
                            {% else %}
                                {{ form.risk_analysis(class="form-control") }}
                            {% endif %}
                            <label for="floatingInput">
                                {% if form.risk_analysis.flags.required %}
                                    {{ form.risk_analysis.label(text=form.risk_analysis.label.text + ' *') }}
                                {% else %}
                                    {{ form.risk_analysis.label }}
                                {% endif %}
                            </label>
                        </div>
                        <div class="form-floating mb-3">
                            {% if form.num_days_trading.errors %}
                                {{ form.num_days_trading(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.num_days_trading.errors %}<span>{{ error }}</span>{% endfor %}
                                </div>
                            {% else %}
                                {{ form.num_days_trading(class="form-control") }}
                            {% endif %}
                            <label for="floatingInput">
                                {% if form.num_days_trading.flags.required %}
                                    {{ form.num_days_trading.label(text=form.num_days_trading.label.text + ' *') }}
                                {% else %}
                                    {{ form.num_days_trading.label }}
                                {% endif %}
                            </label>
                        </div>
                        <div class="form-floating mb-3">
                            {% if form.comments.errors %}
                                {{ form.comments(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.comments.errors %}<span>{{ error }}</span>{% endfor %}
                                </div>
                            {% else %}
                                {{ form.comments(class="form-control") }}
                            {% endif %}
                            <label for="floatingInput">
                                {% if form.comments.flags.required %}
                                    {{ form.comments.label(text=form.comments.label.text + ' *') }}
                                {% else %}
                                    {{ form.comments.label }}
                                {% endif %}
                            </label>
                        </div>
                        <div class="form-floating mb-3">
                            {% if form.to_change.errors %}
                                {{ form.to_change(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.to_change.errors %}<span>{{ error }}</span>{% endfor %}
                                </div>
                            {% else %}
                                {{ form.to_change(class="form-control") }}
                            {% endif %}
                            <label for="floatingInput">
                                {% if form.to_change.flags.required %}
                                    {{ form.to_change.label(text=form.to_change.label.text + ' *') }}
                                {% else %}
                                    {{ form.to_change.label }}
                                {% endif %}
                            </label>
                        </div>
                        <div style="display:flex; justify-content:space-around; margin-top:2em;">
                            {% if current_user.role.name == "DEVELOPER" %}
                                {% set display_accept_reject = "none" %}
                            {% else %}
                                {% set display_accept_reject = "block" %}
                            {% endif %}
                            <button class="btn btn-primary btn-sm"
                                    type="button"
                                    value="start_date"
                                    style="font-size:16px;
                                            font-weight:600;
                                            display: {{ display_accept_reject }}"
                                    id="strat_date_btn">
                                    <span style="pointer-events: none;">Accept to Live</span>
                            </button>
                            {% if status == "test" or current_user.role.name == "DEVELOPER"%}
                                {% set display_accept_test = "none" %}
                            {% else %}
                                {% set display_accept_test = "block" %}
                            {% endif %}
                            <button class="btn btn-primary btn-sm"
                                    type="button"
                                    value="test_confirmation"
                                    style="font-size:16px;
                                            font-weight:600;
                                            display: {{ display_accept_test }}"
                                    id="test_confirmation_btn">
                                    <span style="pointer-events: none;">Accept to Test</span>
                            </button>
                            <button class="btn btn-warning btn-sm"
                                    name="action"
                                    value="save"
                                    type="submit"
                                    style="font-size:16px;
                                           font-weight:600"
                                    id="save_btn">
                                <span style="pointer-events: none;">{{ form.save.label }}</span>
                            </button>
                            <button class="btn btn-danger btn-sm"
                                    name="action"
                                    value="reject"
                                    type="submit"
                                    style="font-size:16px;
                                           font-weight:600;
                                           display: {{ display_accept_reject }}"
                                    id="reject_btn">
                                <span style="pointer-events: none;">{{ form.strategy_reject.label }}</span>
                            </button>
                        </div>
                        <div class="popup p-4 p-md-5"
                            id="strat_date_popup"
                            style="visibility : hidden;
                                    text-align: unset;">
                            <h4>Enter Live Start Date</h4>
                            <div class="form-floating mb-3">
                                {{ form.strat_date(class="form-control", style="margin-left: 0em; margin-top:1em", value=rework_start) }}
                                <label for="floatingInput">
                                    {% if form.strat_date.flags.required %}
                                        {{ form.strat_date.label(text=form.strat_date.label.text + ' *') }}
                                    {% else %}
                                        {{ form.strat_date.label }}
                                    {% endif %}
                                </label>
                            </div>
                            {% if rework_start == "" %}
                                {% set display_rework = "none" %}
                            {% else %}
                                {% set display_rework = "block" %}
                            {% endif %}
                            <div class="checkbox mb-3"
                                 id="rework_start"
                                 style="display:{{display_rework}}">
                                {% if form.is_reworked_start.errors %}
                                    {{ form.is_reworked_start(class="form-check-input is-invalid", type="checkbox") }}
                                    <div class="invalid-feedback">
                                        {% for error in errors %}<span>{{ error }}</span>{% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.is_reworked_start(class="form-check-input", type="checkbox", style="margin-left: 0em") }}
                                {% endif %}
                                {{ form.is_reworked_start.label(class="form-check-label", style="display: inline") }}
                            </div>
                            <div style="display:flex; justify-content:space-around; margin-top:2em;">
                                <button class="btn btn-success btn-sm"
                                    name="action"
                                    value="accept_live"
                                    type="submit"
                                    style="font-size:16px;
                                            font-weight:600;
                                            padding: 0.5rem 1rem;
                                            background-color: green;
                                            width: unset;
                                            display:{{ display_accept_reject }};"
                                    id="accept_btn">
                                <span style="pointer-events: none;">Submit Strategy</span>
                                </button>
                                <button class="btn btn-danger btn-sm"
                                        type="button"
                                        value="start_date"
                                        style="font-size:16px;
                                                font-weight:600;
                                                padding: 0.5rem 1rem;
                                                width: unset;"
                                        id="cancel_popup_btn">
                                        <span style="pointer-events: none;">Cancel</span>
                                </button>
                            </div>
                        </div>
                        <div class="popup p-4 p-md-5"
                            id="test_confirmation_popup"
                            style="visibility : hidden;
                                    text-align: unset;">
                            <h4>Submit to Test-Only Mode?</h4>
                            <div style="display:flex; justify-content:space-around; margin-top:2em;">
                                <button class="btn btn-success btn-sm"
                                    name="action"
                                    value="accept_test"
                                    type="submit"
                                    style="font-size:16px;
                                            font-weight:600;
                                            padding: 0.5rem 1rem;
                                            background-color: green;
                                            width: unset;
                                            display:{{ display_accept_reject }};"
                                    id="accept_test_btn">
                                <span style="pointer-events: none;">Submit Strategy</span>
                                </button>
                                <button class="btn btn-danger btn-sm"
                                        type="button"
                                        value="test_confirmation"
                                        style="font-size:16px;
                                                font-weight:600;
                                                padding: 0.5rem 1rem;
                                                width: unset;"
                                        id="cancel_test_popup_btn">
                                        <span style="pointer-events: none;">Cancel</span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="popup"
             id="accepted_popup"
             style="visibility : {{ vis_success }}">
            <img src="{{ url_for('static', filename='images/tick.png')}}"/>
            <h4>Success</h4>
            {% if action == "accept_live" or action == "accept_test" %}
                {% if action == "accept_live" %}
                    <p style="fnt-size:14px; font-weight:500;">{{ strategy_name }} accepted to live</p>
                    <p style="fnt-size: 10px; font-weight:300;">To modify Test/Live cluster mapping, visit <b>Manage Cluster mapping</b> tab</p>
                {% else %}
                    <p style="fnt-size:14px; font-weight:500;">{{ strategy_name }} accepted to test</p>
                    <p style="fnt-size: 10px; font-weight:300;">To modify Test cluster mapping, visit <b>Manage Cluster mapping</b> tab</p>
                {% endif %}
                {% if strats_info["reworked_strategy"] != None %}
                    <p style="fnt-size:14px; font-weight:500;">Reworked Strategy: {{ strats_info["reworked_strategy"] }}</p>
                {% endif %}
                {% if strats_info["cluster_list"] != None %}
                    <div style="text-align:left">
                        <ul>
                            {% for cluster in strats_info["cluster_list"] %}<li>{{ cluster }}</li>{% endfor %}
                        </ul>
                    </div>
                {% endif %}
            {% elif action == "reject" %}
                <p style="fnt-size:14px; font-weight:500;">{{ strategy_name }} rejected</p>
            {% else %}
                <p style="fnt-size:14px; font-weight:500;">{{ strategy_name }} review data saved</p>
            {% endif %}
            <button type="button" id="accept_close" style="background:#6fd649;">OK</button>
        </div>
        <div class="popup" id="rejected_popup" style="visibility : {{ vis_fail }}">
            <img src="{{ url_for('static', filename='images/failed.png')}}"/>
            <h4>Failed</h4>
            {% if action == "accept_live" or action == "accept_test" %}
                <p style="fnt-size:14px; font-weight:500;">{{ strategy_name }} could not be accepted</p>
            {% else %}
                <p style="fnt-size:14px; font-weight:500;">{{ strategy_name }} could not be rejected</p>
            {% endif %}
            <button type="button" id="reject_close">OK</button>
        </div>
    </main>
    <script src="{{ url_for('static', filename='scripts/strategy_review.js') }}"></script>
{% endblock content %}
