{% extends "/base.html" %} {% block content %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/strat-add-form.css') }}" />
<div class="container col-xl-10 col-xxl-8 px-4 py-5">
	<div class="row align-items-center g-lg-5 py-5">
		<div class="col-md-10 mx-auto col-lg-8">
			<form method="post" enctype="multipart/form-data" action="" class="p-4 p-md-5 border rounded-3 bg-light"
				id="strat_add_form">
				{{ form.hidden_tag() }}
				<div style="text-align: center">
					<p class="fw-bold">
						{% if msg %} {{ msg | safe }} {% else %} {{ legend }} {% endif %}
					</p>
					<br />
				</div>
				{% if type == "new" %}
				<div class="form-floating mb-3">
					{% if form.strategy_name.errors %} {{
					form.strategy_name(class="form-control is-invalid") }}
					<div class="invalid-feedback">
						{% for error in form.strategy_name.errors %}<span>{{ error }}</span>{% endfor %}
					</div>
					{% else %} {{ form.strategy_name(class="form-control") }} {% endif %}
					<label for="floatingInput">
						{% if form.strategy_name.flags.required %} {{
						form.strategy_name.label(text=form.strategy_name.label.text + ' *')
						}} {% else %} {{ form.strategy_name.label }} {% endif %}
					</label>
				</div>
				{% else %}
				<div class="form-floating mb-3">
					{% if form.strategy_name.errors %} {{
					form.strategy_name(class="form-control is-invalid") }}
					<div class="invalid-feedback">
						{% for error in form.strategy_name.errors %}<span>{{ error }}</span>{% endfor %}
					</div>
					{% else %} {{ form.strategy_name(class="form-control", readonly=true)
					}} {% endif %}
					<label for="floatingInput">
						{% if form.strategy_name.flags.required %} {{
						form.strategy_name.label(text=form.strategy_name.label.text + ' *')
						}} {% else %} {{ form.strategy_name.label }} {% endif %}
					</label>
				</div>
				{% endif %}
				<div class="checkbox mb-3">
					{% if form.is_rework.errors %} {{
					form.is_rework(class="form-check-input is-invalid", type="checkbox")
					}}
					<div class="invalid-feedback">
						{% for error in errors %}<span>{{ error }}</span>{% endfor %}
					</div>
					{% else %} {{ form.is_rework(class="form-check-input",
					type="checkbox") }} {% endif %} {{
					form.is_rework.label(class="form-check-label") }}
				</div>
				<div class="form-floating mb-3" id="old_strategy">
					{% if form.old_strategy.errors %} {{
					form.old_strategy(class="form-control is-invalid") }}
					<div class="invalid-feedback">
						{% for error in form.old_strategy.errors %}<span>{{ error }}</span>{% endfor %}
					</div>
					{% else %} {{ form.old_strategy(class="form-control") }} {% endif %}
					<label for="floatingInput">{{ form.old_strategy.label }}</label>
				</div>
				<div class="form-floating mb-3">
					{% if form.segment.errors %}
					<select name="{{ form.segment.name }}" id="{{ form.segment.id }}" class="form-control is-invalid">
						<option disabled selected value>-- select an option --</option>
						{% for item in form.segment.choices %}
						<option value="{{item}}">{{ item }}</option>
						{% endfor %}
					</select>
					<div class="invalid-feedback">
						{% for error in form.segment.errors %}<span>{{ error }}</span>{%
						endfor %}
					</div>
					{% else %}
					<select name="{{ form.segment.name }}" id="{{ form.segment.id }}" class="form-control">
						<option disabled selected value>-- select an option --</option>
						{% for item in form.segment.choices %}
						<option value="{{item}}">{{ item }}</option>
						{% endfor %}
					</select>
					{% endif %}
					<label for="floatingInput">
						{% if form.segment.flags.required %} {{
						form.segment.label(text=form.segment.label.text + ' *') }} {% else
						%} {{ form.segment.label }} {% endif %}
					</label>
				</div>
				<div class="form-floating mb-3">
					{% if form.exchange_name.errors %}
					<select name="{{ form.exchange_name.name }}" id="{{ form.exchange_name.id }}"
						class="form-control is-invalid">
						<option disabled selected value>-- select an option --</option>
						{% for item in form.exchange_name.choices %}
						<option value="{{item}}">{{ item }}</option>
						{% endfor %}
					</select>
					<div class="invalid-feedback">
						{% for error in form.exchange_name.errors %}<span>{{ error }}</span>{% endfor %}
					</div>
					{% else %}
					<select name="{{ form.exchange_name.name }}" id="{{ form.exchange_name.id }}" class="form-control">
						<option disabled selected value>-- select an option --</option>
						{% for item in form.exchange_name.choices %} {% if
						form.exchange_name.data == item %}
						<option value="{{item}}" selected>{{ item }}</option>
						{% else %}
						<option value="{{item}}">{{ item }}</option>
						{% endif %} {% endfor %}
					</select>
					{% endif %}
					<label for="floatingInput">
						{% if form.exchange_name.flags.required %} {{
						form.exchange_name.label(text=form.exchange_name.label.text + ' *')
						}} {% else %} {{ form.exchange_name.label }} {% endif %}
					</label>
				</div>
				<div class="row mb-3">
					<div class="form-floating col-md-4">
						{{ form.backtest_date(class="form-control") }}
						<label for="floatingInput">
							{% if form.backtest_date.flags.required %} {{
							form.backtest_date.label(text=form.backtest_date.label.text + ' *')
							}} {% else %} {{ form.backtest_date.label }} {% endif %}
						</label>
					</div>
					<div class="form-floating col-md-4">
						{% if form.overlap_days.errors %} {{
							form.overlap_days(class="form-control is-invalid") }}
							<div class="invalid-feedback">
								{% for error in form.overlap_days.errors %}<span>{{ error }}</span>{% endfor %}
							</div>
							{% else %} {{ form.overlap_days(class="form-control") }} {% endif
							%}
							<label class="inline-field" style="font-size: 0.8rem;">{{ form.overlap_days.label }}</label>
					</div>
					<div class="form-floating col-md-4">
						{% if form.custom_slippage.errors %} {{
						form.custom_slippage(class="form-control is-invalid") }}
						<div class="invalid-feedback">
							{% for error in form.custom_slippage.errors %}<span>{{ error }}</span>{% endfor %}
						</div>
						{% else %} {{ form.custom_slippage(class="form-control") }} {% endif
						%}
						<label for="floatingInput" class="inline-field">{{ form.custom_slippage.label }}</label>
					</div>
				</div>
				<h6>For a Cash strategy add stoploss limit price and trigger price</h6>
				For example for a 3% stoploss limit price, just write 3
				<div class="row mb-3">
					<div class="form-floating col-md-4">
						{% if form.trigger_coeff.errors %} {{
						form.trigger_coeff(class="form-control is-invalid") }}
						<div class="invalid-feedback">
							{% for error in form.trigger_coeff.errors %}<span>{{ error }}</span>{% endfor %}
						</div>
						{% else %} {{ form.trigger_coeff(class="form-control") }} {% endif
						%}
						<label for="floatingInput" class="inline-field">{{ form.trigger_coeff.label }}</label>
					</div>
					<div class="form-floating col-md-4">
						{% if form.limit_coeff.errors %} {{
						form.limit_coeff(class="form-control is-invalid") }}
						<div class="invalid-feedback">
							{% for error in form.limit_coeff.errors %}<span>{{ error }}</span>{% endfor %}
						</div>
						{% else %} {{ form.limit_coeff(class="form-control") }} {% endif %}
						<label for="floatingInput" class="inline-field">{{ form.limit_coeff.label }}</label>
					</div>
					<div class="form-floating col-md-4">
						{{ form.expiration_time(class="form-control") }}
						<label for="floatingInput" class="inline-field">{{ form.expiration_time.label }}</label>
					</div>
				</div>
				<div class="form-floating mb-3">
					{% if form.long_short.errors %}
					<select name="{{ form.long_short.name }}" id="{{ form.long_short.id }}"
						class="form-control is-invalid">
						<option disabled selected value>-- select an option --</option>
						{% for item in form.long_short.choices %}
						<option value="{{item}}">{{ item }}</option>
						{% endfor %}
					</select>
					<div class="invalid-feedback">
						{% for error in form.long_short.errors %}<span>{{ error }}</span>{%
						endfor %}
					</div>
					{% else %}
					<select name="{{ form.long_short.name }}" id="{{ form.long_short.id }}" class="form-control">
						<option disabled selected value>-- select an option --</option>
						{% for item in form.long_short.choices %} {% if form.long_short.data
						== item %}
						<option value="{{item}}" selected>{{ item }}</option>
						{% else %}
						<option value="{{item}}">{{ item }}</option>
						{% endif %} {% endfor %}
					</select>
					{% endif %}
					<label for="floatingInput">
						{% if form.long_short.flags.required %} {{
						form.long_short.label(text=form.long_short.label.text + ' *') }} {%
						else %} {{ form.long_short.label }} {% endif %}
					</label>
				</div>
				<div class="form-floating mb-3">
					{% if form.long_book_mapping.errors %}
					<select name="{{ form.long_book_mapping.name }}" id="{{ form.long_book_mapping.id }}"
						class="form-control is-invalid">
						<option disabled selected value>-- select an option --</option>
						{% for item in form.long_book_mapping.choices %}
						<option value="{{item}}">{{ item }}</option>
						{% endfor %}
					</select>
					<div class="invalid-feedback">
						{% for error in form.long_book_mapping.errors %}<span>{{ error }}</span>{% endfor %}
					</div>
					{% else %}
					<select name="{{ form.long_book_mapping.name }}" id="{{ form.long_book_mapping.id }}"
						class="form-control">
						<option disabled selected value>-- select an option --</option>
						{% for item in form.long_book_mapping.choices %} {% if
						form.long_book_mapping.data == item %}
						<option value="{{item}}" selected>{{ item }}</option>
						{% else %}
						<option value="{{item}}">{{ item }}</option>
						{% endif %} {% endfor %}
					</select>
					{% endif %}
					<label for="floatingInput">
						{% if form.long_book_mapping.flags.required %} {{
						form.long_book_mapping.label(text=form.long_book_mapping.label.text
						+ ' *') }} {% else %} {{ form.long_book_mapping.label }} {% endif %}
					</label>
				</div>
				<div class="form-floating mb-3">
					{% if form.short_book_mapping.errors %}
					<select name="{{ form.short_book_mapping.name }}" id="{{ form.short_book_mapping.id }}"
						class="form-control is-invalid">
						<option disabled selected value>-- select an option --</option>
						{% for item in form.short_book_mapping.choices %}
						<option value="{{item}}">{{ item }}</option>
						{% endfor %}
					</select>
					<div class="invalid-feedback">
						{% for error in form.short_book_mapping.errors %}<span>{{ error }}</span>{% endfor %}
					</div>
					{% else %}
					<select name="{{ form.short_book_mapping.name }}" id="{{ form.short_book_mapping.id }}"
						class="form-control">
						<option disabled selected value>-- select an option --</option>
						{% for item in form.short_book_mapping.choices %} {% if
						form.short_book_mapping.data == item %}
						<option value="{{item}}" selected>{{ item }}</option>
						{% else %}
						<option value="{{item}}">{{ item }}</option>
						{% endif %} {% endfor %}
					</select>
					{% endif %}
					<label for="floatingInput">
						{% if form.short_book_mapping.flags.required %} {{
						form.short_book_mapping.label(text=form.short_book_mapping.label.text
						+ ' *') }} {% else %} {{ form.short_book_mapping.label }} {% endif
						%}
					</label>
				</div>

				<div class="form-floating mb-3">
					<select name="{{ form.cluster_mapping.name }}" id="{{ form.cluster_mapping.id }}"
						class="form-control select2-class" multiple="multiple">
						{% for item in form.cluster_mapping.data %}
						<option value={{ item }} selected>{{ item }}
						</option>
						{% endfor %}

						{% for item in form.cluster_mapping.choices %}
						{% if item not in form.cluster_mapping.data %}
						<option value={{ item }}>{{ item }}
						</option>
						{% endif %}
						{% endfor %}
					</select>
				</div>
                <h6>Select which nodes NOT to be run initially</h6>
                <div class="row mb-3">
                    <div class="checkbox col-md-4">
                        {% if form.not_run_kivifolio.errors %} {{
                        form.not_run_kivifolio(class="form-check-input is-invalid", type="checkbox")
                        }}
                        <div class="invalid-feedback">
                            {% for error in errors %}<span>{{ error }}</span>{% endfor %}
                        </div>
                        {% else %} {{ form.not_run_kivifolio(class="form-check-input",
                        type="checkbox") }} {% endif %} {{
                        form.not_run_kivifolio.label(class="form-check-label") }}
                    </div>
                    <div class="checkbox col-md-4">
                        {% if form.not_run_cluster_backtest.errors %} {{
                        form.not_run_cluster_backtest(class="form-check-input is-invalid", type="checkbox")
                        }}
                        <div class="invalid-feedback">
                            {% for error in errors %}<span>{{ error }}</span>{% endfor %}
                        </div>
                        {% else %} {{ form.not_run_cluster_backtest(class="form-check-input",
                        type="checkbox") }} {% endif %} {{
                        form.not_run_cluster_backtest.label(class="form-check-label") }}
                    </div>
                </div>
				<div class="form-floating">
					{{ form.comments(class="form-control", style="height: 80px") }}
					<label for="floatingInput">
						{% if form.comments.flags.required %} {{
						form.comments.label(text=form.comments.label.text + ' *') }} {% else
						%} {{ form.comments.label }} {% endif %}
					</label>
				</div>
				<div class="mb-3">
					<label for="floatingInput">
						{% if form.python_file.flags.required %} {{
						form.python_file.label(text=form.python_file.label.text + ' *') }}
						{% else %} {{ form.python_file.label }} {% endif %}
					</label>
					{% if form.python_file.errors %} {{
					form.python_file(class="form-control form-control-sm is-invalid") }}
					<div class="invalid-feedback">
						{% for error in form.python_file.errors %}<span>{{ error }}</span>{%
						endfor %}
					</div>
					{% else %} {{ form.python_file(class="form-control form-control-sm")
					}} {% endif %}
				</div>
				<div class="mb-3">
					<label for="floatingInput">
						{% if form.ipynb_file.flags.required %} {{
						form.ipynb_file.label(text=form.ipynb_file.label.text + ' *') }} {%
						else %} {{ form.ipynb_file.label }} {% endif %}
					</label>
					{% if form.ipynb_file.errors %} {{ form.ipynb_file(class="form-control
					form-control-sm is-invalid") }}
					<div class="invalid-feedback">
						{% for error in form.ipynb_file.errors %}<span>{{ error }}</span>{%
						endfor %}
					</div>
					{% else %} {{ form.ipynb_file(class="form-control form-control-sm") }}
					{% endif %}
				</div>
				<br />
				<button class="w-100 btn btn-lg btn-primary" type="submit">
					<span style="pointer-events: none">{{ form.submit.label }}</span>
				</button>
				<hr class="my-4" />
			</form>
		</div>
	</div>
</div>
{% if invalid_filter is defined %} {% set popup_visibility = 'visible' %} {%
else %} {% set popup_visibility = 'hidden' %} {% endif %}
<div class="popup" id="invalid_filter_popup" style="visibility : {{popup_visibility}};">
	<img src="{{ url_for('static', filename='images/failed.png')}}" />
	<h3>Invalid python file</h3>
	<p id="reason_failure">
		{{invalid_filter}} is present in strategy code which is not allowed
	</p>
	<button type="button" id="invalid_filter_popup_close">OK</button>
</div>
{% endblock content %}
{% block scripts %}
<script src="{{ url_for('static', filename='scripts/submission.js') }}"></script>
{{super()}}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
	$(document).ready(function () {
		$(".select2-class").select2({
			placeholder: "Select Cluster",
			allowClear: true,
		});
	});
</script>
{% endblock scripts %}