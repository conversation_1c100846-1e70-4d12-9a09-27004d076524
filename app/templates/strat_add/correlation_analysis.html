{% extends "base.html" %}
{% block content %}
    <main>
        <link rel="stylesheet"
              href="{{ url_for('static', filename='css/strat_result.css') }}"/>
        <div class="d-flex align-items-center justify-content-center">
            <h1 style="font-size:30px; margin-bottom : 1em; margin-top : 1em;">Results</h1>
        </div>
        <div class="d-flex align-items-center justify-content-center">
            <dl>
                <dt class="pending">
                </dt>
                <dd>
                    <span class="key">Pending Strategy</span>
                </dd>
                <dt class="live">
                </dt>
                <dd>
                    <span class="key">Live Strategy</span>
                </dd>
                <dt class="dead">
                </dt>
                <dd>
                    <span class="key">Dead Strategy</span>
                </dd>
                <dt class="rejected">
                </dt>
                <dd>
                    <span class="key">Rejected Strategy</span>
                </dd>
                <dt class="flag">
                </dt>
                <dd>
                    <span class="key">Correlated Strategy</span>
                </dd>
            </dl>
        </div>
        <div style="display:flex; flex-direction:column; padding:2em;">
            {% for top_corr_strats in divided_top_corr_strats %}
                <div style="display:flex; align-items:center; justify-content:center;">
                    <table class="styled-table">
                        <tr>
                            <th>Strategy Name</th>
                            {% for i in range(1, 16) %}
                                <th>#{{ i }}</th>
                            {% endfor %}
                        </tr>
                        {% for key, values in top_corr_strats.items() %}
                            <tr>
                                {% if max_corr_strats[key] > 0.45 %}
                                    <td style="background-color:#AF5665;">
                                        <a href="{{ url_for('strat_add.expand_strategy', strategy_name=key) }}"
                                           target="_blank">{{ key }}</a>
                                    </td>
                                {% else %}
                                    <td style="background-color:#f3f3f3;">
                                        <a href="{{ url_for('strat_add.expand_strategy', strategy_name=key) }}"
                                           target="_blank">{{ key }}</a>
                                    </td>
                                {% endif %}
                                {% for strat, corr in values.items() %}
                                    {% if corr[1] == "pending" %}
                                        <td style="background-color: #b3e6ff;">{{ strat }} : {{ corr[0] }}</td>
                                    {% elif corr[1] == "live" %}
                                        <td style="background-color: #DAF0CA;">{{ strat }} : {{ corr[0] }}</td>
                                    {% elif corr[1] == "dead" %}
                                        <td style="background-color: #ffe680;">{{ strat }} : {{ corr[0] }}</td>
                                    {% elif corr[1] == "rejected" %}
                                        <td style="background-color: #979797;">{{ strat }} : {{ corr[0] }}</td>
                                    {% endif %}
                                {% endfor %}
                            </tr>
                        {% endfor %}
                    </table>
                    <br/>
                </div>
            {% endfor %}
            {% if error_strategy|length > 0 %}
                <div style="display:flex; align-items:center; justify-content:center;">
                    <table class="styled-table">
                        <caption>Failed strategies</caption>
                        <tr>
                            {% for error_strat in error_strategy %}<th>#{{ loop.index }}</th>{% endfor %}
                        </tr>
                        <tr>
                            {% for error_strat in error_strategy %}<td>{{ error_strat }}</td>{% endfor %}
                        </tr>
                    </table>
                </div>
            {% endif %}
        </div>
    </main>
{% endblock content %}
