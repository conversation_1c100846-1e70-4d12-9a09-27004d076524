{% extends "base.html" %}
{% block content %}
    <main>
        <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
        <link rel="stylesheet"
              href="{{ url_for('static', filename='css/strat_review.css') }}"/>
        <link rel="stylesheet"
              href="{{ url_for('static', filename='css/strat_management.css') }}"/>
        <div id="live-strats" class="tabcontent" style="display:block;">
            <div class="px-4 py-5 my-5 text-center">
                <h1 class="display-5 fw-bold" style="margin-top: -2em; font-size : 30px">Live Performance</h1>
                <div class="px-5">
                    <div class="tbl-header">
                        <table id="live_strats_table_header">
                            <thead>
                                <tr>
                                    <th name="sortable_headers_live">Strategy Name</th>
                                    {% if current_user.role.name != "DEVELOPER" %}<th name="sortable_headers_live">Developer Name</th>{% endif %}
                                    <th name="sortable_headers_live">Segment Name</th>
                                    <th name="sortable_headers_live">MonthSR</th>
                                    <th name="sortable_headers_live">DailySR</th>
                                    <th name="sortable_headers_live">Monthly Ret (in L) </th>
                                    <th name="sortable_headers_live">MaxDD (in L)</th>
                                    <th name="sortable_headers_live">RetDD</th>
                                    <th name="sortable_headers_live">Trading Days</th>
                                    <th name="sortable_headers_live">Live Days</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="tbl-content">
                        <table id="live_strats_table">
                            <tbody>
                                {% for strategy, meta_info in live_strat_info.items() %}
                                    <tr style="font-size : 14px;">
                                        <td class="clickable_cell" name="strategy_cell" value={{ strategy.strategy_name }}>
                                            {{ strategy.strategy_name }}
                                        </td>
                                        {% if current_user.role.name != "DEVELOPER" %}<td>{{ strategy.developer }}</td>{% endif %}
                                        <td>{{ strategy.segment }}</td>
                                        {% if meta_info['monthly_sr'] != None %}
                                            <td>{{ meta_info['monthly_sr']|int }}</td>
                                        {% else %}
                                            <td>{{ meta_info['monthly_sr'] }}</td>
                                        {% endif %}
                                        {% if meta_info['daily_sr'] != None %}
                                            <td>{{ meta_info['daily_sr']|int }}</td>
                                        {% else %}
                                            <td>{{ meta_info['daily_sr'] }}</td>
                                        {% endif %}
                                        {% if meta_info['avg_monthly_ret'] != None %}
                                            <td>{{ (meta_info['avg_monthly_ret'] / 1e5)|round(4) }}</td>
                                        {% else %}
                                            <td>{{ meta_info['avg_monthly_ret'] }}</td>
                                        {% endif %}
                                        {% if meta_info['max_dd'] != None %}
                                            <td>{{ (meta_info['max_dd'] / 1e5)|round(4) }}</td>
                                        {% else %}
                                            <td>{{ meta_info['max_dd'] }}</td>
                                        {% endif %}
                                        {% if meta_info['ret_dd'] != None %}
                                            <td>{{ meta_info['ret_dd']|round(2) }}</td>
                                        {% else %}
                                            <td>{{ meta_info['ret_dd'] }}</td>
                                        {% endif %}
                                        <td>{{ meta_info['trading_days'] }}</td>
                                        <td>{{ meta_info['live_days'] }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <script src="{{ url_for('static', filename='scripts/tablefilter/tablefilter.js') }}"></script>
    <script src="{{ url_for('static', filename='scripts/utility.js') }}"></script>
    <script src="{{ url_for('static', filename='scripts/live_performance_tracker.js') }}"></script>
{% endblock content %}
