{% extends "base.html" %}
{% block content %}
    <main>
        <link rel="stylesheet"
              href="{{ url_for('static', filename='css/cluster_performance.css') }}"/>
        <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
        <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
        <div id="performance" style="display:none;">{{ performance }}</div>
        <div id="removal_report" style="display:none;">{{ removal_report }}</div>
        <div id="pnl_curve" style="display:none;">{{ pnl_curve }}</div>
        <div class="my_container">
            <div class="d-flex align-items-center justify-content-center">
                {% if performance %}
                <h1 style="font-size:30px;">Performance Metrics</h1>
                {% endif %}
            </div>
            <div id="tag1"></div>
            <div id="tag2"></div>
            {% if gandalf is defined and not gandalf.empty %}
                <div class="d-flex align-items-center justify-content-center">
                    <h1 style="font-size:30px;">Live Gandalf</h1>
                </div>
                <div class="tbl-header">
                    <table id="gandalf_table_header" style="width:100%;table-layout:fixed;">
                        <thead>
                            <tr>
                                <th>
                                    
                                </th>
                                
                                <th>
                                    AvgMonthlyRet
                                </th>
                                
                                <th>
                                    MonthSR
                                </th>
                                <th>
                                    DailySR
                                </th>
                                <th>
                                    #Trades
                                </th>
                                <th>
                                    Trading Days
                                </th>
                                <th>
                                    MaxDD
                                </th>
                                <th>
                                    MaxDDdays
                                </th>
                                <th>
                                    RetDD
                                </th>
                            </tr>
                        </thead>
                    </table>
                </div>
                <div class="tbl-content">
                    <table id="gandalf_table" style="width:100%;table-layout:fixed;">
                        <tbody>
                            {% macro format_number(number) %}
                                {% if ( number|abs ) > 1e6 %}
                                    {{ '{:.6e}'.format(number) }}
                                {% else %}
                                    {{ number | round(6) }}
                                {% endif %}
                            {% endmacro %}
                            {% for row in gandalf.itertuples() %}
                                <tr>
                                    <td>
                                        {{ row.Index }}
                                    </td>
                                    <td>
                                        {{ format_number(row.AvgMonthlyRet) }}
                                    </td>
                                    <td>
                                        {{ row.MonthSR | round(6) }}
                                    </td>
                                    <td>
                                        {{ row.DailySR | round(6) }}
                                    </td>
                                    <td>
                                        {{ row.TradeCount }}
                                    </td>
                                    <td>
                                        {{ row.TradingDays }}
                                    </td>     
                                    <td>
                                        {{ format_number(row.MaxDD) }}
                                    </td>
                                    <td>
                                        {{ row.MaxDDdays }}
                                    </td>
                                    <td>
                                        {{ row.RetDD | round(6) }}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% endif %}
            {% if gandalf_data is defined %}
                <div class="d-flex align-items-center justify-content-center">
                    <h1 style="font-size:30px;">Live Gandalfs</h1>
                </div>
                {% for exchange, gandalf in gandalf_data.items() %}
                    {% if not gandalf.empty %}
                        <div class="d-flex align-items-center justify-content-center">
                            <h1 style="font-size:30px; margin-top:30px;">{{ exchange }}</h1>
                        </div>
                        <div class="tbl-header">
                            <table id="gandalf_table_header_{{ exchange }}" style="width:100%;table-layout:fixed;">
                                <thead>
                                    <tr>
                                        <th></th>
                                        <th>AvgMonthlyRet</th>
                                        <th>MonthSR</th>
                                        <th>DailySR</th>
                                        <th>#Trades</th>
                                        <th>Trading Days</th>
                                        <th>MaxDD</th>
                                        <th>MaxDDdays</th>
                                        <th>RetDD</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="tbl-content">
                            <table id="gandalf_table_{{ exchange }}" style="width:100%;table-layout:fixed;">
                                <tbody>
                                    {% macro format_number(number) %}
                                        {% if (number|abs) > 1e6 %}
                                            {{ '{:.6e}'.format(number) }}
                                        {% else %}
                                            {{ number | round(6) }}
                                        {% endif %}
                                    {% endmacro %}
                                    {% for row in gandalf.itertuples() %}
                                        <tr>
                                            <td>{{ row.Index }}</td>
                                            <td>{{ format_number(row.AvgMonthlyRet) }}</td>
                                            <td>{{ row.MonthSR | round(6) }}</td>
                                            <td>{{ row.DailySR | round(6) }}</td>
                                            <td>{{ row.TradeCount }}</td>
                                            <td>{{ row.TradingDays }}</td>
                                            <td>{{ format_number(row.MaxDD) }}</td>
                                            <td>{{ row.MaxDDdays }}</td>
                                            <td>{{ row.RetDD | round(6) }}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% endif %}
                {% endfor %}
            {% endif %}
            <br>
            <div id="tag3"></div>
        </div>
    </main>
    <script src="{{ url_for('static', filename='scripts/performance_metric.js') }}"></script>
{% endblock content %}
