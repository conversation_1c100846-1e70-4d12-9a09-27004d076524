{% extends "base.html" %}
{% block content %}
  <main>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <link rel="stylesheet"
          href="{{ url_for('static', filename='css/strat_review.css') }}"/>
    <div class="container p-5">
      <div class="d-flex align-items-center justify-content-center">
        <h1 style="font-size:30px;">Strategy Review Page</h1>
      </div>
    </div>
    <div style="padding:0em 4em; margin-top:-2em;">
      <div class="table-container">
      <div class="tbl-header">
      <table class="styled-table" id="strats">
        <caption> *Select strategies for review </caption>

        <thead>
          <tr style="font-size: 16px;">
            <th>
              <input type="checkbox" id="checkbox_all" style="border: 0.15em solid #000" />
          </th>
          <th name="sortable_headers_review">Date</th>
          <th name="sortable_headers_review">Strategy Name</th>
          <th name="sortable_headers_review">Developer Name</th>
          <th name="sortable_headers_review">Segment Name</th>
          <th name="sortable_headers_review">Cluster</th>
          <th>Backtest Result</th>
          {% if current_user.role.name != "DEVELOPER" %}
          <th>Modification Allowed</th>
          {% endif %}
         

        </tr>
        </thead>
      </table>
    </div>
    <div class="tbl-content">
      <table id="strats_body">
        <tbody>
          {% for strategy in submitted_strategies %}
            <tr style="font-size : 14px;">
              {% set access_count = strategy.strategy_access.all()|length %}
              <td>
                <input type="checkbox" class="check" name="checkbox_input"/>
              </td>
              <td>{{ strategy.submission_day }}</td>
            <td name="strat_name" class="clickable_cell"value={{ strategy.strategy_name }}>{{ strategy.strategy_name }}
              {% if access_count > 0 %}
              <span class="numberCircle">{{ access_count }}</span>
              {% endif %}
          </td>
          <td>{{ strategy.developer }}</td>
          <td>{{ strategy.segment }}</td>
          <td>
            {% set flag = namespace(val='None') %}
            {% for cluster in strategy.cluster_mapping %}
                {% if cluster.mapping_state == 0 %}
                    {% set flag.val = '' %}
                    {{ cluster.cluster_name }}&nbsp;&nbsp;
                {% endif %}
            {% endfor %}
            {{flag.val}}
          </td>
          <td>
            <div class="popup" name="backtest_result_popup" style="visibility : hidden">
              <h3 style="padding: 10px">{{ strategy.strategy_name }}</h3>
              <svg width="500" height="400" value={{ strategy.strategy_name }}>
                <rect value="{{ strategy.strategy_name }}" x="20" y="125" rx="20" ry="20" width="75" height="50" stroke="black" stroke-width="1" fill={{ color_mapping[service_state[strategy.strategy_name][0]] }} />
                <text x="57.5" y="150" dominant-baseline="middle" text-anchor="middle" fill="white">{{ config.SERVICE_INDEX[0] }}</text>
                <line x1="95" y1="125" x2="150" y2="100" style="stroke:black;stroke-width:2" marker-end="url(#arrow)"/>
                <line x1="95" y1="175" x2="150" y2="200" style="stroke:black;stroke-width:2" marker-end="url(#arrow)"/>
                <rect x="150" y="50" rx="20" ry="20" width="125" height="50" stroke="black" stroke-width="1" fill={{ color_mapping[service_state[strategy.strategy_name][1]] }} />
                <text x="212.5" y="75" dominant-baseline="middle" text-anchor="middle" fill="white">{{ config.SERVICE_INDEX[1] }}</text>
                <rect x="150" y="200" rx="20" ry="20" width="125" height="50" stroke="black" stroke-width="1" fill={{ color_mapping[service_state[strategy.strategy_name][2]] }} />
                <text x="212.5" y="225" dominant-baseline="middle" text-anchor="middle" fill="white">{{ config.SERVICE_INDEX[2] }}</text>
                <line x1="275" y1="200" x2="315" y2="175" style="stroke:black;stroke-width:2" />
                <line x1="275" y1="250" x2="315" y2="275" style="stroke:black;stroke-width:2" />
                <rect x="315" y="125" rx="20" ry="20" width="125" height="50" stroke="black" stroke-width="1" fill={{ color_mapping[service_state[strategy.strategy_name][3]] }} />
                <text x="377.5" y="150" dominant-baseline="middle" text-anchor="middle" fill="white">{{ config.SERVICE_INDEX[3] }}</text>
                <rect x="315" y="275" rx="20" ry="20" width="175" height="50" stroke="black" stroke-width="1" fill={{ color_mapping[service_state[strategy.strategy_name][4]] }} />
                <text x="402.5" y="300" dominant-baseline="middle" text-anchor="middle" fill="white">{{ config.SERVICE_INDEX[4] }}</text>
              </svg>
              <p id="metric_fail_reason"></p>
              <button type="button" name="backtest_popup_close">OK</button>
            </div>
            {% if backtest_results[strategy.strategy_name] == 'DONE' %}
              {% set button_class = 'btn btn-success btn-sm' %}
            {% elif backtest_results[strategy.strategy_name] == 'DONE PARTIAL' %}
              {% set button_class = 'btn btn-warning btn-sm' %}
            {% elif backtest_results[strategy.strategy_name] == 'ERROR' %}
              {% set button_class = 'btn btn-danger btn-sm' %}
            {% else %}
              {% set button_class = 'btn btn-info btn-sm' %}
            {% endif %}
            <button type="button" class="{{ button_class }}" name="backtest_result_btn" value="{{ strategy.strategy_name }}" style="font-weight:600;">
                {% if backtest_results[strategy.strategy_name] == 'DONE PARTIAL' %}
                  DONE
                {% else %}
                  {{ backtest_results[strategy.strategy_name] }}
                {% endif %}
            </button>
          </td>
          {% if current_user.role.name != "DEVELOPER" %}
          <td>
            <label class="switch">
              <input type="checkbox" 
                     class="toggle-checkbox" 
                     name="modify_button" 
                     value="{{ strategy.strategy_name }}"
                     {% if strategy_allow_modification_dict[strategy.strategy_name] == 1 %}checked{% endif %}>
              <span class="slider"></span>
            </label>
            </td>
            {% endif %}
        </tr>
      {% endfor %}
    </tbody>
  </table>
    </div>
  </div>
  <div style="display:flex;
              justify-content: space-around;
              margin-top:2em;
              padding-bottom:4em">
    <a href="{{ url_for('strat_add.comments_dashboard') }}"
       type="button"
       class="btn btn-danger"
       style="color: #fff;
              font-weight : 600;
              font-size:15px"
       target="_blank"> Comments Dashboard </a>
    {% if current_user.role.name != "DEVELOPER" %}
      {% set display_download_btn = "block" %}
    {% else %}
      {% set display_download_btn = "none" %}
    {% endif %}
    <button type="button"
            class="btn btn-warning"
            id="btn-download"
            style="color: #000;
                   font-weight : 600;
                   font-size:15px;
                   display:{{ display_download_btn }}">
      Download
    </button>
  </div>
  <div class="popup" id="wait_popup" style="visibility : {{ vis_success }}">
    <img src="{{ url_for('static', filename='images/waiting.svg')}}"/>
    <h3>Please wait</h3>
    <p>Strategies are under review</p>
  </div>
  <div class="popup" id="fail_popup" style="visibility : {{ vis_fail }}">
    <img src="{{ url_for('static', filename='images/failed.png')}}"/>
    <h3 style="margin-top : 0.5em;">Failed</h3>
    <p id="fail_reason">Your request could not be processed</p>
    <button type="button" id="fail_close">OK</button>
  </div>
  <div class="popup" id="encryption_popup" style="visibility : hidden">
    <img src="{{ url_for('static', filename='images/key.png')}}"/>
    <h3 style="margin-top : 0.5em;">
      Encryption Key Required
    </h3>
    <p>
      Please upload your encryption key file
    </p>
    <input type="file" id="file" name="file"/>
    <br/>
    <br/>
    <div style="display:flex">
      <button type="button"
              id="encryption_submit"
              style="width:50%;
                     margin-right:0.5em;
                     background-color: #6fd649">
        Submit
      </button>
      <button type="button" id="encryption_close" style="width:50%;">
        Cancel
      </button>
    </div>
  </div>
  <div class="popup" id="backtest_added_popup" style="visibility : hidden">
    <img src="{{ url_for('static', filename='images/tick.png')}}"/>
    <h3>
      Success
    </h3>
    <p>
      Backtesting request added
    </p>
    <button type="button"
            id="backtest_added_popup_close"
            style="background:#6fd649">
      OK
    </button>
  </div>
  <div class="popup" id="backtest_failed_popup" style="visibility : hidden">
    <img src="{{ url_for('static', filename='images/failed.png')}}"/>
    <h3>
      Failed
    </h3>
    <p>
      Backtesting request could not be processed
    </p>
    <p id="reason_backtest_fail">
    </p>
    <button type="button" id="backtest_failed_popup_close">
      OK
    </button>
  </div>
</div>
</main>
<script src="{{ url_for('static', filename='scripts/tablefilter/tablefilter.js') }}"></script>
<script src="{{ url_for('static', filename='scripts/utility.js') }}"></script>
<script src="{{ url_for('static', filename='scripts/review_strategy_dashboard.js') }}"></script>
{% endblock content %}
