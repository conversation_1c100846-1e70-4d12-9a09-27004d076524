{% extends "base.html" %}

{% block content %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/policy.css') }}" />
<div class="container mt-5">
    <h1 class="text-center mb-1">Exposure Limits</h1>
    <h5 class="text-center mb-4">(All values shown here are in lakhs)</h5>
    
    <form method="POST" action="" class="d-flex flex-column align-items-center">
        {{ form.hidden_tag() }}

        <table class="table text-center table-borderless w-50 mx-auto">
            <tbody>
                <tr>
                    <td><label for="optidx_sell_limit">Optidx Sell Limit:</label></td>
                    <td>{{ form.optidx_sell_limit(class="form-control", style="width: 250px;", id="optidx_sell_limit") }}</td>
                </tr>
                <tr>
                    <td><label for="optidx_buy_limit">Optidx Buy Limit:</label></td>
                    <td>{{ form.optidx_buy_limit(class="form-control", style="width: 250px;", id="optidx_buy_limit") }}</td>
                </tr>
                <tr>
                    <td><label for="optstk_sell_limit">Optstk Sell Limit:</label></td>
                    <td>{{ form.optstk_sell_limit(class="form-control", style="width: 250px;", id="optstk_sell_limit") }}</td>
                </tr>
                <tr>
                    <td><label for="optstk_buy_limit">Optstk Buy Limit:</label></td>
                    <td>{{ form.optstk_buy_limit(class="form-control", style="width: 250px;", id="optstk_buy_limit") }}</td>
                </tr>
                <tr>
                    <td><label for="futidx_limit">Futidx Limit:</label></td>
                    <td>{{ form.futidx_limit(class="form-control", style="width: 250px;", id="futidx_limit") }}</td>
                </tr>
                <tr>
                    <td><label for="futstk_limit">Futstk Limit:</label></td>
                    <td>{{ form.futstk_limit(class="form-control", style="width: 250px;", id="futstk_limit") }}</td>
                </tr>
                <tr>
                    <td><label for="cash_limit">Cash Limit:</label></td>
                    <td>{{ form.cash_limit(class="form-control", style="width: 250px;", id="cash_limit") }}</td>
                </tr>
            </tbody>
        </table>

        <div class="form-group text-center mt-3">
            {{ form.update(class="btn btn-primary", style="margin-top: 10px;") }}
        </div>
    </form>
</div>

<div class="popup" id="error_popup" style="visibility: hidden;">
    <img src="{{ url_for('static', filename='images/failed.png') }}" alt="Failed" />
    <h3>Failure</h3>
    <p id="reason_failure"></p>
    <button type="button" id="error_popup_close" onclick="closePopup()">OK</button>
</div>

<script>
    function showErrorPopup(message) {
        document.getElementById('reason_failure').textContent = message;
        document.getElementById('error_popup').style.visibility = 'visible';
    }

    function closePopup() {
        const errorMessage = document.getElementById('reason_failure').textContent;
        if (errorMessage === "Failed to fetch risk limits.") {
            window.location.href = "{{ url_for('strat_add.strategy_home') }}";
        } else {
            document.getElementById('error_popup').style.visibility = 'hidden';
        }
    }
</script>
{% if error_message %}
<script>
    showErrorPopup("{{ error_message }}");
</script>
{% endif %}

{% endblock content %}
