{% extends "base.html" %}
{% block content %}
<main>
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/markdown-it/dist/markdown-it.min.js"></script>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/strat_home.css') }}" />
    <div class="page_heading">
        <h1>Portfolio Analysis</h1>
    </div>
    <div>
        <div style="text-align: center;
                margin-top: 2em;
                display: flex;
                justify-content: space-around" class="dropdown-div">  
            <button type="button" value="{{current_user.username}}"
                onclick="fetchPortfolioPerformance(this)" class="btn btn-info btn-sm" id="performance_metric_btn"
                style="color: #fff; background: #99334d; font-weight: 600; font-size: 14px; border: 1px solid white; display: {{ display_perf_kivi }}">
                Portfolio Performance
            </button>
        </div>
        <div class="dropdown-div">
            {% if (current_user.role.name != "DEVELOPER") %}
            <select class="form-select" aria-label="Default select example" id="user-dropdown">
                {% for user in user_list %}
                {% if user == current_username %}
                <option value='{{ user }}' selected="selected">
                    {{ user }}
                </option>
                {% else %}
                <option value='{{ user }}'>
                    {{ user }}
                </option>
                {% endif %}
                {% endfor %}
            </select>
            <div class="dropdown" >
                <button type="button" class="btn btn-info btn-sm" data-toggle="dropdown"
                    aria-haspopup="true" aria-expanded="false" id="developer_portfolio"
                    style="font-weight: 600; font-size: 14px; border: 1px solid white;width: 155px;"
                    onclick="redirectToDeveloperPortfolio(this)">
                    Get performance
                </button>
            </div>
            {% endif %}
        </div>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">



        <div class="container-fluid px-4 py-3 my-3 text-center" style="padding-top: 0;">
            <div class="heading-container">
                <div class="line"></div>
                <h2 class="area-heading">FRESH IDEAS</h2>
                <div class="line"></div>
            </div>

            {% if current_user.role.name == "ADMIN" %}
            <button class="btn custom-btn"
                style="color: #fff; background: #602131; font-weight: 600; font-size: 14px; border: 1px solid white;margin-top: 20px;"
                id="addPolicyBtn" onclick="addPoints()">Add Ideas</button>
            {% endif %}
        </div>

        <div class="container px-3">
            <div id="card-container" class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-3 justify-content-center">
                {% for key, value in new_areas_to_work.items() %}
                <div class="col mb-4">
                    <div class="custom-card card square-card custom-border shadow">
                        <div class="card-body d-flex flex-column justify-content-center align-items-center">
                            <h4 class="custom-title card-title h5 text-center">{{
                                value[0]
                                | safe
                                }}</h4>
                            <div style="margin-bottom: auto;">
                                <p class="description">{{value[2]}}</p>

                                <div class="full-content d-none">{{ value[1] | safe }}</div>
                            </div>
                            <div class="area-buttons" id="area-buttons" style="display: block;">
                                {% if (current_user.role.name == "ADMIN") %}
                                <button class="edit-btn" title="Edit" data-content="{{ value[1] }}"
                                    data-description="{{value[2]}}" data-title="{{ value[0] }}" data-path="{{key}}"
                                    data-attachment="{{ value[3]|join(', ') }}" onclick="editPoint(this)">
                                    <i class="fas fa-pencil-alt"></i>
                                </button>
                                <button class="delete-btn" title="Delete" id="{{ key }}" onclick="deletePoint(this)">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                                <input type="file" class="attachment-input" accept=".html" id="{{key}}"
                                    style="display: none;" multiple onchange="handleAttachmentChange(this)">
                                <button class="attach-btn" title="Attach HTML files"
                                    onclick="openAttachmentInput(this)">
                                    <i class="fas fa-paperclip"></i>
                                </button>

                                {%endif%}
                                <button class="read-more-btn" data-path="{{key}}"
                                    style="background-color: #602131; color: white; font-size: 16px;"
                                    data-attachment="{{ value[3]|join(', ') }}" onclick="viewMore(event)">Read
                                    More</button>
                            </div>
                        </div>
                    </div>
                </div>
                <input type="hidden" id="attachmentData" value="{{ value[3]|join(', ') }}">

                {% endfor %}
            </div>
        </div>
        <div class="modal" id="editorModal" tabindex="-1" aria-labelledby="editorModalLabel" aria-hidden="true"
            data-bs-backdrop="static">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="editorModalLabel">Edit</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"
                            onclick="CloseEditor()"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="Heading" class="form-label">Title:</label>
                            <input type="text" class="form-control" id="Heading">
                        </div>
                        <div class="mb-3">
                            <label for="Description" class="form-label">Description:</label>
                            <input type="text" class="form-control" id="Description">
                        </div>
                        <textarea id="editor"></textarea>
                        <div class="mb-3" id="attachmentsSection" style="display: block;">
                            <label for="attachments-dropdown">Attachments:</label>
                            <select id="attachments-dropdown" style="width: auto; display: block;"
                                onchange="handleDeleteAttachment(this)">
                                <option disabled selected value> Attachments</option>
                            </select>
                            <button id="delete-attachment-btn" class="btn btn-danger mt-2">Delete
                                Attachment</button>
                        </div>


                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
                                onclick="CloseEditor()">Close</button>
                            <button type="button" class="btn btn-primary" onclick="saveChanges()">Save
                                changes</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true"
            data-bs-backdrop="static">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="previewModalLabel">Details</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <textarea id="previewMarkdown" style="display: none;"></textarea>


                    </div>
                    <!-- Hidden textarea for SimpleMDE -->
                    <div class="dropdown " style="margin-left: 10px;">
                        <button class="btn btn-secondary dropdown-toggle" type="button" id="download_attachment"
                            data-bs-toggle="dropdown" aria-expanded="false" style="margin-bottom: 20px;">
                            Attachments
                        </button>
                        <ul class="dropdown-menu" id="download_attachment_btn" aria-labelledby="html-dropdown">
                            <!-- Attachments will be dynamically added here -->
                        </ul>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="popup" id="error_popup" style="visibility : hidden;">
            <img src="{{ url_for('static', filename='images/failed.png')}}" />
            <h3>
                Failure
            </h3>
            <p id="reason_failure">
            </p>
            <button type="button" id="error_popup_close">
                OK
            </button>
        </div>
</main>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/simplemde/latest/simplemde.min.css">
<script src="https://cdn.jsdelivr.net/simplemde/latest/simplemde.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>

<script src="{{ url_for('static', filename='scripts/strat_home.js') }}"></script>
{% endblock content %}