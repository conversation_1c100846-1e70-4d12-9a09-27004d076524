{% extends "base.html" %}
{% block content %}
    <main>
        <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
        <link rel="stylesheet"
              href="{{ url_for('static', filename='css/strat_review.css') }}"/>
        <link rel="stylesheet"
              href="{{ url_for('static', filename='css/strat_management.css') }}"/>
        <div class="tab">
            <button class="tablinks active" onclick="openStrats(event, 'live-strats')">Live Strategies</button>
            <button class="tablinks" onclick="openStrats(event, 'rejected-strats')">Rejected Strategies</button>
            <button class="tablinks" onclick="openStrats(event, 'dead-strats')">Dead Strategies</button>
        </div>
        <div id="live-strats" class="tabcontent" style="display:block;">
            <div class="px-4 py-5 my-5 text-center">
                <h1 class="display-5 fw-bold" style="margin-top: -2em; font-size : 30px">Live Strategies</h1>
                <div class="legend-container">
                    <ul class="legend">
                        <li><span class="color-item" style="background-color: #ff9999;"></span>Current DD > 125% Backtested DD</li>
                        <li><span class="color-item" style="background-color: #ffc299;"></span>Current DD > 80% Backtested DD</li>
                    </ul>
                </div>
                <a href="{{ url_for('strat_add.delete_tradelog_page') }}"
                    type="button"
                    class="btn btn-danger btn-small"
                    style="margin-top: -5em; margin-left: -30em;">Delete Tradelog</a>
                <a href="{{ url_for('strat_add.todo_dashboard', status='LIVE') }}"
                   type="button"
                   class="btn btn-warning btn-small"
                   style="margin-right:-60em;
                          margin-top: -5em">To Do Dashboard</a>
                <div class="px-5">
                    <div class="tbl-header">
                        <table id="live_strats_table_header">
                            <thead>
                                <tr>
                                    <th name="sortable_headers_live">Strategy Name</th>
                                    {% if current_user.role.name != "DEVELOPER" %}<th name="sortable_headers_live">Developer Name</th>{% endif %}
                                    <th name="sortable_headers_live">DD ratio</th>
                                    <th name="sortable_headers_live">Monte DD ratio</th>
                                    <th name="sortable_headers_live">Segment Name</th>
                                    <th name="sortable_headers_live">Live Days</th>
                                    <th name="sortable_headers_live">Slippage (bps)</th>
                                    <th>Comments / Todo</th>
                                    {% if current_user.role.name != "DEVELOPER" %}<th>Remove</th>{% endif %}
                                    <th>Clear Pickle</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="tbl-content">
                        <table id="live_strats_table">
                            <tbody>
                                {% for strategy, meta_list in live_strats.items() %}
                                    {% if meta_list[0] > 0.8 and meta_list[0] < 1.25 %}
                                        {% set curr_backtest_dd = '#ffc299' %}
                                    {% elif meta_list[0] > 1.25 %}
                                        {% set curr_backtest_dd = '#ff9999' %}
                                    {% endif %}
                                    <tr style="font-size : 14px;">
                                        <td class="clickable_cell" name="strategy_cell" value={{ strategy.strategy_name }} style="background-color: {{ curr_backtest_dd }}">
                                            {{ strategy.strategy_name }}
                                        </td>
                                        {% if current_user.role.name != "DEVELOPER" %}<td>{{ strategy.developer }}</td>{% endif %}
                                        <td>{{ meta_list[0]|round(2) }}</td>
                                        <td>{{ meta_list[1]|round(2) }}</td>
                                        <td>{{ strategy.segment }}</td>
                                        <td>{{ meta_list[2] }}</td>
                                        <td>
                                            {% if strategy.strategy_name in slippage_strat_dict %}
                                                {{ slippage_strat_dict[strategy.strategy_name][0]|round(2) }}
                                            {% else %}
                                                0
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if live_strats_review_comments[strategy.strategy_name] == True %}
                                                <a href="{{ url_for('strat_add.show_strategy_review', strategy_name=strategy.strategy_name) }}"
                                                   title="View Comments"
                                                   type="button"
                                                   class="btn btn-info btn-sm"
                                                   style="color: #fff;
                                                          font-weight : 600;
                                                          background: #371E29;
                                                          border:none"> Comments / Todo </a>
                                            {% else %}
                                                <button type="button"
                                                        title="Review comments not available"
                                                        class="btn btn-warning btn-sm"
                                                        style="pointer-events:auto;
                                                               cursor: not-allowed;
                                                               color: #fff;
                                                               font-weight : 600;
                                                               background: #371E29;
                                                               border:none"
                                                        disabled>
                                                    Comments / Todo
                                                </button>
                                            {% endif %}
                                        </td>
                                        {% if current_user.role.name != "DEVELOPER" %}
                                            <td>
                                                <button type="button" class="btn btn-danger btn-sm" style="color: #fff; font-weight : 550;" name="inactive_btns" value={{ strategy.strategy_name }}> Inactivate
                                                </button>
                                            </td>
                                        {% endif %}
                                        <td>
                                            {% if strategy.strategy_name in resetted_strats_live %}
                                            <button type="button"
                                                    name="reset_btns"
                                                    value={{strategy.strategy_name}}
                                                    class="btn btn-secondary btn-sm"
                                                    style="margin-bottom: 5px; width: 105px;">
                                                    UnReset Live
                                            </button>
                                            {% else %}
                                            <button type="button"
                                                    name="reset_btns"
                                                    value={{strategy.strategy_name}}
                                                    class="btn btn-primary btn-sm"
                                                    style="margin-bottom: 5px; width: 105px;">
                                                    Reset Live
                                            </button>
                                            {% endif %}
                                            {% if strategy.strategy_name not in strats_not_in_test_env %}
                                                {% if strategy.strategy_name in resetted_strats_test %}
                                                <button type="button"
                                                        name="reset_btns_test"
                                                        value={{strategy.strategy_name}}
                                                        class="btn btn-secondary btn-sm"
                                                        style="margin-bottom: 5px; width: 105px;">
                                                        UnReset Test
                                                </button>
                                                {% else %}
                                                <button type="button"
                                                        name="reset_btns_test"
                                                        value={{strategy.strategy_name}}
                                                        class="btn btn-primary btn-sm"
                                                        style="margin-bottom: 5px; width: 105px;">
                                                        Reset Test
                                                </button>
                                                {% endif %}
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div id="rejected-strats" class="tabcontent">
            <div class="px-4 py-5 my-5 text-center">
                <h1 class="display-5 fw-bold" style="margin-top: -2em; font-size : 30px">Rejected Strategies</h1>
                <a href="{{ url_for('strat_add.todo_dashboard', status='REJECTED') }}"
                type="button"
                class="btn btn-warning btn-small"
                style="margin-right:-60em;
                        margin-top: -5em">To Do Dashboard</a>
                <div class="px-5">
                    <div class="tbl-header">
                        <table id="rejected_strats_table_header">
                            <thead>
                                <tr>
                                    <th name="sortable_headers_rejected">Strategy Name</th>
                                    {% if current_user.role.name != "DEVELOPER" %}<th name="sortable_headers_rejected">Developer Name</th>{% endif %}
                                    <th name="sortable_headers_rejected">Segment Name</th>
                                    <th name="sortable_headers_rejected">Rejected Date</th>
                                    <th>
                                        Comments / Todo
                                    </th>
                                    <th>
                                        Add to Backtest
                                    </th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="tbl-content">
                        <table id="rejected_strats_table">
                            <tbody>
                                {% for strategy in rejected_strats %}
                                    <tr style="font-size : 14px;">
                                        <td class="clickable_cell" name="strategy_cell" value={{ strategy.strategy_name }}>
                                            {{ strategy.strategy_name }}
                                        </td>
                                        {% if current_user.role.name != "DEVELOPER" %}
                                            <td>
                                                {{ strategy.developer }}
                                            </td>
                                        {% endif %}
                                        <td>
                                            {{ strategy.segment }}
                                        </td>
                                        <td>
                                            {{ strategy.submission_day }}
                                        </td>
                                        <td>
                                            {% if rejected_strats_review_comments[strategy.strategy_name] == True %}
                                                <a href="{{ url_for('strat_add.show_strategy_review', strategy_name=strategy.strategy_name) }}"
                                                   title="View Comments"
                                                   type="button"
                                                   class="btn btn-info btn-sm"
                                                   style="color: #fff;
                                                          font-weight : 600;
                                                          background: #371E29;
                                                          border:none">Comments / Todo</a>
                                            {% else %}
                                                <button type="button"
                                                        title="Review comments not available"
                                                        class="btn btn-warning btn-sm"
                                                        style="pointer-events:auto;
                                                               cursor: not-allowed;
                                                               color: #fff;
                                                               font-weight : 600;
                                                               background: #371E29;
                                                               border:none"
                                                        disabled>
                                                    Comments / Todo
                                                </button>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if strategy.strategy_name in rejected_backtest_dict %}
                                                {% if (current_user.role.name == 'DEVELOPER') and (rejected_backtest_dict[strategy.strategy_name] != current_user.username) %}
                                                <button type="button"
                                                    name="rejected_backtest"
                                                    class="btn btn-warning btn-sm">
                                                    Added
                                                </button>
                                                {% else %}
                                                <button type="button"
                                                    name="rejected_backtest"
                                                    value={{strategy.strategy_name}}
                                                    class="btn btn-danger btn-sm">
                                                    Delete
                                                </button>
                                                {% endif %}
                                            {% else %}
                                            <button type="button"
                                                    name="rejected_backtest"
                                                    value={{strategy.strategy_name}}
                                                    class="btn btn-success btn-sm">
                                                    Add
                                            </button>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div id="dead-strats" class="tabcontent">
            <div class="px-4 py-5 my-5 text-center">
                <h1 class="display-5 fw-bold" style="margin-top: -2em; font-size : 30px">
                    Dead Strategies
                </h1>
                <div class="legend-container">
                    <ul class="legend">
                        <li><span class="color-item" style="background-color: #ff9999;"></span>Current DD > 125% Backtested DD</li>
                        <li><span class="color-item" style="background-color: #ffc299;"></span>Current DD > 80% Backtested DD</li>
                    </ul>
                </div>
                <a href="{{ url_for('strat_add.todo_dashboard', status='DEAD') }}"
                   type="button"
                   class="btn btn-warning btn-small"
                   style="margin-right:-60em;
                          margin-top: -5em">To Do Dashboard</a>
                <div class="px-5">
                    <div class="tbl-header">
                        <table id="dead_strats_table_header">
                            <thead>
                                <tr>
                                    <th name="sortable_headers_dead">
                                        Strategy Name
                                    </th>
                                    {% if current_user.role.name != "DEVELOPER" %}
                                        <th name="sortable_headers_dead">
                                            Developer Name
                                        </th>
                                    {% endif %}
                                    <th name="sortable_headers_dead">
                                        DD ratio
                                    </th>
                                    <th name="sortable_headers_dead">
                                        Segment Name
                                    </th>
                                    <th name="sortable_headers_dead">
                                        Last Run Day
                                    </th>
                                    <th name="sortable_headers_live">
                                        Slippage (bps)
                                    </th>
                                    <th>
                                        Comments / Todo
                                    </th>
                                    <th>
                                        Add to Backtest
                                    </th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="tbl-content">
                        <table id="dead_strats_table">
                            <tbody>
                                {% for strategy, meta_values in dead_strats.items() %}
                                    {% if meta_values[0] > 0.8 and meta_values[0] < 1.25 %}
                                        {% set curr_backtest_dd = '#ffc299' %}
                                    {% elif meta_values[0] > 1.25 %}
                                        {% set curr_backtest_dd = '#ff9999' %}
                                    {% endif %}
                                    <tr style="font-size : 14px;">
                                        <td class="clickable_cell" name="strategy_cell" value={{ strategy.strategy_name }} style="background-color: {{ curr_backtest_dd }}">
                                            {{ strategy.strategy_name }}
                                        </td>
                                        {% if current_user.role.name != "DEVELOPER" %}
                                            <td>
                                                {{ strategy.developer }}
                                            </td>
                                        {% endif %}
                                        <td>
                                            {{ meta_values[0]|round(2) }}
                                        </td>
                                        <td>
                                            {{ strategy.segment }}
                                        </td>
                                        <td>
                                            {{ strategy.last_run_day }}
                                        </td>
                                        <td>
                                            {% if strategy.strategy_name in slippage_strat_dict %}
                                                {{ slippage_strat_dict[strategy.strategy_name][0]|round(2) }}
                                            {% else %}
                                                0
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if dead_strats_review_comments[strategy.strategy_name] == True %}
                                                <a href="{{ url_for('strat_add.show_strategy_review', strategy_name=strategy.strategy_name) }}"
                                                   title="View Comments"
                                                   type="button"
                                                   class="btn btn-info btn-sm"
                                                   style="color: #fff;
                                                          font-weight : 600;
                                                          background: #371E29;
                                                          border:none">Comments / Todo</a>
                                            {% else %}
                                                <button type="button"
                                                        title="Review comments not available"
                                                        class="btn btn-warning btn-sm"
                                                        style="pointer-events:auto;
                                                               cursor: not-allowed;
                                                               color: #fff;
                                                               font-weight : 600;
                                                               background: #371E29;
                                                               border:none"
                                                        disabled>
                                                    Comments / Todo
                                                </button>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if strategy.strategy_name in dead_backtest_dict %}
                                                {% if (current_user.role.name == 'DEVELOPER') and (dead_backtest_dict[strategy.strategy_name] != current_user.username) %}
                                                <button type="button"
                                                    name="dead_backtest"
                                                    class="btn btn-warning btn-sm">
                                                    Added
                                                </button>
                                                {% else %}
                                                <button type="button"
                                                    name="dead_backtest"
                                                    value={{strategy.strategy_name}}
                                                    class="btn btn-danger btn-sm">
                                                    Delete
                                                </button>
                                                {% endif %}
                                            {% else %}
                                            <button type="button"
                                                    name="dead_backtest"
                                                    value={{strategy.strategy_name}}
                                                    class="btn btn-success btn-sm">
                                                    Add
                                            </button>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="popup" id="sucess_popup" style="visibility : hidden;">
            <img src="{{ url_for('static', filename='images/tick.png')}}"/>
            <h3>
                Success
            </h3>
            <p id="reason_success">
            </p>
            <div style="display:flex; justify-content:center; text-align:left;">
                <div>
                    <ul id="clusters_removed">
                    </ul>
                </div>
            </div>
            <button type="button" id="success_popup_close" style="background:#6fd649">
                OK
            </button>
        </div>
        <div class="popup" id="failure_popup" style="visibility : hidden;">
            <img src="{{ url_for('static', filename='images/failed.png')}}"/>
            <h3>
                Failed
            </h3>
            <p id="reason_failure">
            </p>
            <button type="button" id="failure_popup_close">
                OK
            </button>
        </div>
    </main>
    <script src="{{ url_for('static', filename='scripts/tablefilter/tablefilter.js') }}"></script>
    <script src="{{ url_for('static', filename='scripts/utility.js') }}"></script>
    <script src="{{ url_for('static', filename='scripts/strat_management.js') }}"></script>
{% endblock content %}
