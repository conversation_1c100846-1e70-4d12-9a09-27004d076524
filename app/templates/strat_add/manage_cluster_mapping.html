{% extends "base.html" %}
{% block content %}

<main>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <link
        href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css"
        rel="stylesheet"
    />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <link
        href="https://fonts.googleapis.com/css?family=Expletus Sans"
        rel="stylesheet"
    />
    <link rel="stylesheet"
              href="{{ url_for('static', filename='css/strat_management.css') }}"/>

    <script>
        const liveStrategiesList = {{ live_strategies | tojson }};
        const testStrategiesList = {{ test_strategies | tojson }};
    </script>
    
    <div class="tab">
        <button class="tablinks active" onclick="selectMappingType(event, 'live', liveStrategiesList)"><b>Live Strategies</b></button>
        <button class="tablinks" onclick="selectMappingType(event, 'test', testStrategiesList)"><b>Test Strategies</b></button>
    </div>
        
    <div class="px-4 py-5 my-5 text-center">
        <h1 class="display-5 fw-bold" style="margin-top: -2em; font-size: 30px">
            Change Cluster Mapping
        </h1>
        <div class="d-grid gap-2 d-sm-flex justify-content-sm-center">
            Select the <b><span id="mappingtype">LIVE</span></b> strategy and the clusters it must be mapped to
        </div>
    </div>

    <div class="col-md-10 mx-auto col-lg-8" style="margin-top: -4em;width:40%">
        <form
            method="post"
            enctype="multipart/form-data"
            id="manage_cluster_mapping"
            action=""
            class="p-4 p-md-5 border rounded-3 bg-light"
        >
            {{ form.hidden_tag() }}

            <!-- Hidden field to pass information about which mapping is being changed - test or live -->
            <input type="hidden" id="ENV" name="ENV" value="LIVE_ENV">

            <!-- field to take strategy input -->
            <div class="form-floating mb-3">
                <select
                    name="{{form.strategies.name}}"
                    id="{{form.strategies.id}}"
                    class="form-control select2-strategies"
                    style="width: 100%;"
                >
                    <option value="">-- Select a strategy --</option>
                    {% for value in form.strategies.choices %}
                    <option value="{{ value }}">
                        {{ value }}
                    </option>
                    {% endfor %}
                </select>
            </div>

            <!-- field to take cluster input -->
            <div class="form-floating mb-3">
                <select
                    name="{{ form.cluster_mapping.name }}"
                    id="{{ form.cluster_mapping.id }}"
                    class="form-control select2-cluster-mapping"
                    multiple="multiple"
                >
                    {% for value in form.cluster_mapping.choices %}
                    <option value="{{ value }}">{{ value }}</option>
                    {% endfor %}
                </select>
            </div>

            <!-- submit button to the form -->
            <div>
                <button  style="margin-top: 0.5em" class="w-100 btn btn-lg btn-primary" type="submit">
                    <span style="pointer-events: none"
                        >{{ form.submit.label }}</span
                    >
                </button>
            </div>
        </form>
    </div>
    
    <script src="{{ url_for('static', filename='scripts/manage_cluster_mapping.js') }}"></script>
</main>
{% endblock content %}
