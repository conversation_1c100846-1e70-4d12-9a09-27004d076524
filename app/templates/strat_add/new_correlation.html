{% extends "base.html" %}
{% block content %}
    <main>
        <link rel="stylesheet"
              href="{{ url_for('static', filename='css/strat_review.css') }}"/>
        <link rel="stylesheet"
              href="{{ url_for('static', filename='css/strat_management.css') }}"/>
        <div class="d-flex align-items-center justify-content-center">
            <h1 style="font-size:30px; margin-bottom : 1em; margin-top : 1em;">{{ base_strategy_name }} to Live Strategies Correlation Analysis</h1>
        </div>
        <div style="display:flex; flex-direction:column; padding:2em;">
            <div class="px-5">
                <div class="tbl-header">
                    <table id="corr_table_header">
                        <thead>
                            <tr>
                                <th name="sortable_headers">Strategy Name</th>
                                <th name="sortable_headers">PnL Dependence</th>
                                <th name="sortable_headers">Trade Dependence</th>
                                <th name="sortable_headers">Union Correlation</th>
                                <th name="sortable_headers">Intersection Correlation</th>
                                <th name="sortable_headers">Start Date</th>
                            </tr>
                        </thead>
                    </table>
                </div>
                <div class="tbl-content">
                    <table id="corr_table">
                        <tbody>
                            {% for strategy, corr_info_dict in final_corr.items() %}
                                <tr style="font-size : 14px;">
                                    <td class="clickable_cell" name="strategy_cell" value={{ strategy }}>
                                        {{ strategy }}
                                    </td>
                                    <td>{{ corr_info_dict['pnl_dependence'] }}</td>
                                    <td>{{ corr_info_dict['trade_dependence'] }}</td>
                                    <td>{{ corr_info_dict['union_corr'] }}</td>
                                    <td>{{ corr_info_dict['intersection_corr'] }}</td>
                                    <td>{{ corr_info_dict['start_date'] }}</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <br />
        <br />
        <br />

        <div class="d-flex align-items-center justify-content-center">
            <h1 style="font-size:30px; margin-bottom : 1em; margin-top : 1em;">Live Strategies to {{ base_strategy_name }} Correlation Analysis</h1>
        </div>
        <div style="display:flex; flex-direction:column; padding:2em;">
            <div class="px-5">
                <div class="tbl-header">
                    <table id="reverse_corr_table_header">
                        <thead>
                            <tr>
                                <th name="sortable_headers_reverse">Strategy Name</th>
                                <th name="sortable_headers_reverse">PnL Dependence</th>
                                <th name="sortable_headers_reverse">Trade Dependence</th>
                                <th name="sortable_headers_reverse">Union Correlation</th>
                                <th name="sortable_headers_reverse">Intersection Correlation</th>
                                <th name="sortable_headers_reverse">Start Date</th>
                            </tr>
                        </thead>
                    </table>
                </div>
                <div class="tbl-content">
                    <table id="reverse_corr_table">
                        <tbody>
                            {% for strategy, corr_info_dict in final_reverse_corr.items() %}
                                <tr style="font-size : 14px;">
                                    <td class="clickable_cell" name="strategy_cell" value={{ strategy }}>
                                        {{ strategy }}
                                    </td>
                                    <td>{{ corr_info_dict['pnl_dependence'] }}</td>
                                    <td>{{ corr_info_dict['trade_dependence'] }}</td>
                                    <td>{{ corr_info_dict['union_corr'] }}</td>
                                    <td>{{ corr_info_dict['intersection_corr'] }}</td>
                                    <td>{{ corr_info_dict['start_date'] }}</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>
    <script src="{{ url_for('static', filename='scripts/tablefilter/tablefilter.js') }}"></script>
    <script src="{{ url_for('static', filename='scripts/utility.js') }}"></script>
    <script src="{{ url_for('static', filename='scripts/new_correlation.js') }}"></script>
{% endblock content %}
