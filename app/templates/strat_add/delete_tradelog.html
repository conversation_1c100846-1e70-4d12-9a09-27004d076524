{% extends "base.html" %} {% block content %}

<main>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <link
        href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css"
        rel="stylesheet"
    />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <link
        href="https://fonts.googleapis.com/css?family=Expletus Sans"
        rel="stylesheet"
    />
    <div class="px-4 py-5 my-5 text-center">
        <h1 class="display-5 fw-bold" style="margin-top: -2em; font-size: 30px">
            Delete tradelog for sentinel backtests
        </h1>
        <div class="d-grid gap-2 d-sm-flex justify-content-sm-center">
            Select the live strategies for which you want to delete tradelog
        </div>
    </div>
    <div class="col-md-10 mx-auto col-lg-8" style="margin-top: -4em;width:40%">
        <form
            method="post"
            enctype="multipart/form-data"
            id="delete_tradelog"
            action=""
            class="p-4 p-md-5 border rounded-3 bg-light"
        >
            {{ form.hidden_tag() }}

            <div class="form-floating mb-3">
                <select
                    name="{{ form.strategies.name }}"
                    id="{{ form.strategies.id }}"
                    class="form-control select2-strategies"
                    multiple="multiple"
                >
                    {% for value in form.strategies.choices %}
                    <option value="{{ value }}" {% if value in form.strategies.data %} selected {% endif %}>{{ value }}</option>
                    {% endfor %}
                </select>
                {% if form.strategies.errors %}
                <div class="text-danger">
                    {{ form.strategies.errors[0] }}
                </div>
                {% endif %}
            </div>

            <!-- Submit button -->
            <div>
                <button  style="margin-top: 0.5em" class="w-100 btn btn-lg btn-primary" type="submit">
                    <span style="pointer-events: none"
                        >{{ form.submit.label }}</span
                    >
                </button>
            </div>
        </form>
    </div>
    <script>
        $('.select2-strategies').select2();
    </script>
</main>
{% endblock content %}
