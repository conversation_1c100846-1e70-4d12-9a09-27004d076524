<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8"/>
        <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <title>SAMBA</title>
        <link rel="icon"
              type="image/x-icon"
              href="{{ url_for('static', filename='images/logo.png') }}"/>
        <link href='https://fonts.googleapis.com/css?family=Expletus Sans'
              rel='stylesheet'/>
        <link rel="stylesheet"
              href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.1/dist/css/bootstrap.min.css"/>
        <link rel="stylesheet"
              href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"/>
        <link rel="stylesheet"
              href="{{ url_for('static', filename='css/header.css') }}"/>
        {% block head %}
        {% endblock head %}
    </head>
    <body>
        <header class="header">
            <div class="header_navbar">
                {% if current_user.is_authenticated == True %}
                    <h1 id="heading">
                        <a href="{{ url_for('main.index') }}">SAM<span>BA</span></a>
                    </h1>
                {% else %}
                    <h1 id="heading">
                        <a href="{{ url_for('main.index') }}">SAM<span>BA</span></a>
                    </h1>
                {% endif %}
                <div class="avatar">
                    {% if current_user.is_authenticated == True %}
                        <a href="{{ url_for('auth.change_password') }}"
                           style="background-color : #101010">
                            <img class="profile"
                                 src="{{ url_for('static', filename='images/user.png') }}"/>
                        </a>
                    {% endif %}
                </div>
            </div>
            {% if current_user.is_authenticated == True %}
                <div class="sidebar" id="sidebar">
                    <img class="close"
                         src="{{ url_for('static', filename='images/menu.svg') }}"
                         id="toggle_button"/>
                    <div class="logo-details">
                        <div class="logo_name">
                            <div class="logo">
                                <h1 id="logo">
                                    <a href="{{ url_for('main.index') }}">SAM<span>BA</span></a>
                                </h1>
                            </div>
                        </div>
                    </div>
                    <ul class="nav-list">
                        <li>
                            <a href="{{ url_for('strat_add.pending_strategy_page') }}" id="home_button">
                                <img class="img-sidebar"
                                     src="{{ url_for('static', filename='images/pending.png') }}"/>
                                <span class="links_name">Pending Strategies</span>
                            </a>
                            <span class="tooltip">Pending Strategies</span>
                        </li>
                        {% if current_user.role.name != "DEVELOPER" %}
                            <li>
                                <a href="{{ url_for('auth.manage_users') }}" id="manage_user_button">
                                    <img class="img-sidebar"
                                         src="{{ url_for('static', filename='images/register.png') }}"/>
                                    <span class="links_name">User Management</span>
                                </a>
                                <span class="tooltip">Manage Users</span>
                            </li>
                        {% endif %}
                        <li>
                            <a href="{{ url_for('strat_add.review_strategy_dashboard') }}"
                               id="review_strat_button">
                                <img class="img-sidebar"
                                     src="{{ url_for('static', filename='images/review.png') }}"/>
                                <span class="links_name">Review Strategy</span>
                            </a>
                            <span class="tooltip">Review Strategy</span>
                        </li>
                        <li>
                            <a href="{{ url_for('strat_add.test_strategy_page') }}"
                            id="test_strat_button">
                                <img class="img-sidebar"
                                    src="{{ url_for('static', filename='images/strats_manager.png') }}"/>
                                <span class="links_name">Test Strategies</span>
                            </a>
                            <span class="tooltip">Test-only mode strategies</span>
                        </li>
                        <li>
                            <a href="{{ url_for('strat_add.strategy_manager') }}"
                               id="live_strat_button">
                                <img class="img-sidebar"
                                     src="{{ url_for('static', filename='images/strats_manager.png') }}"/>
                                <span class="links_name">Strategy Management</span>
                            </a>
                            <span class="tooltip">Strategy Management</span>
                        </li>
                        <li>
                            <a href="{{ url_for('slip_dashboard.slippage_home', segment='CASH') }}">
                                <img class="img-sidebar"
                                     src="{{ url_for('static', filename='images/addition.png') }}"/>
                                <span class="links_name">Slippage Dashboard</span>
                            </a>
                            <span class="tooltip">Slippage Dashboard</span>
                        </li>
                        <li>
                            <a href="{{ url_for('slip_dashboard.advanced_slippage_dashboard') }}">
                                <img class="img-sidebar"
                                     src="{{ url_for('static', filename='images/growth.png') }}"/>
                                <span class="links_name">Advanced Slippage</span>
                            </a>
                            <span class="tooltip">Advanced Slippage Analysis</span>
                        </li>
                        <li>
                            <a href="{{ url_for('strat_add.get_cluster_backtest') }}" id="run_backtest_button">
                                <img class="img-sidebar"
                                     src="{{ url_for('static', filename='images/testing.png') }}"/>
                                <span class="links_name">Run Backtest</span>
                            </a>
                            <span class="tooltip">Run Backtest</span>
                        </li>
                        <li>
                            <a href="{{ url_for('strat_add.live_performance_tracker') }}">
                                <img class="img-sidebar"
                                     src="{{ url_for('static', filename='images/growth.png') }}"/>
                                <span class="links_name">Live Performance</span>
                            </a>
                            <span class="tooltip">Live Performance</span>
                        </li>
                        {% if current_user.role.name != "DEVELOPER" %}
                        <li>
                            <a href="{{ url_for('strat_add.manage_cluster_mapping_page') }}" id="change_cluster_map_button">
                                <img class="img-sidebar"
                                     src="{{ url_for('static', filename='images/testing.png') }}"/>
                                <span class="links_name">Manage Cluster mapping</span>
                            </a>
                            <span class="tooltip">Manage Cluster Mapping</span>
                        </li>
                        {% endif %}
                        {% if current_user.role.name == "ADMIN" %}
                            <li>
                                <a href="{{ url_for('strat_add.risk_management') }}" id="manage_risk_button">
                                    <img class="img-sidebar"
                                         src="{{ url_for('static', filename='images/testing.png') }}"/>
                                    <span class="links_name">Risk Management</span>
                                </a>
                                <span class="tooltip">Adjust Limits</span>
                            </li>
                        {% endif %}
                        <div class="bottom-sidebar">
                            <li>
                                <a href="{{ url_for('strat_add.get_policy_page') }}" id="strategy_policies_button">
                                    <img class="img-sidebar" src="{{ url_for('static', filename='images/policy.png') }}"/>
                                    <span class="links_name">Strategy Policies</span>
                                </a>
                                <span class="tooltip">Strategy Policies</span>
                            </li>
                        </div>
                        
                    </ul>
                </div>
            {% endif %}
        </header>
    {% with messages=get_flashed_messages(with_categories=True) %}
    {% if messages %}
    {% for category, message in messages %}<div class="alert alert-{{ category }}">{{ message }}</div>{% endfor %}
    {% endif %}
    {% endwith %}
    {% block content %}
    {% endblock content %}
    </body>
    {% block scripts %}
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js"
                    integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo"
                    crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.4.1/dist/js/bootstrap.min.js"
    integrity="sha384-wfSDF2E50Y2D1uUdj0O3uMBJnjuUD4Ih7YwaYd1iqfktj0Uod8GCExl3Og8ifwB6" crossorigin="anonymous"></script>

    <script src="{{ url_for('static', filename='scripts/home.js') }}"></script>
    {% endblock scripts %}

</html>