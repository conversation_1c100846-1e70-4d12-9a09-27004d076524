from flask import render_template, current_app, redirect, url_for, request, abort, jsonify
from app.slip_dashboard import slip_dashboard
from flask_login import login_required
import datetime
import json
import pandas as pd
import numpy as np
from flasgger import swag_from
from app.utility.slippage_util import (
    generate_overall_analysis_dict,
    generate_slip_graph,
    generate_time_slip_graph,
)
from app.utility.advanced_slippage_util import (
    process_slippage_data,
    generate_slippage_trend_chart,
    generate_strategy_comparison_chart,
    calculate_summary_statistics
)
from app.utility.trade_vision_util import (
    process_trade_comparison
)


@slip_dashboard.route("/slip_dashboard/<segment>", methods=["GET"])
@swag_from(
    "/docs/slip_dashboard/slip_home_get.yaml",
    methods=["GET"],
)
@login_required
def slippage_home(segment):
    notional = "quantity * signal_price"
    execution_notional = "quantity * execution_signal_price"
    signal_slippage_bps = f"(sum({notional} * signal_slip) * 10000 / sum({notional}))"
    execution_slippage_bps = f"(sum({execution_notional} * execution_slip) * 10000 / sum({execution_notional}))"
    trade_slippage_bps = f"(sum({notional} * trade_slip) * 10000 / sum({notional}))"
    one_year = (datetime.date.today() - datetime.timedelta(days=365)).strftime(
        "%Y-%m-%d"
    )
    six_moths = (datetime.date.today() - datetime.timedelta(days=182)).strftime(
        "%Y-%m-%d"
    )
    last_month = (datetime.date.today() - datetime.timedelta(days=30)).strftime(
        "%Y-%m-%d"
    )

    # Get list of segments traded in last one year
    query_segment_lis = current_app.config["CLICKHOUSE_CLIENT"].execute(
        f"select distinct(segment) from slippage_trade where toDate(timestamp) > '{one_year}' and buysell=-1 "
        f"INTERSECT select distinct(segment) from slippage_trade where toDate(timestamp) > '{one_year}' and buysell=1;"
    )
    segment_list = [x[0] for x in query_segment_lis]
    # If current segment is not in the list, redirect to cash dashboard
    if segment not in segment_list:
        return redirect(url_for("slip_dashboard.slippage_home", segment="CASH"))

    # query different kind of analysis from clickhouse
    query_res_per_day = current_app.config["CLICKHOUSE_CLIENT"].execute(
        f"select toDate(timestamp), {signal_slippage_bps}, {execution_slippage_bps}, {trade_slippage_bps}, sum({notional}) / 1e7 "
        f"from slippage_trade where segment='{segment}' group by toDate(timestamp);"
    )
    query_res_last_year = current_app.config["CLICKHOUSE_CLIENT"].execute(
        f"select buysell, {signal_slippage_bps}, sum({notional}) / 1e7 from slippage_trade where "
        f"toDate(timestamp) > '{one_year}' and segment='{segment}' group by buysell;"
    )
    query_res_six_month = current_app.config["CLICKHOUSE_CLIENT"].execute(
        f"select buysell, {signal_slippage_bps} , sum({notional}) / 1e7 from slippage_trade "
        f"where toDate(timestamp) > '{six_moths}' and segment='{segment}' group by buysell;"
    )
    query_res_last_month = current_app.config["CLICKHOUSE_CLIENT"].execute(
        f"select buysell, {signal_slippage_bps} , sum({notional}) / 1e7 from slippage_trade "
        f"where toDate(timestamp) > '{last_month}' and segment='{segment}' group by buysell;"
    )

    segment_last_year_slip = generate_overall_analysis_dict(query_res_last_year)
    segment_six_month_slip = generate_overall_analysis_dict(query_res_six_month)
    segment_last_month_slip = generate_overall_analysis_dict(query_res_last_month)

    segment_overall_slip = {
        "one_year": segment_last_year_slip,
        "six_month": segment_six_month_slip,
        "last_month": segment_last_month_slip,
    }

    segment_slippage_dict = {}
    for item in query_res_per_day:
        segment_slippage_dict[item[0]] = [item[1], item[2], item[3], item[4]]

    segment_slippage_dict = dict(sorted(segment_slippage_dict.items(), reverse=True))
    slippage_info_for_charts = {
        k: [v[0], v[3]] for k, v in segment_slippage_dict.items()
    }

    graph_dict = {}
    segment_graphs = generate_slip_graph(slippage_info_for_charts)
    graph_dict["day_slippage"] = segment_graphs[0]
    graph_dict["day_exposure"] = segment_graphs[1]

    segment_list.sort()

    return render_template(
        "slip_dashboard/slip_home.html",
        current_segment=segment,
        segment_list=segment_list,
        segment_slippage_dict=segment_slippage_dict,
        segment_overall_slip=segment_overall_slip,
        graph_dict=graph_dict,
    )


@slip_dashboard.route("/time_graph", methods=["GET"])
@swag_from(
    "/docs/slip_dashboard/time_graph_get.yaml",
    methods=["GET"],
)
@login_required
def slippage_time_graph():
    segment = request.args.get("segment", None)
    start_date = request.args.get("start_date", None)
    end_date = request.args.get("end_date", None)

    try:
        start_date_formatted = datetime.datetime.strptime(start_date, "%Y-%m-%d")
        end_date_formatted = datetime.datetime.strptime(end_date, "%Y-%m-%d")
    except Exception as e:
        current_app.logger.warning(f"Passed date are in wrong format {e}")
        abort(404)

    if start_date_formatted >= end_date_formatted:
        current_app.logger.warning("Start date is greater than end date")
        abort(404)

    # Get list of segments traded in last one year
    one_year = (datetime.date.today() - datetime.timedelta(days=365)).strftime(
        "%Y-%m-%d"
    )
    query_segment_lis = current_app.config["CLICKHOUSE_CLIENT"].execute(
        f"select distinct(segment) from slippage_trade where toDate(timestamp) > '{one_year}' and buysell=-1 "
        f"INTERSECT select distinct(segment) from slippage_trade where toDate(timestamp) > '{one_year}' and buysell=1;"
    )
    segment_list = [x[0] for x in query_segment_lis]
    if segment not in segment_list:
        return

    notional = "quantity * executed_price"
    signal_slippage_bps = f"(sum({notional} * signal_slip) * 10000 / sum({notional}))"
    query_res_timestamp = current_app.config["CLICKHOUSE_CLIENT"].execute(
        f"select toHour(timestamp), toMinute(timestamp), buysell, {signal_slippage_bps}, sum({notional}) / "
        f"1e7 from slippage_trade where segment='{segment}' and toDate(timestamp)>='{start_date}' and toDate(timestamp)<='{end_date}' group by "
        f"toHour(timestamp), toMinute(timestamp), buysell;"
    )

    segment_time_slippage_dict = {1: {}, -1: {}}
    for item in query_res_timestamp:
        time_instance = datetime.time(item[0], item[1])
        segment_time_slippage_dict[item[2]][time_instance] = [item[3], item[4]]

    segment_time_slippage_dict[1] = dict(sorted(segment_time_slippage_dict[1].items()))
    segment_time_slippage_dict[-1] = dict(
        sorted(segment_time_slippage_dict[-1].items())
    )

    graph_dict = dict()
    if len(segment_time_slippage_dict[1]) > 0:
        buy_side_graphs = generate_time_slip_graph(
            segment_time_slippage_dict[1], buy_sell="Buy"
        )
        graph_dict["buy_timewise_slippage"] = buy_side_graphs[0]
        graph_dict["buy_timewise_exposure"] = buy_side_graphs[1]

    if len(segment_time_slippage_dict[-1]) > 0:
        sell_side_graphs = generate_time_slip_graph(
            segment_time_slippage_dict[-1], buy_sell="Sell"
        )
        graph_dict["sell_timewise_slippage"] = sell_side_graphs[0]
        graph_dict["sell_timewise_exposure"] = sell_side_graphs[1]

    return render_template(
        "slip_dashboard/slip_time_graph.html",
        current_segment=segment,
        graph_dict=graph_dict,
    )


@slip_dashboard.route("/slip_dashboard/advanced_slippage", methods=["GET"])
@login_required
def advanced_slippage_dashboard():
    """Advanced slippage analysis dashboard with enhanced filtering and visualization."""
    return render_template("slip_dashboard/advanced_slippage.html")


@slip_dashboard.route("/slip_dashboard/api/slippage_data", methods=["POST"])
@login_required
def get_slippage_data():
    """API endpoint to fetch slippage data based on filters."""
    try:
        data = request.get_json()

        # Extract filter parameters
        start_date = data.get('start_date', '2024-01-01')
        end_date = data.get('end_date', '2024-01-31')
        segments = data.get('segments', ['OPTIDX'])
        exchanges = data.get('exchanges', ['IND'])
        strategies = data.get('strategies', [])
        slaves = data.get('slaves', [])

        # Build query conditions
        segment_list = ','.join([f"'{s}'" for s in segments])
        segment_condition = f"segment IN ({segment_list})"
        date_condition = f"toDate(timestamp) >= '{start_date}' AND toDate(timestamp) <= '{end_date}'"

        # Base query for slippage data
        base_query = f"""
        SELECT
            strategy_name,
            slave_name,
            segment,
            toDate(timestamp) as date,
            sum(total_slippage) as total_slippage,
            sum(execution_signal_slippage) as execution_signal_slippage,
            sum(execution_slippage) as execution_slippage,
            sum(turnover) as turnover,
            count(*) as trade_count
        FROM slippage_trade
        WHERE {segment_condition} AND {date_condition}
        """

        # Add strategy/slave filters if provided
        if strategies:
            strategy_list = ','.join([f"'{s}'" for s in strategies])
            strategy_condition = f"strategy_name IN ({strategy_list})"
            base_query += f" AND {strategy_condition}"

        if slaves:
            slave_list = ','.join([f"'{s}'" for s in slaves])
            slave_condition = f"slave_name IN ({slave_list})"
            base_query += f" AND {slave_condition}"

        base_query += " GROUP BY strategy_name, slave_name, segment, date ORDER BY date DESC"

        # Execute query
        result = current_app.config["CLICKHOUSE_CLIENT"].execute(base_query)

        # Convert to DataFrame for easier processing
        df = pd.DataFrame(result, columns=[
            'strategy_name', 'slave_name', 'segment', 'date',
            'total_slippage', 'execution_signal_slippage', 'execution_slippage',
            'turnover', 'trade_count'
        ])

        # Calculate ratios
        df['total_slip_to_turnover'] = (df['total_slippage'] / df['turnover'] * 10000).fillna(0)
        df['exec_slip_to_turnover'] = (df['execution_slippage'] / df['turnover'] * 10000).fillna(0)
        df['signal_slip_to_turnover'] = (df['execution_signal_slippage'] / df['turnover'] * 10000).fillna(0)

        # Prepare response data
        response_data = {
            'success': True,
            'data': {
                'raw_data': df.to_dict('records'),
                'summary': {
                    'total_trades': int(df['trade_count'].sum()),
                    'total_turnover': float(df['turnover'].sum()),
                    'total_slippage': float(df['total_slippage'].sum()),
                    'avg_slip_to_turnover': float(df['total_slip_to_turnover'].mean()),
                    'date_range': {
                        'start': start_date,
                        'end': end_date
                    }
                }
            }
        }

        return jsonify(response_data)

    except Exception as e:
        current_app.logger.error(f"Error fetching slippage data: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500


@slip_dashboard.route("/slip_dashboard/api/chart_data/<chart_type>", methods=["POST"])
@login_required
def get_chart_data(chart_type):
    """API endpoint to fetch chart data for different visualizations."""
    try:
        data = request.get_json()

        # Extract filter parameters
        start_date = data.get('start_date', '2024-01-01')
        end_date = data.get('end_date', '2024-01-31')
        segments = data.get('segments', ['OPTIDX'])

        if chart_type == 'slippage_trend':
            # Daily slippage trend data
            segment_list = ','.join([f"'{s}'" for s in segments])
            query = f"""
            SELECT
                toDate(timestamp) as date,
                sum(total_slippage) as total_slippage,
                sum(turnover) as turnover
            FROM slippage_trade
            WHERE segment IN ({segment_list})
            AND toDate(timestamp) >= '{start_date}'
            AND toDate(timestamp) <= '{end_date}'
            GROUP BY date
            ORDER BY date
            """

            result = current_app.config["CLICKHOUSE_CLIENT"].execute(query)
            df = pd.DataFrame(result, columns=['date', 'total_slippage', 'turnover'])
            df['slip_to_turnover_bps'] = (df['total_slippage'] / df['turnover'] * 10000).fillna(0)

            chart_data = {
                'x': df['date'].astype(str).tolist(),
                'y': df['slip_to_turnover_bps'].tolist(),
                'type': 'scatter',
                'mode': 'lines+markers',
                'name': 'Daily Slippage (BPS)',
                'line': {'color': '#4d0000', 'width': 2}
            }

        elif chart_type == 'strategy_comparison':
            # Strategy comparison bar chart
            segment_list = ','.join([f"'{s}'" for s in segments])
            query = f"""
            SELECT
                strategy_name,
                sum(total_slippage) as total_slippage,
                sum(execution_slippage) as execution_slippage,
                sum(turnover) as turnover
            FROM slippage_trade
            WHERE segment IN ({segment_list})
            AND toDate(timestamp) >= '{start_date}'
            AND toDate(timestamp) <= '{end_date}'
            GROUP BY strategy_name
            ORDER BY total_slippage DESC
            LIMIT 10
            """

            result = current_app.config["CLICKHOUSE_CLIENT"].execute(query)
            df = pd.DataFrame(result, columns=['strategy_name', 'total_slippage', 'execution_slippage', 'turnover'])

            chart_data = [
                {
                    'x': df['strategy_name'].tolist(),
                    'y': df['total_slippage'].tolist(),
                    'type': 'bar',
                    'name': 'Total Slippage',
                    'marker': {'color': '#4d0000'}
                },
                {
                    'x': df['strategy_name'].tolist(),
                    'y': df['execution_slippage'].tolist(),
                    'type': 'bar',
                    'name': 'Execution Slippage',
                    'marker': {'color': '#c50000'}
                }
            ]

        elif chart_type == 'timing_histogram':
            # Execution timing distribution
            segment_list = ','.join([f"'{s}'" for s in segments])
            query = f"""
            SELECT
                toSecond(executed_timestamp) - toSecond(timestamp) as execution_delay
            FROM slippage_trade
            WHERE segment IN ({segment_list})
            AND toDate(timestamp) >= '{start_date}'
            AND toDate(timestamp) <= '{end_date}'
            AND execution_delay >= 0 AND execution_delay <= 60
            """

            result = current_app.config["CLICKHOUSE_CLIENT"].execute(query)
            delays = [row[0] for row in result]

            chart_data = {
                'x': delays,
                'type': 'histogram',
                'nbinsx': 30,
                'name': 'Execution Delay Distribution',
                'marker': {'color': '#4d0000', 'opacity': 0.7}
            }

        else:
            return jsonify({'success': False, 'error': 'Invalid chart type'}), 400

        return jsonify({
            'success': True,
            'chart_data': chart_data,
            'layout': {
                'autosize': True,
                'responsive': True,
                'margin': {'t': 50, 'r': 20, 'b': 50, 'l': 60},
                'plot_bgcolor': 'white',
                'paper_bgcolor': 'white'
            }
        })

    except Exception as e:
        current_app.logger.error(f"Error fetching chart data: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500


@slip_dashboard.route("/slip_dashboard/api/filter_options", methods=["GET"])
@login_required
def get_filter_options():
    """API endpoint to fetch available filter options."""
    try:
        # Get date range for last 6 months
        six_months_ago = (datetime.date.today() - datetime.timedelta(days=180)).strftime("%Y-%m-%d")

        # Mock data for testing - in real implementation, query the database
        segments = ['OPTIDX', 'OPTIDX_BSE', 'OPTIDX_US', 'OPTSTK', 'FUTSTK']
        strategies = ['strategy_alpha_v1', 'strategy_beta_v2', 'strategy_gamma_v1', 'strategy_delta_v1']
        slaves = ['slave_alpha_1', 'slave_beta_2', 'slave_gamma_3', 'slave_delta_4']
        exchanges = ['IND', 'NSE', 'BSE', 'US']

        return jsonify({
            'success': True,
            'options': {
                'segments': segments,
                'strategies': strategies,
                'slaves': slaves,
                'exchanges': exchanges
            }
        })

    except Exception as e:
        current_app.logger.error(f"Error fetching filter options: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500


# Test route without authentication for debugging
@slip_dashboard.route("/slip_dashboard/api/test_filter_options", methods=["GET"])
def test_get_filter_options():
    """API endpoint to fetch available filter options."""
    try:
        # Get date range for last 6 months
        six_months_ago = (datetime.date.today() - datetime.timedelta(days=180)).strftime("%Y-%m-%d")

        # Get available segments
        segments_query = f"""
        SELECT DISTINCT segment
        FROM slippage_trade
        WHERE toDate(timestamp) >= '{six_months_ago}'
        ORDER BY segment
        """
        segments_result = current_app.config["CLICKHOUSE_CLIENT"].execute(segments_query)
        segments = [row[0] for row in segments_result]

        # Get available strategies
        strategies_query = f"""
        SELECT DISTINCT strategy_name
        FROM slippage_trade
        WHERE toDate(timestamp) >= '{six_months_ago}'
        AND strategy_name != ''
        ORDER BY strategy_name
        LIMIT 50
        """
        strategies_result = current_app.config["CLICKHOUSE_CLIENT"].execute(strategies_query)
        strategies = [row[0] for row in strategies_result]

        # Get available slaves
        slaves_query = f"""
        SELECT DISTINCT slave_name
        FROM slippage_trade
        WHERE toDate(timestamp) >= '{six_months_ago}'
        AND slave_name != ''
        ORDER BY slave_name
        LIMIT 50
        """
        slaves_result = current_app.config["CLICKHOUSE_CLIENT"].execute(slaves_query)
        slaves = [row[0] for row in slaves_result]

        # Static exchange options based on segments
        exchanges = ['IND', 'NSE', 'BSE', 'US']

        return jsonify({
            'success': True,
            'options': {
                'segments': segments,
                'strategies': strategies,
                'slaves': slaves,
                'exchanges': exchanges
            }
        })

    except Exception as e:
        current_app.logger.error(f"Error fetching filter options: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500


@slip_dashboard.route("/slip_dashboard/api/trade_comparison", methods=["POST"])
@login_required
def get_trade_comparison():
    """API endpoint for TradeVision comparison functionality."""
    try:
        data = request.get_json()

        # Extract parameters
        start_date = data.get('start_date', '2024-01-01')
        end_date = data.get('end_date', '2024-01-31')
        slave_strategy = data.get('slave_strategy', '')
        segment = data.get('segment', 'OPTIDX')
        exchange = data.get('exchange', 'IND')

        if not slave_strategy:
            return jsonify({'success': False, 'error': 'Slave/Strategy name is required'}), 400

        # Use the trade vision utility to process comparison
        result = process_trade_comparison(start_date, end_date, slave_strategy, segment, exchange)

        return jsonify(result)

    except Exception as e:
        current_app.logger.error(f"Error in trade comparison: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500
