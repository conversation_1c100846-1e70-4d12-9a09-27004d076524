import datetime
import json
from io import BytesIO
from zipfile import ZipFile
import os
import pandas as pd
from flasgger import swag_from
from flask import (
    abort,
    current_app,
    flash,
    redirect,
    render_template,
    request,
    send_file,
    url_for,
)
from flask_login import current_user, login_required
from wtforms.validators import ValidationError

from app.models import (
    DeadSheet,
    PendingBacktests,
    StrategyClusterMapping,
    Status,
    Strategy,
    StrategyAccess,
    StrategyReview,
    User,
    StrategyMetaData,
    ClusterMappingStatus,
    db,
)
from app.strat_add import strat_add
from app.strat_add.strat_add_forms import (
    ClusterBacktestForm,
    StrategyReviewForm,
    StrategySubmissionForm,
    ChangeClusterMappingForm,
    RiskManagementForm,
    DeleteTradelogForm,
)
from app.utility.slippage_util import generate_slip_graph
from app.utility.strat_detail_util import (
    check_object_exists,
    get_accessible_strategies,
    get_backend_service_result,
    get_backend_service_state,
    get_charts_from_minio,
    get_gandalf,
    get_live_pnl_curve,
    get_review_comments,
    get_strategies,
    get_strats_not_in_test_env,
    get_strategies_dd_percent,
    get_strategies_with_metadata,
    get_user_lists,
    remove_access,
    remove_backtests,
    get_available_historical_dates,
    convert_date_to_filename_format,
    get_all_md_files,
    get_resetted_strats,
)
from app.utility.strat_manage_util import (
    delete_file_from_minio,
    delete_from_minio,
    upload_html_file_on_minio,
    upload_to_minio,
    update_content_on_minio,
    add_deleted_tradelogs_on_minio,
    fetch_deleted_tradelogs_from_minio,
)
from app.utility.strat_review_util import (
    accept_strategy,
    check_access,
    check_authenticity,
    check_cluster_authenticity,
    check_strategy_for_invalid_filters,
    clean_review,
    get_correlation_live_strats,
    get_correlation_pending_strats,
    get_fresh_correlation,
    is_all_empty_review,
    kill_strategy,
    reject_strategy,
    start_downstream_service,
    validate_download_request,
)
from app.utility.utils import (
    get_file_data_from_minio,
    get_files_from_minio,
    list_files_in_folder,
    upload_dataframe_to_minio,
    send_message_to_kafka,
)
from app.utility.risk_util import (
    fetch_risk_limits,
    update_risk_limits,
)


@strat_add.route("/strat_sub", methods=["GET", "POST"])
@swag_from(
    "/docs/strategy_addition_modification_and_deletion/add_strategy_get.yaml",
    methods=["GET"],
)
@swag_from(
    "/docs/strategy_addition_modification_and_deletion/add_strategy_post.yaml",
    methods=["POST"],
)
@login_required
def add_strategy():
    # View for adding a new strategy for review.
    # Pick available clusters from pushed strategies
    choice_list = [
        strategy.strategy_name
        for strategy in Strategy.query.filter(
            Strategy.strategy_name.contains("cluster")
        ).all()
        if strategy.status.state_description == "LIVE"
    ]
    form = StrategySubmissionForm()
    form.cluster_mapping.choices = choice_list
    form.exchange_name.choices = current_app.config["SUPPORTED_EXCHANGES"]
    current_app.logger.info("Strategy addition page accessed")

    # validate submitted strategy and insert into database
    if form.validate_on_submit():
        strat = Strategy.query.filter_by(strategy_name=form.strategy_name.data).first()
        if strat is not None:
            form.strategy_name.errors += (
                "Live strategy already exists with this name!!",
            )
            return render_template(
                "strat_add/submission.html",
                form=form,
                legend="Add a new strategy for review",
                type="new",
            )

        invalid_filter = check_strategy_for_invalid_filters(form.python_file.data)
        if invalid_filter:
            return render_template(
                "strat_add/submission.html",
                form=form,
                legend="Add a new strategy for review",
                type="new",
                invalid_filter=invalid_filter,
            )
        upload_to_minio(
            form.python_file.data, form.ipynb_file.data, form.strategy_name.data
        )
        strategy = Strategy(
            strategy_name=form.strategy_name.data,
            developer=current_user.username,
            segment=form.segment.data,
            exchange_name=form.exchange_name.data,
            backtest_start_date=form.backtest_date.data,
            book_long=form.long_book_mapping.data,
            book_short=form.short_book_mapping.data,
            strat_state=1,
            submission_day=datetime.datetime.now(),
            trigger_coeff=form.trigger_coeff.data,
            limit_coeff=form.limit_coeff.data,
            expiration_time=form.expiration_time.data,
            long_short=form.long_short.data,
            comments=form.comments.data,
            reworked_strategy=form.old_strategy.data,
            custom_slippage=form.custom_slippage.data,
            overlap_days=form.overlap_days.data,
        )
        for cluster in form.cluster_mapping.data:
            strategy.cluster_mapping.append(
                StrategyClusterMapping(
                    cluster_name=cluster,
                    mapping_status=ClusterMappingStatus.get_status(
                        description="LIVE_ENV"
                    ),
                )
            )
            strategy.cluster_mapping.append(
                StrategyClusterMapping(
                    cluster_name=cluster,
                    mapping_status=ClusterMappingStatus.get_status(
                        description="TEST_ENV"
                    ),
                )
            )

        db.session.add(strategy)
        new_backtest = PendingBacktests(
            request_time=datetime.datetime.now(),
            strategy_name=form.strategy_name.data,
        )
        new_backtest.service_state = "000"
        if form.not_run_kivifolio.data is True:
            new_backtest.service_state += "3"
        else:
            new_backtest.service_state += "0"
        if form.not_run_cluster_backtest.data is True:
            new_backtest.service_state += "3"
        else:
            new_backtest.service_state += "0"

        db.session.add(new_backtest)
        db.session.commit()
        current_app.logger.info(f"Strategy {strategy.strategy_name} added")
        flash("Strategy submitted successfully", "success")
        return redirect(url_for("main.index"))

    form.cluster_mapping.data = []
    return render_template(
        "strat_add/submission.html",
        form=form,
        legend="Add a new strategy for review",
        type="new",
    )


@strat_add.route("/strat_sub/modify/<strat_name>", methods=["GET", "POST"])
@swag_from(
    "/docs/strategy_addition_modification_and_deletion/modify_strategy_get.yaml",
    methods=["GET"],
)
@swag_from(
    "/docs/strategy_addition_modification_and_deletion/modify_strategy_post.yaml",
    methods=["POST"],
)
@login_required
def modify_strategy(strat_name: str):
    """View for modifying a strategy.

    Args:
        strat_name (str): strategy name

    """
    try:
        strategy = Strategy.query.get_or_404(strat_name)
    except Exception as e:
        current_app.logger.error(f"Strategy {strat_name} not found")
        raise e
    if strategy.developer != current_user.username:
        current_app.logger.warning(
            f"Unauthorized attempt to modify strategy {strat_name}"
        )
        abort(403)
    strat = PendingBacktests.query.filter_by(strategy_name=strat_name).first()
    if strat is None:
        current_app.logger.warning(f"Strategy {strat_name} is not a pending strategy.")
        abort(403)

    if strat.allow_modification == 0:
        flash("You cannot modify the strategy.Contact your Manager", "danger")
        return redirect(url_for("strat_add.pending_strategy_page"))
    form = StrategySubmissionForm()
    form.exchange_name.choices = current_app.config["SUPPORTED_EXCHANGES"]
    # Pick available clusters from pushed strategies
    form.cluster_mapping.choices = [
        strategy.strategy_name
        for strategy in Strategy.query.filter(
            Strategy.strategy_name.contains("cluster")
        ).all()
        if strategy.status.state_description == "LIVE"
    ]

    # validate submitted strategy and insert into database
    selected_clusters = [
        cluster.cluster_name
        for cluster in strategy.cluster_mapping
        if cluster.mapping_status
        == ClusterMappingStatus.get_status(description="LIVE_ENV")
    ]
    if form.validate_on_submit():
        upload_to_minio(
            form.python_file.data, form.ipynb_file.data, form.strategy_name.data
        )
        strategy.strategy_name = form.strategy_name.data
        strategy.developer = current_user.username
        strategy.segment = form.segment.data
        strategy.exchange_name = form.exchange_name.data
        strategy.backtest_start_date = form.backtest_date.data
        strategy.book_long = form.long_book_mapping.data
        strategy.book_short = form.short_book_mapping.data
        strategy.strat_state = 1
        strategy.submission_day = datetime.datetime.now()
        strategy.trigger_coeff = form.trigger_coeff.data
        strategy.limit_coeff = form.limit_coeff.data
        strategy.expiration_time = form.expiration_time.data
        strategy.long_short = form.long_short.data
        strategy.comments = form.comments.data
        strategy.reworked_strategy = form.old_strategy.data
        strategy.overlap_days = form.overlap_days.data
        strategy.custom_slippage = form.custom_slippage.data
        if form.is_rework.data is False:
            strategy.reworked_strategy = None
        pending_backtest = PendingBacktests.query.filter_by(
            strategy_name=form.strategy_name.data
        ).first()
        new_service_state = "000"
        if form.not_run_kivifolio.data is True:
            new_service_state += "3"
        else:
            new_service_state += "0"
        if form.not_run_cluster_backtest.data is True:
            new_service_state += "3"
        else:
            new_service_state += "0"
        if pending_backtest:
            pending_backtest.request_time = datetime.datetime.now()
            pending_backtest.service_state = new_service_state
        else:
            new_backtest = PendingBacktests(
                request_time=datetime.datetime.now(),
                strategy_name=form.strategy_name.data,
            )
            new_backtest.service_state = new_service_state
            db.session.add(new_backtest)

        for cluster in strategy.cluster_mapping:
            db.session.delete(cluster)
        for cluster in form.cluster_mapping.data:
            strategy.cluster_mapping.append(
                StrategyClusterMapping(
                    cluster_name=cluster,
                    strategy_name=form.strategy_name.data,
                    mapping_status=ClusterMappingStatus.get_status(
                        description="LIVE_ENV"
                    ),
                )
            )
            strategy.cluster_mapping.append(
                StrategyClusterMapping(
                    cluster_name=cluster,
                    strategy_name=form.strategy_name.data,
                    mapping_status=ClusterMappingStatus.get_status(
                        description="TEST_ENV"
                    ),
                )
            )
        db.session.commit()
        current_app.logger.info(f"Strategy {strategy.strategy_name} modified")
        flash("Strategy modified successfully", "success")
        return redirect(url_for("main.index"))
    elif request.method == "GET":
        current_app.logger.info("Strategy modification page accessed")
        form.strategy_name.data = strategy.strategy_name
        form.segment.data = strategy.segment
        form.exchange_name.data = strategy.exchange_name
        form.trigger_coeff.data = strategy.trigger_coeff
        form.limit_coeff.data = strategy.limit_coeff
        form.expiration_time.data = strategy.expiration_time
        form.long_short.data = strategy.long_short
        form.long_book_mapping.data = strategy.book_long
        form.short_book_mapping.data = strategy.book_short
        form.cluster_mapping.data = selected_clusters
        form.comments.data = strategy.comments
        form.old_strategy.data = strategy.reworked_strategy
        form.overlap_days.data = strategy.overlap_days
        form.custom_slippage.data = strategy.custom_slippage
        if form.old_strategy.data is not None:
            form.is_rework.data = True

    return render_template(
        "strat_add/submission.html",
        form=form,
        legend=f"Modify strategy: {strategy.strategy_name}",
        type="modify",
    )


@strat_add.route("/strat_sub/<strategy_name>/delete", methods=["POST"])
@swag_from(
    "/docs/strategy_addition_modification_and_deletion/delete_strategy.yaml",
    methods=["POST"],
)
@login_required
def delete_strategy(strategy_name: str):
    """Deleting a strategy.

    Args:
        strategy_name (str): strategy name
    """
    try:
        calling_location = request.args.get("calling_location")
        strategy = Strategy.query.get_or_404(strategy_name)
    except Exception as e:
        current_app.logger.error(f"Strategy {strategy_name} not found")
        raise e
    pending_state = Status.query.filter_by(state_description="PENDING").first().id

    if strategy.strat_state != pending_state:
        current_app.logger.warning(f"{strategy_name} does not support deletion")
        abort(403)

    is_authorized = check_authenticity(strategy_list=[strategy.strategy_name])
    if strategy.developer != current_user.username and (is_authorized is False):
        current_app.logger.warning(
            f"Unauthorized attempt to delete strategy {strategy_name}"
        )
        abort(403)
    strat = PendingBacktests.query.filter_by(strategy_name=strategy_name).first()
    if strat is None:
        current_app.logger.warning(
            f"Strategy {strategy_name} is not a pending strategy."
        )
        abort(403)
    if strat.allow_modification == 0:
        flash("You cannot delete the strategy.Contact your Manager", "danger")
        if calling_location == "expand":
            return redirect(
                url_for("strat_add.expand_strategy", strategy_name=strategy_name)
            )
        else:
            return redirect(url_for("strat_add.pending_strategy_page"))

    remove_backtests(strategy_name=strategy_name)
    remove_access(strategy_name=strategy_name)
    delete_from_minio(strategy.strategy_name, "pending_strats")
    for cluster in strategy.cluster_mapping:
        db.session.delete(cluster)
    db.session.delete(strategy)
    db.session.commit()
    current_app.logger.info(f"Strategy {strategy_name} deleted")
    flash("Strategy deleted successfully", "success")
    if calling_location == "expand":
        return redirect(url_for("strat_add.review_strategy_dashboard"))
    else:
        return redirect(url_for("strat_add.pending_strategy_page"))


@strat_add.route("/control_modification", methods=["POST"])
@swag_from(
    "/docs/strategy_review/control_modification.yaml",
    methods=["POST"],
)
@login_required
def control_modification():
    """controls if the developer can modify/delete the strategy"""
    strategy_name = request.form["strategy"]
    try:
        strategy = PendingBacktests.query.filter_by(strategy_name=strategy_name).first()
    except Exception as e:
        current_app.logger.error(f"Strategy {strategy_name} not found")
        raise e
    modification_allowed = strategy.allow_modification
    is_authorized = check_authenticity(strategy_list=[strategy.strategy_name])
    if is_authorized is False:
        current_app.logger.warning(
            f"Unauthorized attempt to change if modification allowed for {strategy_name}"
        )
        abort(403)

    strategy.allow_modification = not modification_allowed
    db.session.commit()
    return "success"



@strat_add.route("/strat_review", methods=["GET", "POST"])
@swag_from("/docs/strategy_review/review_strategy_dashboard_get.yaml", methods=["GET"])
@swag_from(
    "/docs/strategy_review/review_strategy_dashboard_post.yaml", methods=["POST"]
)
@login_required
def review_strategy_dashboard():
    # View for strategy review page.
    if request.method in ["GET"]:
        current_app.logger.info("Review strategy page accessed")
        pending_strats = []
        is_authorized = check_authenticity(strategy_list=[])
        if is_authorized is True:
            pending_strats = get_strategies()
        pending_strats.extend(get_accessible_strategies())
        pending_strats = list(set(pending_strats))
        pending_strats.sort(key=lambda x: x.submission_day)
        pending_strats_name = list(strat.strategy_name for strat in pending_strats)
        # To access allow_modification field without establishing model relationship
        strategy_to_review = PendingBacktests.query.filter(
            PendingBacktests.strategy_name.in_(pending_strats_name)
        ).all()
        strategy_allow_modification_dict = {}
        correlation_info_exists = []
        for strat in pending_strats_name:
            if check_object_exists(
                f"pending_strats/{strat}/{strat}_correlation_info.parquet"
            ) and check_object_exists(
                f"pending_strats/{strat}/{strat}_reverse_correlation_info.parquet"
            ):
                correlation_info_exists.append(strat)

        for strat in strategy_to_review:
            strategy_name = strat.strategy_name
            allow_modification = strat.allow_modification
            strategy_allow_modification_dict[strategy_name] = allow_modification

        backtest_results = get_backend_service_result(pending_strats_name)
        backend_service_state = get_backend_service_state(pending_strats_name)
        color_mapping = {"0": "#33cc33", "1": "red", "2": "#008000", "3": "#b8860b"}
        return render_template(
            "strat_add/review_strategy_dashboard.html",
            submitted_strategies=pending_strats,
            backtest_results=backtest_results,
            service_state=backend_service_state,
            color_mapping=color_mapping,
            correlation_info_exists=correlation_info_exists,
            strategy_allow_modification_dict=strategy_allow_modification_dict,
        )
    else:
        review_strategy_list = request.form["strategies"]
        review_strategy_list = json.loads(review_strategy_list)
        is_authorized = check_authenticity(strategy_list=review_strategy_list)
        is_authorized_access = check_access(strategy_list=review_strategy_list)
        pending_strats = []
        if (is_authorized_access is False) and (is_authorized is False):
            current_app.logger.warning(
                f"Unauthorized attempt to access strategy review page for strategies {review_strategy_list}"
            )
            return redirect(url_for("main.index"))
        if is_authorized is True:
            pending_strats = get_strategies()
        pending_strats.extend(get_accessible_strategies())
        pending_strats = list(set(pending_strats))
        review_strategy_info = []
        for strat in pending_strats:
            if strat.strategy_name in review_strategy_list:
                review_strategy_info.append(
                    [strat.strategy_name, strat.backtest_start_date.year]
                )

        corr_pending_strats, _ = get_correlation_pending_strats(review_strategy_info)
        corr_pending_strats = corr_pending_strats.to_dict()
        if len(corr_pending_strats) == 0:
            current_app.logger.info(
                "Review results request failed as there is not even a single strategy which can be reviewed"
            )
            return "results_not_available"
        return "success"


@strat_add.route("/test_strats", methods=["GET"])
@login_required
def test_strategy_page():
    """
    View for test strategies page - used by admin/manager to manage test-only mode strategies in their scope
    """
    if request.method in ["GET"]:
        current_app.logger.info("Test strategy page accessed")
        test_mode_strats = []
        is_authorized = check_authenticity(strategy_list=[])
        if is_authorized is True:
            test_mode_strats = get_strategies(state_description="TEST")
        test_mode_strats.extend(get_accessible_strategies(state_description="TEST"))
        test_mode_strats = list(set(test_mode_strats))
        test_mode_strats.sort(key=lambda x: x.submission_day)
        resetted_strats = get_resetted_strats(environment="TEST")
        return render_template(
            "strat_add/test_strats.html",
            test_mode_strategies=test_mode_strats,
            resetted_strats=resetted_strats,
        )


@strat_add.route("/<strategy_name>/expand", methods=["GET"])
@swag_from(
    "/docs/strategy_detailing_and_expansion/expand_strategy.yaml", methods=["GET"]
)
@login_required
def expand_strategy(strategy_name: str):
    """View for expanding a strategy.

    Args:
        strategy_name (str): strategy name
    """
    try:
        strategy = Strategy.query.get_or_404(strategy_name)
    except Exception as e:
        current_app.logger.error(f"Strategy {strategy_name} not found")
        raise e
    status = strategy.status.state_description.lower()

    is_authentic = check_authenticity(
        strategy_list=[strategy.strategy_name], state_description=status.upper()
    )
    is_authentic_access = check_access(strategy_list=[strategy.strategy_name])
    if (
        (strategy.developer != current_user.username)
        and (is_authentic is False)
        and (is_authentic_access is False)
    ):
        current_app.logger.warning(
            f"Unauthorized attempt to access expand strategy page for {strategy_name}"
        )
        abort(403)
    current_app.logger.info("Expand strategy page accessed")
    test_mode_clusters = [
        cluster.cluster_name
        for cluster in strategy.cluster_mapping
        if cluster.mapping_status
        == ClusterMappingStatus.get_status(description="TEST_ENV")
    ]
    live_mode_clusters = [
        cluster.cluster_name
        for cluster in strategy.cluster_mapping
        if cluster.mapping_status
        == ClusterMappingStatus.get_status(description="LIVE_ENV")
    ]
    backtest_result = ""
    if status == "pending":
        backtest_results = get_backend_service_result([strategy.strategy_name])
        backtest_result = backtest_results[strategy_name]
    retrieval_url = get_charts_from_minio(strategy_name=strategy_name, status=status)
    titles = {
        "dd": "Cumulative Distribution of DD (%)",
        "monte": "Monte Carlo Simulation",
        "ret": "Scatter Plot of BackTest Returns",
        "kde": "KDE plot of insample and outsample returns",
    }
    meta_data = strategy.meta_data.first()
    review_data = strategy.review.first()
    user_list, users = get_user_lists(strategy=strategy)
    notional = "quantity * executed_price"
    signal_slippage_bps = f"(sum({notional} * signal_slip) * 10000 / sum({notional}))"
    query_res_per_day = current_app.config["CLICKHOUSE_CLIENT"].execute(
        f"select toDate(timestamp), {signal_slippage_bps}, sum({notional}) / 1e7 from slippage_trade "
        f"where strategy_name='{strategy_name}' or slave_name = '{strategy_name}' group by toDate(timestamp);"
    )
    slippage_dict = {}
    for item in query_res_per_day:
        slippage_dict[item[0]] = [item[1], item[2]]
    slippage_dict = dict(sorted(slippage_dict.items(), reverse=True))
    if len(slippage_dict) > 0:
        slip_graphs = generate_slip_graph(slippage_dict)
    else:
        slip_graphs = None

    try:
        sentinel_kivifolio_list = []
        if check_object_exists("submitted_requests/sentinel_kivifolio.csv"):
            data = get_file_data_from_minio("submitted_requests/sentinel_kivifolio.csv")
            df = pd.read_csv(BytesIO(data), usecols=["strategy_name", "user"])
            sentinel_kivifolio_list = df["strategy_name"].to_list()
    except Exception as e:
        current_app.logger.error(
            f"Error in retrieving strategies from minio with error {e}"
        )
    available_dates_perf = get_available_historical_dates([strategy_name])
    available_dates_var = get_available_historical_dates(
        [strategy_name], suffix="var_report.html"
    )

    return render_template(
        "strat_add/expand_strat.html",
        strategy=strategy,
        test_mode_clusters=test_mode_clusters,
        live_mode_clusters=live_mode_clusters,
        backtest_result=backtest_result,
        retrieval_url=retrieval_url,
        title=titles,
        status=status,
        meta_data=meta_data,
        user_list=user_list,
        users=users,
        special_access=is_authentic_access,
        review_data=review_data,
        slip_graphs=slip_graphs,
        sentinel_kivifolio_list=sentinel_kivifolio_list,
        available_dates_perf=available_dates_perf[strategy_name],
        available_dates_var=available_dates_var[strategy_name],
    )


@strat_add.route("/strat_expand/add_user", methods=["POST"])
@swag_from(
    "/docs/strategy_detailing_and_expansion/add_user_access.yaml", methods=["POST"]
)
@login_required
def add_user_access():
    """Give a user access to a particular strategy.

    Raises:
        e: When strategy is not found
        Exception: When user is not found
    """
    strategy_name = request.form.get("strategy_name", None)
    username = request.form.get("username", None)
    try:
        strategy = Strategy.query.get_or_404(strategy_name)
    except Exception as e:
        current_app.logger.error(f"Strategy {strategy_name} not found")
        raise e
    user = User.query.filter_by(username=username).first()
    if user is None:
        current_app.logger.error(f"User {username} not found")
        raise Exception(f"User {username} not found")
    is_authentic = check_authenticity(
        strategy_list=[strategy_name],
        state_description=strategy.status.state_description.upper(),
    )
    if is_authentic is False:
        current_app.logger.warning(
            f"Unauthorized attempt to add user access to strategy {strategy_name} for user {username}"
        )
        abort(403)
    current_app.logger.info("Add user access page accessed")

    if user.username in [
        access.username
        for access in StrategyAccess.query.filter_by(
            strategy_name=strategy.strategy_name
        ).all()
    ]:
        current_app.logger.warning(
            f"User {user.username} already has access to strategy {strategy.strategy_name}"
        )
        return "success"
    new_access = StrategyAccess(
        strategy_name=strategy.strategy_name, username=user.username
    )
    db.session.add(new_access)
    db.session.commit()
    return "success"


@strat_add.route("/strat_expand/remove_user", methods=["POST"])
@swag_from(
    "/docs/strategy_detailing_and_expansion/remove_user_access.yaml", methods=["POST"]
)
@login_required
def remove_user_access():
    """Revoke user access to a particular strategy.

    Raises:
        e: When strategy name is not found
        Exception: When db entry is not found
    """
    strategy_name = request.form.get("strategy_name", None)
    username = request.form.get("username", None)
    try:
        strategy = Strategy.query.get_or_404(strategy_name)
    except Exception as e:
        current_app.logger.error(f"Strategy {strategy_name} not found")
        raise e
    is_authentic = check_authenticity(
        strategy_list=[strategy_name],
        state_description=strategy.status.state_description.upper(),
    )
    if is_authentic is False:
        current_app.logger.warning(
            f"Unauthorized attempt to revoke user access to strategy {strategy_name} for user {username}"
        )
        abort(403)
    strat_access = StrategyAccess.query.filter(
        StrategyAccess.strategy_name == strategy_name,
        StrategyAccess.username == username,
    ).first()
    if strat_access is None:
        current_app.logger.error(
            f"Strat access for strategy {strategy_name} for {username} not found"
        )
        return "failed"
    current_app.logger.info("Revoke user access page accessed")
    db.session.delete(strat_access)
    db.session.commit()
    return "success"


@strat_add.route("/strat_expand/add_todo", methods=["POST"])
@swag_from("/docs/strategy_detailing_and_expansion/add_todo.yaml", methods=["POST"])
@login_required
def add_todo():
    """Add a todo task for a given strategy.

    Raises:
        e: When strategy name is not found
    """
    strategy_name = request.form.get("strategy_name", None)
    todo_list = request.form.get("todo", None)
    if todo_list is not None:
        todo_list = todo_list.strip()
    try:
        strategy = Strategy.query.get_or_404(strategy_name)
    except Exception as e:
        current_app.logger.error(f"Strategy {strategy_name} not found")
        raise e
    is_authentic = check_authenticity(
        strategy_list=[strategy_name],
        state_description=strategy.status.state_description.upper(),
    )
    if (is_authentic is False) and (current_user.username != strategy.developer):
        current_app.logger.warning(
            f"Unauthorized attempt to add todo for strategy {strategy_name}"
        )
        abort(403)
    strategy_review = strategy.review.first()
    if strategy_review is None:
        strategy_review = StrategyReview(
            strategy_name=strategy_name,
            to_do=todo_list,
        )
        db.session.add(strategy_review)
    else:
        strategy_review.to_do = todo_list
    strategy_review = clean_review(review=strategy_review)
    if is_all_empty_review(review=strategy_review):
        db.session.delete(strategy_review)
    current_app.logger.info(f"Todo list for {strategy.strategy_name} updated")
    db.session.commit()
    return "success"


@strat_add.route("/strat_download", methods=["POST"])
@swag_from("/docs/strategy_management/download_strategy.yaml", methods=["POST"])
@login_required
def download_strategy():
    # Downloading a strategy.
    try:
        files = request.files.get("file")
        strategy_list = json.loads(request.form.get("strategy_list"))
        status = request.form.get("status")
        is_authentic = check_authenticity(
            strategy_list=strategy_list, state_description=status
        )
        if is_authentic is False:
            current_app.logger.warning(
                f"Unauthorized attempt to download strategy {strategy_list}"
            )
            return "failed", 400

        current_app.logger.info(f"Request to download strategy {strategy_list}")
        strategies_key_dict = {}
        for strategy_name in strategy_list:
            key, key_authentic = validate_download_request(
                private_key_file=files,
                strategy_name=strategy_name,
                status=status.lower(),
            )
            strategies_key_dict[strategy_name] = key
            if key_authentic is False:
                current_app.logger.warning(
                    f"Unauthorized attempt to download strategy {strategy_name}"
                )
                return "failed", 400
        current_app.logger.info(
            f"Validation for downloading strategy {strategy_list} completed"
        )
        memory_file = BytesIO()
        with ZipFile(memory_file, "w") as zip_file:
            for strategy_name, key in strategies_key_dict.items():
                for content in [
                    ".py",
                    ".ipynb",
                    "_MTM.log",
                    "_Tradelog.parquet",
                ]:
                    try:
                        if content in [".py", ".ipynb"]:
                            file = get_files_from_minio(
                                status=status.lower(),
                                strategy_name=strategy_name,
                                post_fix=f"_{content[1:]}",
                            )
                            file = key.decrypt(file).decode()
                        else:
                            file = get_files_from_minio(
                                status=status.lower(),
                                strategy_name=strategy_name,
                                post_fix=content,
                            )
                        zip_file.writestr(f"{strategy_name}{content}", file)
                    except Exception as e:
                        current_app.logger.info(
                            f"Skipping {content} for {strategy_name} with error {e}"
                        )
                        pass
        memory_file.seek(0)
        return send_file(
            memory_file,
            mimetype="application/octet-stream",
            as_attachment=False,
        )
    except Exception as e:
        current_app.logger.error(
            f"Downloading strategy {strategy_name} failed with error {e}"
        )
        return "failed", 400


@strat_add.route("/strat_expand/performance/<strategy_name>/<week>", methods=["GET"])
@swag_from(
    "/docs/strategy_detailing_and_expansion/performance_metrics.yaml", methods=["GET"]
)
@login_required
def performance_metrics(strategy_name: str, week: str):
    """View for displaying performance metrics.

    Args:
        strategy_name (str): strategy name
    """
    try:
        strategy = Strategy.query.get_or_404(strategy_name)
    except Exception as e:
        current_app.logger.error(f"Strategy {strategy_name} not found")
        raise e
    status = strategy.status.state_description.lower()
    is_authentic = check_authenticity(
        strategy_list=[strategy.strategy_name], state_description=status.upper()
    )
    is_authentic_access = check_access(strategy_list=[strategy.strategy_name])
    if (
        (is_authentic is False)
        and ((strategy.developer != current_user.username) or (status == "pending"))
        and (is_authentic_access is False)
    ):
        current_app.logger.warning(
            f"Unauthorized attempt to access performance metric page for strategy {strategy_name}"
        )
        abort(403)
    current_app.logger.info(
        f"Performance metric page accessed for {week} week and strategy {strategy_name}"
    )
    if week == "current":
        post_fix = "_backtest_results.html"
    else:
        week = convert_date_to_filename_format(week)
        post_fix = f"_{week}_backtest_results.html"
    try:
        performance_file = get_files_from_minio(
            status=status, strategy_name=strategy_name, post_fix=post_fix
        )
        performance_file = performance_file.decode()
    except Exception as e:
        current_app.logger.error(
            f"Got error {e} in request to fetch backtest results for {week} by user {current_user.username}"
        )
        performance_file = None

    try:
        removal_report = get_files_from_minio(
            status=status, strategy_name=strategy_name, post_fix="_removal_report.html"
        )
        removal_report = removal_report.decode()
    except:
        removal_report = None

    dead_trades = DeadSheet.get_strategy_dead_trades(strategy_name)

    pnl_curve_html = ""
    gandalf = pd.DataFrame()
    if len(dead_trades):
        gandalf = get_gandalf(
            dead_trades=dead_trades,
            exchange=strategy.exchange_name,
            segment=strategy.segment,
        )
        pnl_curve_html = get_live_pnl_curve(
            strategy_name=strategy_name,
            status=status,
            start_day=strategy.live_start_day,
            dead_trades=dead_trades,
            week=week,
        )

    return render_template(
        "strat_add/performance_metric.html",
        performance=performance_file,
        removal_report=removal_report,
        pnl_curve=pnl_curve_html,
        gandalf=gandalf,
    )


@strat_add.route("/portfolio_performance/<username>", methods=["GET"])
@swag_from(
    "/docs/strategy_detailing_and_expansion/portfolio_performance.yaml", methods=["GET"]
)
@login_required
def portfolio_performance_metrics(username):
    """
    View for displaying company's performance metrics.
    """
    condition1 = username == "company" and current_user.role.name != "ADMIN"
    condition2 = (
        username != current_user.username and current_user.role.name == "DEVELOPER"
    )
    condition3 = (
        username != current_user.username
        and username != "company"
        and username not in [x.username for x in current_user.developers]
        and current_user.role.name == "MANAGER"
    )
    if condition1 or condition2 or condition3:
        current_app.logger.warning(
            "Unauthorized attempt to access company's performance metric page"
        )
        abort(403)
    current_app.logger.info(
        f"{username} performance metric page accessed by {current_user.username}"
    )

    if username == "company":
        dead_trades = DeadSheet.get_all_dead_trades()
    else:
        userlist = [username]
        user = User.query.filter_by(username=username).first()
        if user is None:
            return f"{username} portfolio doesn't exist"
        userlist.extend([x.username for x in user.developers])
        dead_trades = DeadSheet.get_developer_dead_trades(userlist)
    if not dead_trades:
        return f"{username} portfolio doesn't exist"

    dead_trades_by_exchange = {}
    for trade in dead_trades:
        exchange = trade[1]
        trade_without_exchange = (trade[0], trade[2], trade[3])
        if exchange not in dead_trades_by_exchange:
            dead_trades_by_exchange[exchange] = []
        dead_trades_by_exchange[exchange].append(trade_without_exchange)

    gandalf_data = {}
    for exchange, trades in dead_trades_by_exchange.items():
        gandalf_data[exchange] = get_gandalf(dead_trades=trades, exchange=exchange)

    return render_template(
        "strat_add/performance_metric.html",
        performance="",
        removal_report="",
        pnl_curve="",
        gandalf_data=gandalf_data,
    )


@strat_add.route("/strat_expand/kivifolio_report/<strategy_name>", methods=["GET"])
@swag_from(
    "/docs/strategy_detailing_and_expansion/kivifolio_report.yaml", methods=["GET"]
)
@login_required
def kivifolio_report(strategy_name: str):
    """View for displaying kivifolio report.

    Args:
        strategy_name (str): strategy name
    """
    try:
        strategy = Strategy.query.get_or_404(strategy_name)
    except Exception as e:
        current_app.logger.error(f"Strategy {strategy_name} not found")
        raise e
    status = strategy.status.state_description.lower()
    is_authentic = check_authenticity(
        strategy_list=[strategy.strategy_name], state_description=status.upper()
    )
    is_authentic_access = check_access(strategy_list=[strategy.strategy_name])
    if (
        (is_authentic is False)
        and ((strategy.developer != current_user.username) or (status == "pending"))
        and (is_authentic_access is False)
    ):
        current_app.logger.warning(
            f"Unauthorized attempt to access kivifolio report page for strategy {strategy_name}"
        )
        abort(403)
    current_app.logger.info("Kivifolio report page accessed")
    report = get_files_from_minio(
        status=status, strategy_name=strategy_name, post_fix="_kivifolio_report.html"
    )
    report = report.decode()
    return render_template(
        "strat_add/report.html",
        report=report,
    )


@strat_add.route("/strat_expand/performance", methods=["POST"])
@swag_from(
    "/docs/strategy_detailing_and_expansion/show_performance_metrics.yaml",
    methods=["POST"],
)
@login_required
def show_performance_metrics():
    # Checking if performance metric exists.
    try:
        strategy_name = request.form["strategy"]
        week = request.form["week"]
        strategy = Strategy.query.filter_by(strategy_name=strategy_name).first()
        status = strategy.status.state_description.lower()
        minio_path = current_app.config["STRAT_STATUS_PATH_MAPPING"][status]
        is_authentic = check_authenticity(
            strategy_list=[strategy_name], state_description=status.upper()
        )
        is_authentic_access = check_access(strategy_list=[strategy_name])
        if (
            (is_authentic is False)
            and ((strategy.developer != current_user.username) or (status == "pending"))
            and (is_authentic_access is False)
        ):
            current_app.logger.warning(
                f"Unauthorized attempt to access performance metrics for strategy {strategy_name}"
            )
            abort(403)
        current_app.logger.info(
            f"Searching for {strategy_name} performance metric for {week} week  in {minio_path}"
        )
        if week == "current":
            post_fix = "_backtest_results.html"
        else:
            week = convert_date_to_filename_format(week)
            post_fix = f"_{week}_backtest_results.html"
        prefix = f"{minio_path}/{strategy_name}/{strategy_name}" + post_fix
        if check_object_exists(prefix=prefix) is True:
            return "success"
        return "failed"
    except Exception as e:
        current_app.logger.error(
            f"Performance metric checking failed for strategy {strategy_name} with error {e}"
        )
        return "failed"


@strat_add.route("/get_next_function_chart", methods=["POST"])
@swag_from(
    "/docs/strategy_detailing_and_expansion/get_next_function_chart.yaml",
    methods=["POST"],
)
@login_required
def get_next_function_chart():
    # Fetching next function chart from minio.
    try:
        strategy_name = request.form["strategy"]
        try:
            strategy = Strategy.query.get_or_404(strategy_name)
        except Exception as e:
            current_app.logger.error(f"Strategy {strategy_name} not found")
            raise e
        status = strategy.status.state_description.lower()
        is_authentic = check_authenticity(
            strategy_list=[strategy.strategy_name], state_description=status.upper()
        )
        is_authentic_access = check_access(strategy_list=[strategy.strategy_name])
        if (
            (is_authentic is False)
            and (strategy.developer != current_user.username)
            and (is_authentic_access is False)
        ):
            current_app.logger.warning(
                f"Unauthorized attempt to access next function profiler for strategy {strategy_name}"
            )
            abort(403)
        current_app.logger.info("Next function profiler requested")
        chart = get_files_from_minio(
            status=status, strategy_name=strategy_name, post_fix="_next_func_chart"
        )
        chart = chart.decode()
        return chart
    except Exception as e:
        current_app.logger.error(
            f"Next function profiler fetching for strategy {strategy_name} failed with error {e}"
        )
        return ""


@strat_add.route("/strategy_management", methods=["GET"])
@swag_from("/docs/strategy_management/strategy_manager.yaml", methods=["GET"])
@login_required
def strategy_manager():
    # View for fetching strategy management page.
    current_app.logger.info("Strategy management page accessed")
    live_strats = get_strategies(state_description="LIVE")
    live_strats.extend(get_accessible_strategies(state_description="LIVE"))
    strats_not_in_test_env = get_strats_not_in_test_env()
    rejected_strats = get_strategies(state_description="REJECTED")
    rejected_strats.extend(get_accessible_strategies(state_description="REJECTED"))
    dead_strats = get_strategies(state_description="DEAD")
    dead_strats.extend(get_accessible_strategies(state_description="DEAD"))
    live_strats_review_comments = get_review_comments(strat_list=live_strats)
    rejected_strats_review_comments = get_review_comments(strat_list=rejected_strats)
    dead_strats_review_comments = get_review_comments(strat_list=dead_strats)
    live_strat_with_dd = get_strategies_dd_percent(strat_list=live_strats)
    live_strat_with_dd = dict(
        sorted(live_strat_with_dd.items(), key=lambda x: x[1][0], reverse=True)
    )

    for key, value in live_strat_with_dd.items():
        if key.live_start_day is None:
            live_days = 0
        else:
            live_days = (datetime.datetime.now().date() - key.live_start_day).days
        value.append(live_days)
        live_strat_with_dd[key] = value

    dead_strats_with_dd = get_strategies_dd_percent(strat_list=dead_strats)
    dead_strats_with_dd = dict(
        sorted(dead_strats_with_dd.items(), key=lambda x: x[1][0], reverse=True)
    )

    notional = "quantity * executed_price"
    signal_slippage_bps = f"(sum({notional} * signal_slip) * 10000 / sum({notional}))"
    query_res_per_strategy = current_app.config["CLICKHOUSE_CLIENT"].execute(
        f"select strategy_name, {signal_slippage_bps}, sum({notional}) / 1e7 from slippage_trade group by strategy_name;"
    )
    query_res_per_slave = current_app.config["CLICKHOUSE_CLIENT"].execute(
        f"select slave_name, {signal_slippage_bps}, sum({notional}) / 1e7 from slippage_trade where slave_name!=strategy_name group by slave_name;"
    )
    slippage_strat_dict = {}
    for item in query_res_per_strategy:
        slippage_strat_dict[item[0]] = [item[1], item[2]]

    for item in query_res_per_slave:
        if item[0] in slippage_strat_dict:
            existing_slip = slippage_strat_dict[item[0]]
            final_slip = (existing_slip[0] * existing_slip[1] + item[1] * item[2]) / (
                existing_slip[1] + item[2]
            )
            slippage_strat_dict[item[0]] = [final_slip, existing_slip[1] + item[2]]
        else:
            slippage_strat_dict[item[0]] = [item[1], item[2]]

    try:
        dead_backtest_dict = {}
        rejected_backtest_dict = {}
        resetted_strats_live = get_resetted_strats(environment="LIVE")
        resetted_strats_test = get_resetted_strats(environment="TEST")
        if check_object_exists("submitted_requests/sentinel_dead_backtest.csv"):
            data = get_file_data_from_minio(
                "submitted_requests/sentinel_dead_backtest.csv"
            )
            df = pd.read_csv(BytesIO(data), usecols=["strategy_name", "user"])
            dead_backtest_dict = df.set_index("strategy_name").to_dict()["user"]
        if check_object_exists("submitted_requests/sentinel_rejected_backtest.csv"):
            data = get_file_data_from_minio(
                "submitted_requests/sentinel_rejected_backtest.csv"
            )
            df = pd.read_csv(BytesIO(data), usecols=["strategy_name", "user"])
            rejected_backtest_dict = df.set_index("strategy_name").to_dict()["user"]
    except Exception as e:
        current_app.logger.error(
            f"Error in retrieving strategies from minio with error {e}"
        )
    return render_template(
        "strat_add/strat_management.html",
        live_strats=live_strat_with_dd,
        live_strats_review_comments=live_strats_review_comments,
        rejected_strats=rejected_strats,
        rejected_strats_review_comments=rejected_strats_review_comments,
        dead_strats=dead_strats_with_dd,
        dead_strats_review_comments=dead_strats_review_comments,
        slippage_strat_dict=slippage_strat_dict,
        dead_backtest_dict=dead_backtest_dict,
        rejected_backtest_dict=rejected_backtest_dict,
        resetted_strats_live=resetted_strats_live,
        resetted_strats_test=resetted_strats_test,
        strats_not_in_test_env=strats_not_in_test_env,
    )


@strat_add.route("/live_performance_tracker", methods=["GET"])
@swag_from("/docs/strategy_management/live_performance_tracker.yaml", methods=["GET"])
@login_required
def live_performance_tracker():
    # View for fetching strategy management page.
    current_app.logger.info("Live performance tracker page accessed")
    live_strats = get_strategies(state_description="LIVE")
    live_strats.extend(get_accessible_strategies(state_description="LIVE"))

    meta_data_from_db = {
        meta_data.strategy_name: meta_data for meta_data in StrategyMetaData.query.all()
    }
    live_strat_info = {}
    for strat in live_strats:
        if strat.strategy_name in meta_data_from_db:
            if strat.live_start_day is None:
                live_days = 0
            else:
                live_days = (datetime.datetime.now().date() - strat.live_start_day).days
            meta_data = meta_data_from_db[strat.strategy_name]
            live_strat_info[strat] = {
                "avg_monthly_ret": meta_data.monthly_ret_live,
                "monthly_sr": meta_data.monthly_sharpe_live,
                "daily_sr": meta_data.daily_sharpe_live,
                "trading_days": meta_data.trading_days_live,
                "live_days": live_days,
                "max_dd": meta_data.max_dd_live,
                "ret_dd": meta_data.ret_dd_live,
            }

    return render_template(
        "strat_add/live_performance_tracker.html",
        live_strat_info=live_strat_info,
    )


@strat_add.route("/run_backtest", methods=["POST"])
@swag_from("/docs/strategy_management/run_backtest.yaml", methods=["POST"])
@login_required
def run_backtest():
    # View for requesting a new backtest.
    try:
        strategy_name = request.form["strategy"]
        service_index = int(request.form["service_index"])
        is_authentic = check_authenticity(strategy_list=[strategy_name])
        if is_authentic is False:
            current_app.logger.warning(
                f"Unauthorized access to run backtest option for strategy {strategy_name}"
            )
            abort(403)
        current_app.logger.info(f"Run Backtest requested for {strategy_name}")
        pending_backtest = PendingBacktests.query.filter_by(
            strategy_name=strategy_name
        ).first()
        if pending_backtest:
            if service_index >= len(pending_backtest.service_state):
                return "unavailable"
            if pending_backtest.service_state[service_index] == "0":
                return "already_submitted"
            if service_index in current_app.config["SERVICE_UPSTREAMS"]:
                for upstream_service in current_app.config["SERVICE_UPSTREAMS"][
                    service_index
                ]:
                    if pending_backtest.service_state[upstream_service] != "2":
                        return "upstream_error"
            current_state = pending_backtest.service_state
            new_state = start_downstream_service(service_index, current_state)
            pending_backtest.service_state = new_state
            pending_backtest.request_time = datetime.datetime.now()
        else:
            new_backtest = PendingBacktests(
                request_time=datetime.datetime.now(),
                strategy_name=strategy_name,
            )
            db.session.add(new_backtest)
        db.session.commit()
        current_app.logger.info(f"Adding new backtest for {strategy_name}")
        return "added"
    except Exception as e:
        current_app.logger.error(
            f"Run backtest request failed for {strategy_name} with error {e}"
        )
        return "failed"


@strat_add.route(
    "/strat_expand/cluster_performance/<strategy_name>/<cluster_mapping>",
    methods=["GET"],
)
@swag_from(
    "/docs/strategy_detailing_and_expansion/cluster_performance.yaml", methods=["GET"]
)
@login_required
def cluster_performance(strategy_name: str, cluster_mapping: str):
    """View for displaying cluster performance.

    Args:
        strategy_name (str): strategy name
        cluster_mapping (str): cluster mapping
    """
    try:
        strategy = Strategy.query.get_or_404(strategy_name)
    except Exception as e:
        current_app.logger.error(f"Strategy {strategy_name} not found")
        raise e
    is_authentic = check_authenticity(strategy_list=[strategy.strategy_name])
    is_authentic_access = check_access(strategy_list=[strategy.strategy_name])
    if (is_authentic is False) and (is_authentic_access is False):
        current_app.logger.warning(
            f"Unauthorized attempt to access cluster performance page for strategy {strategy_name}"
        )
        abort(403)
    current_app.logger.info("Clutser performance page accessed")
    try:
        pre_performance_file = get_files_from_minio(
            status="live",
            strategy_name=cluster_mapping,
            post_fix="_backtest_results.html",
        )
        pre_performance_file = pre_performance_file.decode()
    except Exception as e:
        current_app.logger.error(
            f"Strategy {strategy_name} pre cluster performance not found, error: {e}"
        )
        pre_performance_file = None

    post_performance_file = get_files_from_minio(
        status="pending",
        strategy_name=strategy_name,
        cluster_name=cluster_mapping,
        post_fix="_cluster_performance.html",
    )
    post_performance_file = post_performance_file.decode()
    return render_template(
        "strat_add/cluster_performance.html",
        pre_performance=pre_performance_file,
        post_performance=post_performance_file,
    )


@strat_add.route("/strat_expand/cluster_performance", methods=["POST"])
@swag_from(
    "/docs/strategy_detailing_and_expansion/show_cluster_performance.yaml",
    methods=["POST"],
)
@login_required
def show_cluster_performance():
    # View for checking if performance metric exists.
    try:
        strategy_name = request.form["strategy"]
        cluster_mapping = request.form["cluster"]
        try:
            strategy = Strategy.query.get_or_404(strategy_name)
        except Exception as e:
            current_app.logger.error(f"Strategy {strategy_name} not found")
            raise e
        is_authentic = check_authenticity(strategy_list=[strategy.strategy_name])
        is_authentic_access = check_access(strategy_list=[strategy.strategy_name])
        if (is_authentic is False) and (is_authentic_access is False):
            current_app.logger.warning(
                f"Unauthorized attempt to access cluster performance page for strategy {strategy_name}"
            )
            abort(403)
        current_app.logger.info(
            f"Searching for cluster performance files for strategy: {strategy_name} and cluster: {cluster_mapping}"
        )
        prefix = f"pending_strats/{strategy_name}/{strategy_name}_{cluster_mapping}_cluster_performance.html"
        if check_object_exists(prefix=prefix) is False:
            current_app.logger.info(
                f"Strategy {strategy_name}_{cluster_mapping} cluster performance not found"
            )
            return "failed"
        return "success"
    except Exception as e:
        current_app.logger.error(
            f"Cluster Performance checking failed for strategy {strategy_name} and {cluster_mapping} with error {e}"
        )
        return "failed"


@strat_add.route("/strat_expand/kivifolio_report", methods=["POST"])
@swag_from(
    "/docs/strategy_detailing_and_expansion/show_kivifolio_report.yaml",
    methods=["POST"],
)
@login_required
def show_kivifolio_report():
    # View for checking if kivifolio report exists.
    try:
        strategy_name = request.form["strategy"]
        try:
            strategy = Strategy.query.get_or_404(strategy_name)
        except Exception as e:
            current_app.logger.error(f"Strategy {strategy_name} not found")
            raise e
        status = strategy.status.state_description.lower()
        is_authentic = check_authenticity(
            strategy_list=[strategy.strategy_name], state_description=status.upper()
        )
        is_authentic_access = check_access(strategy_list=[strategy.strategy_name])
        if (
            (is_authentic is False)
            and ((strategy.developer != current_user.username) or (status == "pending"))
            and (is_authentic_access is False)
        ):
            current_app.logger.warning(
                f"Unauthorized attempt to access kivifolio report page for strategy {strategy_name}"
            )
            abort(403)
        minio_path = current_app.config["STRAT_STATUS_PATH_MAPPING"][status]
        current_app.logger.info(
            f"Searching for kivifolio report for {strategy_name} in {minio_path}"
        )
        prefix = f"{minio_path}/{strategy_name}/{strategy_name}_kivifolio_report.html"
        if check_object_exists(prefix=prefix) is False:
            current_app.logger.info(f"Kivifolio report for {strategy_name} not found")
            return "failed"
        return "success"
    except Exception as e:
        current_app.logger.error(
            f"Kivifolio report checking failed for strategy {strategy_name} with error {e}"
        )
        return "failed"


@strat_add.route("/make_strategy_dead", methods=["POST"])
@swag_from("/docs/strategy_management/make_strategy_dead.yaml", methods=["POST"])
@login_required
def make_strategy_dead():
    # View for making a live strategy dead.
    try:
        strategy_name = request.form["strategy"]
        try:
            strategy = Strategy.query.get_or_404(strategy_name)
        except Exception as e:
            current_app.logger.error(f"Strategy {strategy_name} not found")
            raise e
        is_authentic = check_authenticity(
            strategy_list=[strategy.strategy_name], state_description="LIVE"
        )
        if is_authentic is False:
            current_app.logger.warning(
                f"Unauthorized attempt to kill strategy {strategy_name}"
            )
            abort(403)
        current_app.logger.info(f"Request to kill {strategy_name}")
        clusters_list, msg_to_send = kill_strategy(strategy=strategy)
        db.session.commit()
        send_message_to_kafka(msg_to_send)
        return clusters_list
    except Exception as e:
        current_app.logger.error(
            f"Killing strategy {strategy_name} failed with error {e}"
        )
        return "failed"


@strat_add.route("/reset_strategy", methods=["POST"])
@swag_from("/docs/strategy_management/reset_strategy.yaml", methods=["POST"])
@login_required
def reset_strategy():
    """View for resetting a strategy in particular environment.
    This marks a strategy for it's pickles to be cleared and exits to be sent.
    """
    try:
        strategy_name = request.form["strategy"]
        environment = request.form["environment"]
        if environment.upper() not in ["LIVE", "TEST"]:
            current_app.logger.warning(
                f"Failed attempt to reset strategy {strategy_name} in invalid environment {environment} by {current_user.username}"
            )
            return "invalid environment"
        if "cluster" in strategy_name:
            return "Resetting/Unresetting not allowed for cluster strategies"
        try:
            strategy = Strategy.query.get_or_404(strategy_name)
        except Exception as e:
            current_app.logger.error(f"Strategy {strategy_name} not found")
            raise e
        if not StrategyClusterMapping.check_cluster_for_strategy(
            strategy_name=strategy_name, environment=environment.upper() + "_ENV"
        ):
            return "Resetting/Unresetting not allowed for top level strategies"
        if strategy.status.state_description not in ["LIVE", "TEST"]:
            return "Resetting/Unresetting allowed only for LIVE/TEST strategies"
        if (strategy.strategy_name in get_strats_not_in_test_env()) and (
            environment.upper() == "TEST"
        ):
            return "This Live strategy does not run in the test environment (New rework present in test environment)."
        is_authentic = (
            check_authenticity(
                strategy_list=[strategy.strategy_name], state_description="LIVE"
            )
        ) or (
            check_authenticity(
                strategy_list=[strategy.strategy_name], state_description="TEST"
            )
        )
        if (is_authentic is False) and (strategy.developer != current_user.username):
            current_app.logger.warning(
                f"Unauthorized attempt to reset strategy {strategy_name} in {environment} environment by {current_user.username}"
            )
            abort(403)

        action = request.form["action"]
        exchange_name = strategy.exchange_name.upper()
        file_path = f"submitted_requests/reworked_strategies_{environment.upper()}_{exchange_name}.csv"
        if check_object_exists(file_path):
            data = get_file_data_from_minio(file_path)
            df = pd.read_csv(BytesIO(data), header=None, dtype=str)
            df.columns = ["strat_name"]
            if df["strat_name"].iloc[0] != "1":
                current_app.logger.error(
                    f"{action} {strategy_name} at {pd.Timestamp.now()} by {current_user.username}"
                )
                return "Resetting/Unresetting strategy not allowed in pre-market"

            if action == "reset":
                if strategy_name in df["strat_name"].tolist()[1:]:
                    current_app.logger.error(
                        f"Invalid reset request for {strategy_name} in {environment} environment by {current_user.username}"
                    )
                    return "duplicate_strategy"
                current_app.logger.info(
                    f"Reset {strategy_name} in {environment} environment by {current_user.username}"
                )
                df = pd.concat(
                    [df, pd.DataFrame({"strat_name": [strategy_name]})],
                    ignore_index=True,
                )
            elif action == "unreset":
                if strategy_name not in df["strat_name"].tolist()[1:]:
                    current_app.logger.error(
                        f"Invalid Unreset request for {strategy_name} in {environment} environment by {current_user.username}"
                    )
                    return "empty_strategy"
                current_app.logger.info(
                    f"Unreset {strategy_name} in {environment} environment by {current_user.username}"
                )
                df = df[df["strat_name"] != strategy_name]
            else:
                return f"invalid/unrecognised action: {action}"
        else:
            current_app.logger.info(
                f"Reworked strategy file in {environment} environment does not exist for {exchange_name}, created a new file."
            )
            df = pd.DataFrame({"strat_name": ["1"]})
            if action == "reset":
                df = pd.concat(
                    [df, pd.DataFrame({"strat_name": [strategy_name]})],
                    ignore_index=True,
                )
        update_content_on_minio(
            path=file_path,
            content=df.to_csv(index=False, header=None),
        )
        return "success"
    except Exception as e:
        current_app.logger.error(
            f"Resetting strategy {strategy_name} failed with error {e}"
        )
        return "failed"


@strat_add.route("/review_strategy/<strategy_name>", methods=["GET", "POST"])
@swag_from("/docs/strategy_review/strategy_review_post.yaml", methods=["POST"])
@swag_from("/docs/strategy_review/strategy_review_get.yaml", methods=["GET"])
@login_required
def strategy_review(strategy_name: str):
    """View for fetching the strategy review form.

    Args:
        strategy_name (str): strategy name
    """
    form = StrategyReviewForm()
    try:
        strat = Strategy.query.get_or_404(strategy_name)
    except Exception as e:
        current_app.logger.error(f"Strategy {strategy_name} not found")
        raise e
    is_authorized = check_authenticity(
        strategy_list=[strategy_name], state_description="PENDING"
    ) or check_authenticity(strategy_list=[strategy_name], state_description="TEST")
    is_authentic_access = check_access(strategy_list=[strategy_name])
    if (is_authorized is False) and (is_authentic_access is False):
        current_app.logger.warning(
            f"Unauthorized attempt to access strategy review page for strategy {strategy_name}"
        )
        abort(403)
    status = strat.status.state_description.lower()
    strat_review = strat.review.first()
    if request.method == "GET":
        current_app.logger.info("Strategy review page accessed")
        if strat_review is not None:
            for field in form._fields.keys():
                try:
                    current_data = eval(f"strat_review.{field}")
                    if current_data is not None:
                        form[field].data = current_data
                except:
                    pass

    rework_start = ""
    if strat.reworked_strategy:
        strategy_reworked = Strategy.query.get_or_404(strat.reworked_strategy)
        rework_start = strategy_reworked.live_start_day

    # validate submitted strategy and insert into database
    if form.validate_on_submit():
        if strat_review is None:
            # add a new entry
            strat_review = StrategyReview(
                strategy_name=strategy_name,
                timecheck=form.timecheck.data,
                correlation_check=form.correlation_check.data,
                trade_distribution_check=form.trade_distribution_check.data,
                risk_analysis=form.risk_analysis.data,
                num_days_trading=form.num_days_trading.data,
                comments=form.comments.data,
                to_change=form.to_change.data,
            )
            strat_review = clean_review(review=strat_review)
            if is_all_empty_review(review=strat_review) is False:
                db.session.add(strat_review)
        else:
            strat_review.strategy_name = strategy_name
            strat_review.timecheck = form.timecheck.data
            strat_review.correlation_check = form.correlation_check.data
            strat_review.trade_distribution_check = form.trade_distribution_check.data
            strat_review.risk_analysis = form.risk_analysis.data
            strat_review.num_days_trading = form.num_days_trading.data
            strat_review.comments = form.comments.data
            strat_review.to_change = form.to_change.data
            strat_review = clean_review(review=strat_review)
            if is_all_empty_review(review=strat_review) is True:
                db.session.delete(strat_review)
        action = request.form["action"]

        current_app.logger.info(
            f"Strategy review submitted for {strategy_name} demanding action {action}"
        )

        if action in ["accept_live", "accept_test"]:
            # accept strategy to live/test mode
            strat_date = form.strat_date.data
            mode = action[7:]
            if is_authorized is False:
                current_app.logger.critical(
                    f"User {current_user.username} tried to accept strategy {strategy_name} in {mode} mode."
                )
                return render_template(
                    "strat_add/strategy_review.html",
                    form=form,
                    legend=f"Review Strategy: {strategy_name}",
                    strategy_name=strategy_name,
                    vis_success="hidden",
                    vis_fail="visible",
                    strats_info={},
                    action=action,
                    rework_start=rework_start,
                    status=status,
                )
            success, strats_info = accept_strategy(
                strategy=strategy_name,
                mode=mode,
                start_date=strat_date,
            )
            if success is False:
                return render_template(
                    "strat_add/strategy_review.html",
                    form=form,
                    legend=f"Review Strategy: {strategy_name}",
                    strategy_name=strategy_name,
                    vis_success="hidden",
                    vis_fail="visible",
                    strats_info={},
                    action=action,
                    rework_start=rework_start,
                    status=status,
                )
            if status == "pending":
                remove_backtests(strategy_name=strategy_name)
                remove_access(strategy_name=strategy_name)
            return render_template(
                "strat_add/strategy_review.html",
                form=form,
                legend=f"Review Strategy: {strategy_name}",
                strategy_name=strategy_name,
                vis_success="visible",
                vis_fail="hidden",
                strats_info=strats_info,
                action=action,
                rework_start=rework_start,
                status=status,
            )
        elif action in ["reject"]:
            # reject strategy
            if is_authorized is False:
                current_app.logger.critical(
                    f"User {current_user.username} tried to reject strategy {strategy_name}"
                )
                return render_template(
                    "strat_add/strategy_review.html",
                    form=form,
                    legend=f"Review Strategy: {strategy_name}",
                    strategy_name=strategy_name,
                    vis_success="hidden",
                    vis_fail="visible",
                    strats_info={},
                    action=action,
                    rework_start=rework_start,
                    status=status,
                )
            success = reject_strategy(strategy=strategy_name)
            if success is False:
                return render_template(
                    "strat_add/strategy_review.html",
                    form=form,
                    legend=f"Review Strategy: {strategy_name}",
                    strategy_name=strategy_name,
                    vis_success="hidden",
                    vis_fail="visible",
                    strats_info={},
                    action=action,
                    rework_start=rework_start,
                    status=status,
                )
            if status == "pending":
                remove_backtests(strategy_name=strategy_name)
                remove_access(strategy_name=strategy_name)
            return render_template(
                "strat_add/strategy_review.html",
                form=form,
                legend=f"Review Strategy: {strategy_name}",
                strategy_name=strategy_name,
                vis_success="visible",
                vis_fail="hidden",
                strats_info={},
                action=action,
                rework_start=rework_start,
                status=status,
            )
        else:
            # save reviews
            db.session.commit()
            return render_template(
                "strat_add/strategy_review.html",
                form=form,
                legend=f"Review Strategy: {strategy_name}",
                strategy_name=strategy_name,
                vis_success="visible",
                vis_fail="hidden",
                strats_info={},
                action=action,
                rework_start=rework_start,
                status=status,
            )
    return render_template(
        "strat_add/strategy_review.html",
        form=form,
        legend=f"Review Strategy: {strategy_name}",
        strategy_name=strategy_name,
        vis_success="hidden",
        vis_fail="hidden",
        strats_info={},
        action="",
        rework_start=rework_start,
        status=status,
    )


@strat_add.route("/show_review_strategy/<strategy_name>", methods=["GET"])
@swag_from("/docs/strategy_review/show_strategy_review.yaml", methods=["GET"])
@login_required
def show_strategy_review(strategy_name: str):
    """View for displaying strategy review.

    Args:
        strategy_name (str): strategy name
    """
    try:
        strat = Strategy.query.get_or_404(strategy_name)
    except Exception as e:
        current_app.logger.error(f"Strategy {strategy_name} not found")
        raise e
    status = strat.status.state_description.lower()
    is_authorized = check_authenticity(
        strategy_list=[strategy_name], state_description=status.upper()
    )
    if (is_authorized == False) and (strat.developer != current_user.username):
        current_app.logger.warning(
            f"Unauthorized attempt to access display strategy review page for strategy {strategy_name}"
        )
        abort(403)
    current_app.logger.info("Strategy review display page accessed")
    strat_review = strat.review.first()

    return render_template(
        "strat_add/strategy_review_display.html",
        strategy_review=strat_review,
    )


@strat_add.route("/comments_dashboard", methods=["GET"])
@swag_from("/docs/strategy_review/comments_dashboard.yaml", methods=["GET"])
@login_required
def comments_dashboard():
    # View for displaying comments dashboard.
    is_authorized = check_authenticity(strategy_list=[])
    review_data = {}
    current_app.logger.info("Comments dashboard accessed")
    strategies = []
    if is_authorized is True:
        strategies = get_strategies(state_description="PENDING")
        strategies.extend(get_strategies(state_description="TEST"))
    strategies.extend(get_accessible_strategies(state_description="PENDING"))
    strategies.extend(get_accessible_strategies(state_description="TEST"))
    strategies = list(set(strategies))
    strategies.sort(key=lambda x: x.submission_day)
    for strategy in strategies:
        review_data[strategy.strategy_name] = strategy.review.first()
    return render_template("strat_add/comments_dashboard.html", review_data=review_data)


@strat_add.route("/pending_strats", methods=["GET"])
@swag_from(
    "/docs/strategy_addition_modification_and_deletion/pending_strats_get.yaml",
    methods=["GET"],
)
@login_required
def pending_strategy_page():
    current_app.logger.info("Pending Strats page accessed")
    pending_strats = (
        Status.query.filter_by(state_description="PENDING").first().strategies.all()
    )
    pending_strats = [
        strat for strat in pending_strats if strat.developer == current_user.username
    ]
    test_strats = (
        Status.query.filter_by(state_description="TEST").first().strategies.all()
    )
    test_strats = [
        strat for strat in test_strats if strat.developer == current_user.username
    ]
    resetted_strats = get_resetted_strats(environment="TEST")
    review_comments = get_review_comments(strat_list=pending_strats + test_strats)
    return render_template(
        "strat_add/pending_strats.html",
        submitted_strategies=pending_strats,
        test_strategies=test_strats,
        review_comments=review_comments,
        resetted_strats=resetted_strats,
    )


@strat_add.route("/strat_home", methods=["GET", "POST"])
@swag_from("/docs/strategy_detailing_and_expansion/strategy_home.yaml", methods=["GET"])
@login_required
def strategy_home():
    user_list = []
    if current_user.role.name == "ADMIN":
        user_list.append("company")
        user_list.extend([user.username for user in User.query.all()])
    else:
        user_list.extend([user.username for user in current_user.developers])
    new_areas_to_work = get_all_md_files(
        path="new_areas_to_work/new_areas_to_work.csv", folder_name="new_areas_to_work"
    )

    for key in new_areas_to_work:
        path = os.path.dirname(key)
        attachments = list_files_in_folder(path)
        html_attachments = [
            os.path.basename(file) for file in attachments if file.endswith(".html")
        ]
        new_areas_to_work[key].append(html_attachments)

    if request.method == "POST":
        if current_user.role.name != "ADMIN":
            current_app.logger.warning(
                "Unauthorized attempt to modify current areas to work"
            )
            abort(403)
        path = request.form["path"]
        content = request.form["content"]
        title = request.form["title"]
        description = request.form["description"]
        if check_object_exists("new_areas_to_work/new_areas_to_work.csv"):
            data = get_file_data_from_minio("new_areas_to_work/new_areas_to_work.csv")
            df = pd.read_csv(BytesIO(data), index_col=0)
        else:
            df = pd.DataFrame(columns=["index", "path", "title", "description"])
            df = df.set_index("index")

        if path != "" and len(df) > 0 and path in df["path"].values:
            df.loc[df.path == path, "title"] = title
            df.loc[df.path == path, "description"] = description
        else:
            index = df.index.max() + 1 if len(df) else 0
            path = f"new_areas_to_work/title{index}/title{index}.md"
            new_row = pd.DataFrame(
                {
                    "index": [index],
                    "path": [path],
                    "title": [title],
                    "description": [description],
                }
            )
            new_row.set_index("index", inplace=True)
            df = pd.concat([df, new_row], sort=False)
        update_content_on_minio(path=path, content=content)
        upload_dataframe_to_minio(
            path="new_areas_to_work/new_areas_to_work.csv", dataframe=df
        )
        current_app.logger.info(f"Successfully updated the idea {title}")

    return render_template(
        "strat_add/strat_home.html",
        user_list=user_list,
        current_username="company",
        new_areas_to_work=new_areas_to_work,
    )


@strat_add.route("/todo_dashboard/<status>", methods=["GET"])
@swag_from("/docs/strategy_review/todo_dashboard.yaml", methods=["GET"])
@login_required
def todo_dashboard(status):
    # View for displaying the todo dashboard.
    strategy_list = get_strategies(state_description=status.upper())
    todo_list = {}
    strategy_review_list = [
        review.strategy_name
        for review in StrategyReview.query.filter(
            StrategyReview.to_do.__ne__(None) & StrategyReview.to_do.__ne__("")
        ).all()
    ]
    for strategy in strategy_list:
        if strategy.strategy_name in strategy_review_list:
            todo_list[strategy] = strategy.review.first().to_do
    return render_template(
        "strat_add/todo_dashboard.html",
        todo_list=todo_list,
    )


@strat_add.route("/add_to_sentinel", methods=["POST"])
@swag_from("/docs/strategy_management/add_to_sentinel.yaml", methods=["POST"])
@login_required
def sentinel_backtest():
    # View to add to sentinel backtest
    try:
        strategy_name = request.form["strategy"]
        service = request.form["service"]
        action = request.form["action"]
        try:
            strategy = Strategy.query.get_or_404(strategy_name)
        except Exception as e:
            current_app.logger.error(f"Strategy {strategy_name} not found")
            raise e
        status = strategy.status.state_description.lower()
        status_dict = {
            "kivifolio": "live",
            "dead_backtest": "dead",
            "rejected_backtest": "rejected",
        }
        if service in status_dict:
            if status != status_dict[service]:
                current_app.logger.error(
                    f"Attempt to add {status} {strategy_name} to sentinel {service} by {current_user.username}"
                )
                return "status_failed"
            if service == "kivifolio":
                is_authentic = check_authenticity(
                    strategy_list=[strategy.strategy_name],
                    state_description=status.upper(),
                )
                if is_authentic is False:
                    current_app.logger.warning(
                        f"Unauthorized attempt to sentinel {service} for {strategy_name} by {current_user.username}"
                    )
                    abort(403)
                if "cluster" not in strategy_name:
                    current_app.logger.error(
                        f"Attempt to add {strategy_name} to sentinel {service} by {current_user.username}"
                    )
                    return "invalid_strategy"
            elif service in ["dead_backtest", "rejected_backtest"]:
                is_authentic = check_authenticity(
                    strategy_list=[strategy.strategy_name],
                    state_description=status.upper(),
                )
                if (is_authentic is False) and (
                    strategy.developer != current_user.username
                ):
                    current_app.logger.warning(
                        f"Unauthorized attempt to sentinel {service} for {strategy_name} by {current_user.username}"
                    )
                    abort(403)
                if "cluster" in strategy_name:
                    current_app.logger.error(
                        f"Attempt to add {strategy_name} to sentinel {service} by {current_user.username}"
                    )
                    return "invalid_strategy"
        else:
            current_app.logger.warning(
                f"Unauthorized attempt to sentinel {service} for {strategy_name} by {current_user.username}"
            )
            abort(403)
        if check_object_exists(f"submitted_requests/sentinel_{service}.csv"):
            data = get_file_data_from_minio(
                f"submitted_requests/sentinel_{service}.csv"
            )
            df = pd.read_csv(BytesIO(data), usecols=["strategy_name", "user"])
            if action == "Add":
                if strategy_name in df["strategy_name"].tolist():
                    current_app.logger.error(
                        f"Invalid add {service} request for {strategy_name} by {current_user.username}"
                    )
                    return "duplicate_strategy"
                if current_user.role.name == "DEVELOPER":
                    df_user = df[df["user"] == current_user.username]
                    if len(df_user) >= 5:
                        current_app.logger.error(
                            f"Attempt to add more than 5 strategies to sentinel {service} by {current_user.username}"
                        )
                        return "exceeded_limit"
                current_app.logger.info(
                    f"Sentinel {service} added for {strategy_name} by {current_user.username}"
                )
                new_row = pd.DataFrame(
                    {"strategy_name": [strategy_name], "user": [current_user.username]}
                )
                df = pd.concat([df, new_row], ignore_index=True)
            elif action == "Delete":
                if strategy_name not in df["strategy_name"].tolist():
                    current_app.logger.error(
                        f"Invalid Delete {service} request {strategy_name} by {current_user.username}"
                    )
                    return "empty_strategy"
                current_app.logger.info(
                    f"Sentinel {service} removed for {strategy_name} by {current_user.username}"
                )
                index = df[df["strategy_name"] == strategy_name].index[0]
                df.drop(index, inplace=True)
            else:
                current_app.logger.warning(
                    f"Attempt to perform invalid {action} on {service} by {current_user.username}"
                )
                abort(403)
        else:
            if action == "Add":
                df = pd.DataFrame(
                    {"strategy_name": [strategy_name], "user": [current_user.username]}
                )
            elif action == "Delete":
                current_app.logger.info(
                    f"Invalid Delete {service} request for {strategy_name} by {current_user.username}"
                )
                return "failed"
            else:
                current_app.logger.warning(
                    f"Attempt to perform invalid {action} on {service} by {current_user.username}"
                )
                abort(403)

        upload_dataframe_to_minio(
            path=f"submitted_requests/sentinel_{service}.csv", dataframe=df
        )
        return "success"
    except Exception as e:
        current_app.logger.error(
            f"Request to sentinel {service} failed on {action} for strategy {strategy_name} by {current_user.username} with error {e}"
        )
        return "failed"


@strat_add.route("/post_cluster_backtest", methods=["POST"])
@swag_from(
    "/docs/strategy_management/post_cluster_backtest.yaml",
    methods=["POST"],
)
@login_required
def post_cluster_backtest():
    form = ClusterBacktestForm()
    cluster = form.cluster.data
    try:
        if form.validate_on_submit():
            if form.slaves.data == "":
                flash("Please select atleast one slave", "error")
                raise ValidationError("Invalid Form Data: Empty Slaves")
            strategy = Strategy.query.get_or_404(cluster)
            is_authentic = check_authenticity(
                strategy_list=[cluster],
                state_description="LIVE",
            )
            is_authentic_access = check_access(strategy_list=[cluster])

            if (
                (is_authentic is False)
                and (is_authentic_access is False)
                and (strategy.developer != current_user.username)
            ):
                current_app.logger.warning(
                    f"Unauthorized attempt to add cluster backtest for {cluster} by {current_user.username}"
                )
                abort(403)
            if check_object_exists("submitted_requests/cluster_backtest.csv"):
                data = get_file_data_from_minio(
                    "submitted_requests/cluster_backtest.csv"
                )
                df = pd.read_csv(BytesIO(data), index_col=0)

                if (
                    len(df[(df.cluster == cluster) & (df.slaves == form.slaves.data)])
                    == 0
                ):
                    current_app.logger.info(
                        f"Cluster Backtest added for {cluster} by {current_user.username}"
                    )
                    index = df.index.max() + 1 if len(df) else 0
                    new_row = pd.DataFrame(
                        {
                            "index": [index],
                            "user": [current_user.username],
                            "cluster": [cluster],
                            "slaves": [form.slaves.data],
                            "state": [0],
                        }
                    )
                    new_row.set_index("index", inplace=True)
                    df = pd.concat([df, new_row], sort=False)
                elif form.is_restart.data:
                    if (
                        len(
                            df[
                                (df.cluster == cluster)
                                & (df.slaves == form.slaves.data)
                                & (df.state == 1)
                            ]
                        )
                        == 1
                    ):
                        index = df[
                            (df.cluster == cluster) & (df.slaves == form.slaves.data)
                        ].index[0]
                        df.loc[index, "state"] = 0
                    else:
                        return "failed"
                else:
                    flash(
                        "Cluster and slave combination already added to backtest",
                        "error",
                    )
                    raise ValidationError(
                        f"Invalid Form Data: Duplicate Cluster {cluster} and slaves {form.slaves.data} combination"
                    )
            else:
                df = pd.DataFrame(
                    {
                        "index": [0],
                        "user": [current_user.username],
                        "cluster": [cluster],
                        "slaves": [form.slaves.data],
                        "state": [0],
                    }
                )
                df.set_index("index", inplace=True)
            upload_dataframe_to_minio(
                path="submitted_requests/cluster_backtest.csv", dataframe=df
            )
        else:
            flash(
                "Invalid Form Data. Both cluster and slaves fields are mandatory.",
                "error",
            )
            raise ValidationError("Invalid Form Data")
    except Exception as e:
        current_app.logger.error(
            f"Error '{e}' occured while submitting cluster backtest by {current_user.username}"
        )
    return redirect(url_for("strat_add.get_cluster_backtest"))


@strat_add.route("/get_cluster_backtest", methods=["GET"])
@swag_from(
    "/docs/strategy_management/get_cluster_backtest.yaml",
    methods=["GET"],
)
@login_required
def get_cluster_backtest():
    form = ClusterBacktestForm()
    accessible_clusters = get_strategies(state_description="LIVE")
    accessible_clusters.extend(get_accessible_strategies(state_description="LIVE"))
    accessible_clusters_name = list(
        set(
            [
                strat.strategy_name
                for strat in accessible_clusters
                if "cluster" in strat.strategy_name
            ]
        )
    )
    form.cluster.choices = accessible_clusters_name

    current_app.logger.info("Cluster backtest page accessed")

    try:
        cluster_backtest_dict = []
        if check_object_exists("submitted_requests/cluster_backtest.csv"):
            data = get_file_data_from_minio("submitted_requests/cluster_backtest.csv")
            df = pd.read_csv(BytesIO(data), index_col=0)
            df = df[df["cluster"].isin(accessible_clusters_name)]
            cluster_backtest_dict = df.to_dict(orient="records")

    except Exception as e:
        current_app.logger.error(
            f"Error in retrieving backtest clusters from minio with error '{e}'"
        )

    return render_template(
        "strat_add/cluster_backtest.html",
        form=form,
        cluster_backtest_dict=cluster_backtest_dict,
    )


@strat_add.route("/delete_cluster_backtest", methods=["POST"])
@swag_from(
    "/docs/strategy_management/delete_cluster_backtest.yaml",
    methods=["POST"],
)
@login_required
def delete_cluster_backtest():
    cluster = request.form["cluster"]
    slaves = request.form["slaves"]
    try:
        strategy = Strategy.query.get_or_404(cluster)
        is_authentic = check_authenticity(
            strategy_list=[cluster],
            state_description="LIVE",
        )
        is_authentic_access = check_access(strategy_list=[cluster])
        if (
            (is_authentic is False)
            and (is_authentic_access is False)
            and (strategy.developer != current_user.username)
        ):
            current_app.logger.warning(
                f"Unauthorized attempt to delete cluster backtest for {cluster} by {current_user.username}"
            )
            abort(403)
        if check_object_exists("submitted_requests/cluster_backtest.csv"):
            data = get_file_data_from_minio("submitted_requests/cluster_backtest.csv")
            df = pd.read_csv(BytesIO(data), index_col=0)

            if len(df[(df.cluster == cluster) & (df.slaves == slaves)]):
                current_app.logger.info(
                    f"Cluster backtest removed for {cluster} by {current_user.username}"
                )
                index = df[(df.cluster == cluster) & (df.slaves == slaves)].index[0]
                df = df[(df.cluster != cluster) | (df.slaves != slaves)]
            else:
                current_app.logger.error(
                    f"Invalid delete cluster backtest request for {cluster} and slaves {slaves} by {current_user.username}"
                )
                return "empty_cluster"
        else:
            current_app.logger.info(
                f"File does not exist at minio. Invalid deletion request for {cluster} by {current_user.username}. "
            )
            return "failed"

        upload_dataframe_to_minio(
            path="submitted_requests/cluster_backtest.csv", dataframe=df
        )

        if check_object_exists(
            f"custom_cluster_backtest/{cluster}_{index}_backtest_results.html"
        ):
            delete_file_from_minio(
                path=f"custom_cluster_backtest/{cluster}_{index}_backtest_results.html"
            )
        return "success"
    except Exception as e:
        current_app.logger.error(
            f"Error '{e}' occured while deleting cluster backtest for {cluster} by {current_user.username}"
        )
        return "failed"


@strat_add.route("/get_slaves", methods=["POST"])
@swag_from(
    "/docs/strategy_management/get_slaves.yaml",
    methods=["POST"],
)
@login_required
def get_slaves():
    cluster = request.form["cluster"]
    try:
        strategy = Strategy.query.get_or_404(cluster)
        is_authentic = check_authenticity(
            strategy_list=[cluster],
            state_description="LIVE",
        )
        is_authentic_access = check_access(strategy_list=[cluster])
        if (
            (is_authentic is False)
            and (is_authentic_access is False)
            and (strategy.developer != current_user.username)
        ):
            current_app.logger.warning(
                f"Unauthorized attempt to get slaves for {cluster} by {current_user.username}"
            )
            abort(403)
        current_app.logger.info(
            f"Slave request for {cluster} by {current_user.username}"
        )
        slave_strats_obj = StrategyClusterMapping.query.filter_by(
            cluster_name=cluster,
            mapping_status=ClusterMappingStatus.get_status(description="LIVE_ENV"),
        ).all()
        accessible_live_strats = [
            strat.strategy_name for strat in get_strategies(state_description="LIVE")
        ]
        accessible_live_strats.extend(
            [
                strat.strategy_name
                for strat in get_accessible_strategies(state_description="LIVE")
            ]
        )
        slaves = [
            slave.strategy_name
            for slave in slave_strats_obj
            if slave.strategy_name in accessible_live_strats
        ]
        slaves = list(set(slaves))
        return slaves
    except Exception as e:
        current_app.logger.error(
            f"Error '{e}' occured while fetching slaves for {cluster} by {current_user.username}"
        )
        return "failed"


@strat_add.route("/custom_cluster_performance/<cluster_name>/<slaves>", methods=["GET"])
@swag_from(
    "/docs/strategy_detailing_and_expansion/cluster_performance_metrics.yaml",
    methods=["GET"],
)
@login_required
def custom_cluster_performance_metrics(cluster_name: str, slaves: str):
    """View for seeing cluster performance.

    Args:
        cluster_name (str): cluster name
    """
    try:
        cluster = Strategy.query.get_or_404(cluster_name)
    except Exception as e:
        current_app.logger.error(f"Strategy {cluster_name} not found")
        raise e
    status = cluster.status.state_description.lower()
    is_authentic = check_authenticity(
        strategy_list=[cluster.strategy_name], state_description=status.upper()
    )
    is_authentic_access = check_access(strategy_list=[cluster.strategy_name])
    if (
        (is_authentic is False)
        and (is_authentic_access is False)
        and ((cluster.developer != current_user.username))
    ):
        current_app.logger.warning(
            f"Unauthorized attempt to access performance metric page for cluster {cluster_name}"
        )
        abort(403)
    if status != "live" or "cluster" not in cluster_name:
        current_app.logger.error(
            f"Cluster performance metrics file accessed for {status} {cluster_name} by {current_user.username}"
        )
        raise ValueError("Invalid Cluster")
    current_app.logger.info("Cluster performance metric page accessed")

    if check_object_exists("submitted_requests/cluster_backtest.csv"):
        data = get_file_data_from_minio("submitted_requests/cluster_backtest.csv")
        df = pd.read_csv(BytesIO(data), index_col=0)
        if len(df[(df.cluster == cluster_name) & (df.slaves == slaves)]) > 0:
            index = df[(df.cluster == cluster_name) & (df.slaves == slaves)].index[0]
        else:
            raise ValueError(
                f"Invalid {cluster_name} and {slaves} combination for custom cluster performance metrics"
            )
    if check_object_exists(
        f"custom_cluster_backtest/{cluster_name}_{index}_backtest_results.html"
    ):
        performance_file = get_file_data_from_minio(
            f"custom_cluster_backtest/{cluster_name}_{index}_backtest_results.html"
        )
        performance_file = performance_file.decode()
        return render_template(
            "strat_add/performance_metric.html",
            performance=performance_file,
            removal_report="",
            pnl_curve="",
        )
    else:
        flash(
            "Backtest results don't exist",
            "error",
        )
        return redirect(url_for("strat_add.get_cluster_backtest"))


@strat_add.route("/manage_cluster_mapping", methods=["GET", "POST"])
@swag_from(
    "/docs/manage_cluster_mapping/manage_cluster_mapping_get.yaml", methods=["GET"]
)
@swag_from(
    "/docs/manage_cluster_mapping/manage_cluster_mapping_post.yaml", methods=["POST"]
)
@login_required
def manage_cluster_mapping_page():
    """View for manage cluster mapping page. Helps change the cluster mapping for live / test strategy in db

    Raises:
        e: Raises exception if unauthorized attempt is made to access the page

    """
    # User role must be manager or admin only.
    is_authorized = check_authenticity(strategy_list=[])
    if not is_authorized:
        current_app.logger.warning(
            "Unauthorized attempt to access change cluster mapping page"
        )
        abort(403)
    current_app.logger.info("Change Cluster mapping page accessed")

    form = ChangeClusterMappingForm()
    cluster_choice_list = [
        strategy.strategy_name
        for strategy in Strategy.query.filter(
            Strategy.strategy_name.contains("cluster")
        ).all()
    ]
    form.cluster_mapping.choices = cluster_choice_list
    live_strats = get_strategies(state_description="LIVE")
    live_strats = [strat.strategy_name for strat in live_strats]
    form.strategies.choices = live_strats
    test_strats = get_strategies(state_description="TEST")
    test_strats = [strat.strategy_name for strat in test_strats]
    test_strats = sorted(test_strats + live_strats)

    if form.validate_on_submit():
        mappingStatus = request.form.get("ENV")
        selected_strategy = form.strategies.data
        selected_clusters = form.cluster_mapping.data
        if mappingStatus == "TEST_ENV":
            is_authorized = check_authenticity(
                strategy_list=[selected_strategy], state_description="TEST"
            ) or check_authenticity(
                strategy_list=[selected_strategy], state_description="LIVE"
            )
        else:
            is_authorized = check_authenticity(
                strategy_list=[selected_strategy], state_description="LIVE"
            )
        is_authorized_clusters = check_cluster_authenticity(
            cluster_list=selected_clusters
        )
        if is_authorized is False or is_authorized_clusters is False:
            current_app.logger.warning(
                f"Unauthorized attempt to change cluster mapping {selected_strategy}"
            )
            abort(403)

        strategy = Strategy.query.get_or_404(selected_strategy)
        for cluster in strategy.cluster_mapping:
            if cluster.mapping_status == ClusterMappingStatus.get_status(
                description=mappingStatus
            ):
                db.session.delete(cluster)
        for cluster in selected_clusters:
            strategy.cluster_mapping.append(
                StrategyClusterMapping(
                    cluster_name=cluster,
                    strategy_name=selected_strategy,
                    mapping_status=ClusterMappingStatus.get_status(
                        description=mappingStatus
                    ),
                )
            )
        db.session.commit()
        current_app.logger.info(
            f"Strategy {selected_strategy} modified in {mappingStatus[:4].lower()} with clusters {selected_clusters}"
        )
        flash(
            f"Strategy's {mappingStatus[:4].lower()} mode cluster mapping modified successfully",
            "success",
        )

    form.strategies.data = ""
    form.cluster_mapping.data = []
    return render_template(
        "strat_add/manage_cluster_mapping.html",
        form=form,
        live_strategies=live_strats,
        test_strategies=test_strats,
    )


@strat_add.route("/get_current_clusters", methods=["POST"])
@login_required
def get_current_clusters():
    """Returns the clusters that are mapped to strategy

    Raises:
        e: Raises exception if strategy name not found

    Returns:
       list: list of mapped clusters
    """
    strategy_name = request.form["selected_strategy"]
    mapping_state = request.form["mapping_state"]
    if mapping_state == "TEST_ENV":
        is_authorized = check_authenticity(
            strategy_list=[strategy_name], state_description="TEST"
        ) or check_authenticity(strategy_list=[strategy_name], state_description="LIVE")
    else:
        is_authorized = check_authenticity(
            strategy_list=[strategy_name], state_description="LIVE"
        )
    if not is_authorized:
        current_app.logger.warning(
            f"Unauthorized attempt to access cluster mapping for {strategy_name}"
        )
        abort(403)

    strategy = Strategy.query.get_or_404(strategy_name)
    clusters = [
        cluster.cluster_name
        for cluster in strategy.cluster_mapping
        if cluster.mapping_status
        == ClusterMappingStatus.get_status(description=mapping_state)
    ]
    return clusters


@strat_add.route("/download_custom_cluster_backtest", methods=["POST"])
@swag_from(
    "/docs/strategy_management/download_custom_cluster_backtest.yaml", methods=["POST"]
)
@login_required
def download_custom_cluster_backtest():
    """View for seeing cluster performance.

    Args:
        cluster_name (str): cluster name
    """
    cluster_name = request.form["cluster"]
    slaves = request.form["slaves"]
    try:
        cluster = Strategy.query.get_or_404(cluster_name)
    except Exception as e:
        current_app.logger.error(f"Strategy {cluster_name} not found")
        raise e
    status = cluster.status.state_description.lower()
    is_authentic = check_authenticity(
        strategy_list=[cluster.strategy_name], state_description=status.upper()
    )
    is_authentic_access = check_access(strategy_list=[cluster.strategy_name])
    if (
        (is_authentic is False)
        and (is_authentic_access is False)
        and ((cluster.developer != current_user.username))
    ):
        current_app.logger.warning(
            f"Unauthorized attempt to download performance metrics for cluster {cluster_name}"
        )
        abort(403)
    if status != "live" or "cluster" not in cluster_name:
        current_app.logger.error(
            f"Attempt to download Cluster performance metrics for {status} {cluster_name} by {current_user.username}"
        )
        raise ValueError("Invalid Cluster")

    if check_object_exists("submitted_requests/cluster_backtest.csv"):
        data = get_file_data_from_minio("submitted_requests/cluster_backtest.csv")
        df = pd.read_csv(BytesIO(data), index_col=0)
        if len(df[(df.cluster == cluster_name) & (df.slaves == slaves)]) > 0:
            index = df[(df.cluster == cluster_name) & (df.slaves == slaves)].index[0]
        else:
            raise ValueError(
                f"Invalid {cluster_name} and {slaves} combination for custom cluster performance metrics"
            )
    else:
        raise ValueError(
            "No requests for backtest found. Please submit backtest request"
        )

    current_app.logger.info(f"Downloading cluster {cluster_name} performance")
    atleast_one_file_exist = False
    memory_file = BytesIO()
    try:
        with ZipFile(memory_file, "w") as zip_file:
            for content in ["_MTM.log", "_Tradelog.parquet"]:
                if check_object_exists(
                    f"custom_cluster_backtest/{cluster_name}_{index}{content}"
                ):
                    file = get_file_data_from_minio(
                        f"custom_cluster_backtest/{cluster_name}_{index}{content}"
                    )
                    zip_file.writestr(f"{cluster_name}_{index}{content}", file)
                    atleast_one_file_exist = True
        if atleast_one_file_exist:
            memory_file.seek(0)
            return send_file(
                memory_file,
                mimetype="application/octet-stream",
                as_attachment=False,
            )
        else:
            return "failed", 400
    except Exception as e:
        current_app.logger.error(f"Downloading failed with error {e}")
        flash(
            "Backtest results don't exist",
            "error",
        )
        return redirect(url_for("strat_add.get_cluster_backtest"))


@strat_add.route("/strat_expand/var_report", methods=["POST"])
@swag_from(
    "/docs/strategy_detailing_and_expansion/check_var_report.yaml",
    methods=["POST"],
)
@login_required
def check_var_report():
    # Checking if var report exists.
    try:
        strategy_name = request.form["strategy"]
        week = request.form["week"]
        strategy = Strategy.query.filter_by(strategy_name=strategy_name).first()
        status = strategy.status.state_description.lower()
        minio_path = current_app.config["STRAT_STATUS_PATH_MAPPING"][status]
        is_authentic = check_authenticity(
            strategy_list=[strategy_name], state_description=status.upper()
        )
        is_authentic_access = check_access(strategy_list=[strategy_name])
        if (
            (is_authentic is False)
            and ((strategy.developer != current_user.username) or (status == "pending"))
            and (is_authentic_access is False)
        ):
            current_app.logger.warning(
                f"Unauthorized attempt to access var report for strategy {strategy_name}"
            )
            abort(403)
        current_app.logger.info(
            f"Searching for {strategy_name} var report for {week} week in {minio_path}"
        )
        if week == "current":
            post_fix = "_var_report.html"
        else:
            week = convert_date_to_filename_format(week)
            post_fix = f"_{week}_var_report.html"
        prefix = f"{minio_path}/{strategy_name}/{strategy_name}" + post_fix
        if check_object_exists(prefix=prefix) is True:
            return "success"
        return "failed"
    except Exception as e:
        current_app.logger.error(
            f"Var Report checking failed for strategy {strategy_name} with error {e}"
        )
        return "failed"


@strat_add.route("/strat_expand/var_report/<strategy_name>/<week>", methods=["GET"])
@swag_from(
    "/docs/strategy_detailing_and_expansion/show_var_report.yaml", methods=["GET"]
)
@login_required
def show_var_report(strategy_name: str, week: str):
    """View for displaying var report.

    Args:
        strategy_name (str): strategy name
        week (str): date for which report is needed
    """
    try:
        strategy = Strategy.query.get_or_404(strategy_name)
    except Exception as e:
        current_app.logger.error(f"Strategy {strategy_name} not found")
        raise e
    status = strategy.status.state_description.lower()
    is_authentic = check_authenticity(
        strategy_list=[strategy.strategy_name], state_description=status.upper()
    )
    is_authentic_access = check_access(strategy_list=[strategy.strategy_name])
    if (
        (is_authentic is False)
        and ((strategy.developer != current_user.username) or (status == "pending"))
        and (is_authentic_access is False)
    ):
        current_app.logger.warning(
            f"Unauthorized attempt to access VaR report for strategy {strategy_name}"
        )
        abort(403)
    current_app.logger.info(
        f"VaR report accessed for {week} week and strategy {strategy_name}"
    )
    if week == "current":
        post_fix = "_var_report.html"
    else:
        week = convert_date_to_filename_format(week)
        post_fix = f"_{week}_var_report.html"
    try:
        var_report = get_files_from_minio(
            status=status, strategy_name=strategy_name, post_fix=post_fix
        )
        var_report = var_report.decode()
    except Exception as e:
        current_app.logger.error(
            f"Got error {e} in request to fetch var report for {week} by user {current_user.username}"
        )
        var_report = None

    return render_template(
        "strat_add/report.html",
        report=var_report,
    )


@strat_add.route("/policy", methods=["GET", "POST"])
@login_required
def get_policy_page():
    """View for displaying policy page and handle policy update"""
    policies = get_all_md_files(path="policies/policies.csv", folder_name="policies")
    current_app.logger.info(f"Policy page accessed by {current_user.username}")
    if request.method == "POST":
        if current_user.role.name != "ADMIN":
            current_app.logger.warning("Unauthorized attempt to modify policy")
            abort(403)
        path = request.form["path"]
        content = request.form["content"]
        title = request.form["title"]
        if check_object_exists("policies/policies.csv"):
            data = get_file_data_from_minio("policies/policies.csv")
            df = pd.read_csv(BytesIO(data), index_col=0)
        else:
            df = pd.DataFrame(columns=["index", "path", "title"])
            df = df.set_index("index")

        if path != "" and len(df) > 0 and path in df["path"].values:
            df.loc[df.path == path, "title"] = title
        else:
            index = df.index.max() + 1 if len(df) else 0
            path = f"policies/policy{index}.md"
            new_row = pd.DataFrame(
                {
                    "index": [index],
                    "path": [path],
                    "title": [title],
                }
            )
            new_row.set_index("index", inplace=True)
            df = pd.concat([df, new_row], sort=False)
        update_content_on_minio(path=path, content=content)
        upload_dataframe_to_minio(path="policies/policies.csv", dataframe=df)
        current_app.logger.info(f"Successfully updated the policy {title}")
    return render_template("strat_add/policy.html", cards=policies)


@strat_add.route("/convert_to_md_format", methods=["POST"])
@login_required
def html_to_markdown():
    """Get markdown file corresponding to path"""
    path = request.form["path"]
    title = ""
    file_data = ""
    if check_object_exists("policies/policies.csv"):
        data = get_file_data_from_minio("policies/policies.csv")
        df = pd.read_csv(BytesIO(data), index_col=0)
        if len(df[df["path"] == path]):
            title = df[df["path"] == path]["title"].values[0]
            file_data = get_file_data_from_minio(path).decode("utf-8")
    return [title, file_data]


@strat_add.route("/delete_md_file", methods=["POST"])
@login_required
def delete_md_file():
    """Delete policy at given path"""
    if current_user.role.name != "ADMIN":
        current_app.logger.warning("Unauthorized attempt to delete md file")
        abort(403)
    path = request.form["path"]
    type_ = request.form["type"]
    if type_ == "new_area":
        csv_path = "new_areas_to_work/new_areas_to_work.csv"
    else:
        csv_path = "policies/policies.csv"

    if check_object_exists(path):
        data = get_file_data_from_minio(csv_path)
        df = pd.read_csv(BytesIO(data), index_col=0)

        if len(df[df.path == path]):
            current_app.logger.info(
                f"Removing md file {df[df['path'] == path]['title'].values[0]}"
            )
            df = df[df.path != path]
        else:
            current_app.logger.error(
                f"Invalid delete request by {current_user.username}"
            )
            return "empty"
    else:
        current_app.logger.info(
            f"File does not exist at minio. Invalid deletion request by {current_user.username}. "
        )
        return "failed"

    upload_dataframe_to_minio(path=csv_path, dataframe=df)
    # to delete attachmets as well
    if type_ == "new_area":
        path = os.path.dirname(path)
    if check_object_exists(path):
        delete_file_from_minio(path=path)
    return "success"


@strat_add.route("/upload_html_files", methods=["POST"])
@login_required
def upload_html_files():
    if current_user.role.name != "ADMIN":
        current_app.logger.warning("Unauthorized attempt to upload html files")
        abort(403)
    files = request.files.getlist("htmlFiles")
    path = request.form.getlist("path")
    path = os.path.dirname(path[0])
    for file in files:
        filename = path + "/" + file.filename
        upload_html_file_on_minio(path=filename, file=file.stream)
    return "success"


@strat_add.route("/fetch_html_file", methods=["POST"])
@login_required
def fetch_html_file():
    path = request.form["path"]
    if check_object_exists(path):
        content = get_file_data_from_minio(path)
        return content
    else:
        return "file does not exist"


@strat_add.route("/delete_html_file", methods=["POST"])
@login_required
def delete_html_file():
    if current_user.role.name != "ADMIN":
        current_app.logger.warning("Unauthorized attempt to delete html files")
        abort(403)
    path = request.form["path"]
    if check_object_exists(path):
        delete_file_from_minio(path=path)
        return "success"
    else:
        return "failed"


@strat_add.route("/risk_management", methods=["GET", "POST"])
@login_required
def risk_management():
    """View for displaying risk management page

    Returns:
        str: return the rendered template as a string or a Response object for redirection (on clicking the update button)
    """
    if current_user.role.name != "ADMIN":
        current_app.logger.warning(
            "Unauthorized attempt to access Risk management page"
        )
        abort(403)
    current_app.logger.info(f"Risk management page accessed by {current_user.username}")
    form = RiskManagementForm()
    error_message = None

    if form.validate_on_submit():
        limits_dict = {
            "OptidxSellLimit": int((form.optidx_sell_limit.data) * 100000),
            "OptidxBuyLimit": int((form.optidx_buy_limit.data) * 100000),
            "OptstkSellLimit": int((form.optstk_sell_limit.data) * 100000),
            "OptstkBuyLimit": int((form.optstk_buy_limit.data) * 100000),
            "FutidxLimit": int((form.futidx_limit.data) * 100000),
            "FutstkLimit": int((form.futstk_limit.data) * 100000),
            "CashLimit": int((form.cash_limit.data) * 100000),
        }
        error_message = update_risk_limits(limits_dict)
        if error_message is None:
            current_app.logger.info(f"Risk limits updated by {current_user.username}")
            return redirect(url_for("strat_add.risk_management"))
        else:
            return render_template(
                "strat_add/risk_management.html",
                form=form,
                error_message=error_message,
                current_limits=limits_dict,
            )

    current_limits_df, error_message = fetch_risk_limits()
    limits_dict = current_limits_df.set_index("variable_name")[
        "variable_value"
    ].to_dict()

    form.optidx_sell_limit.data = int(limits_dict.get("OptidxSellLimit", 1) / 100000)
    form.optidx_buy_limit.data = int(limits_dict.get("OptidxBuyLimit", 1) / 100000)
    form.optstk_sell_limit.data = int(limits_dict.get("OptstkSellLimit", 1) / 100000)
    form.optstk_buy_limit.data = int(limits_dict.get("OptstkBuyLimit", 1) / 100000)
    form.futidx_limit.data = int(limits_dict.get("FutidxLimit", 1) / 100000)
    form.futstk_limit.data = int(limits_dict.get("FutstkLimit", 1) / 100000)
    form.cash_limit.data = int(limits_dict.get("CashLimit", 1) / 100000)

    return render_template(
        "strat_add/risk_management.html",
        form=form,
        error_message=error_message,
        current_limits=limits_dict,
    )


@strat_add.route("/delete_tradelog", methods=["GET", "POST"])
@login_required
def delete_tradelog_page():
    """View for deleting sentinel backtest tradelogs

    Raises:
        e: Raises exception if unauthorized attempt is made to access the page

    """
    current_app.logger.info("Delete tradelog page accessed")
    form = DeleteTradelogForm()
    strat_choice_list = get_strategies(state_description="LIVE")
    strat_choice_list.extend(get_accessible_strategies(state_description="LIVE"))
    strat_choices_names = list(
        {
            strat.strategy_name
            for strat in strat_choice_list
            if "cluster" not in strat.strategy_name
        }
    )
    form.strategies.choices = strat_choices_names

    if form.validate_on_submit():
        selected_strategies = form.strategies.data
        selected_strategies = [strat.strip() for strat in selected_strategies]
        is_authorized_access = True
        for strat in selected_strategies:
            if strat not in strat_choices_names:
                is_authorized_access = False
        if is_authorized_access is False:
            current_app.logger.warning(
                f"Unauthorized attempt to delete tradelog of {selected_strategies}"
            )
            abort(403)

        add_deleted_tradelogs_on_minio(
            strategies_to_add=selected_strategies,
            accessible_strategies=strat_choices_names,
        )

        current_app.logger.info(f"Deleted tradelog of {selected_strategies}")
        flash("Delete Tradelog request submitted successfully", "success")

    form.strategies.data = fetch_deleted_tradelogs_from_minio(
        accessible_strategies=strat_choices_names
    )

    return render_template("strat_add/delete_tradelog.html", form=form)
