from flask_wtf import FlaskForm
from flask_wtf.form import _Auto
from wtforms import (
    StringField,
    SubmitField,
    TextAreaField,
    SelectField,
    FileField,
    DateField,
    FloatField,
    BooleanField,
    TimeField,
    SelectMultipleField,
    IntegerField,
)
from wtforms.validators import (
    DataRequired,
    ValidationError,
    Optional,
    NumberRange,
)
from flask_wtf.file import FileAllowed
from app.models import Strategy, Status


class OneRequired(object):
    """
    A custom validation class to validate if at least one of the two fields exists.
    """

    def __init__(self, fieldname):
        self.fieldname = fieldname

    def __call__(self, form, field):
        try:
            other = form[self.fieldname]
        except KeyError:
            raise ValidationError(
                field.gettext("Invalid field name '%s'.") % self.fieldname
            )
        if field.data == "None":
            field.data = None
        if other.data == "None":
            other.data = None
        if (field.data is None) and (other.data is None):
            d = {
                "other_label": hasattr(other, "label")
                and other.label.text
                or self.fieldname,
                "other_name": self.fieldname,
            }
            message = field.gettext("This field or %(other_name)s is required.")

            raise ValidationError(message % d)


class SegmentNameCheck(object):
    """
    Custom validation function to parse and change segment name.
    """

    def __call__(self, form, field):
        values = field.data.split("-")
        field.data = values[0]


class FieldNameCheck(object):
    """
    Custom validation function to trim and evaluate strategy name.
    """

    def __call__(self, form, field):
        if field.data:
            field.data = field.data.strip()
        if field.data == "None":
            field.data = None


class ClusterNameCheck(object):
    """
    Custom validation function to trim and evaluate cluster name.
    """

    def __call__(self, form, field):
        for i in range(len(field.data)):
            field.data[i] = field.data[i].strip()


class StrategySubmissionForm(FlaskForm):
    """
    Form for submitting a new strategy for reviewing.
    """

    strategy_name = StringField(
        "Strategy Name", validators=[DataRequired(), FieldNameCheck()]
    )
    segment = SelectField(
        "Segment",
        choices=[
            "FUTSTK",
            "FUTIDX",
            "FUTIDX_BSE",
            "FUTIDX_GIFT",
            "FUTIDX_US",
            "OPTIDX_US",
            "FUTCUR",
            "FUTCOM",
            "CASH",
            "OPTIDX",
            "OPTIDX-FINNIFTY",
            "OPTIDX-MIDCPNIFTY",
            "OPTSTK",
            "OPTCUR",
            "OPTCOM",
            "OPTIDX_KRX",
            "OPTIDX_BSE",
        ],
        validators=[DataRequired(), SegmentNameCheck()],
    )
    exchange_name = SelectField(
        "Exchange",
        choices=[],
        validators=[DataRequired()],
    )
    backtest_date = DateField("Backtest Date")
    overlap_days = IntegerField("Overlapping days for backtest", default=20)
    trigger_coeff = FloatField("Trigger Price Coefficient", validators=[Optional()])
    limit_coeff = FloatField("Limit Price Coefficient", validators=[Optional()])
    expiration_time = TimeField("Expiration Time", validators=[Optional()])
    custom_slippage = FloatField("Custom Slippage", validators=[Optional()])
    long_short = SelectField(
        "Long/Short", choices=[1, -1, 0], validators=[DataRequired()]
    )
    long_book_mapping = SelectField(
        "Long Book Mapping",
        choices=["None", "LC0", "LC1", "LC2", "LC3", "LC4", "LC5"],
        validators=[OneRequired("short_book_mapping")],
        validate_choice=False,
    )
    short_book_mapping = SelectField(
        "Short Book Mapping",
        choices=["None", "SC1", "SC2", "SC3", "SC4"],
        validators=[OneRequired("long_book_mapping")],
        validate_choice=False,
    )
    cluster_mapping = SelectMultipleField(
        choices=[],
        validate_choice=False,
        validators=[ClusterNameCheck()],
    )
    comments = TextAreaField("Comments")
    python_file = FileField(
        "Strategy Code (py file)", validators=[DataRequired(), FileAllowed(["py"])]
    )
    ipynb_file = FileField(
        "Strategy Code (ipynb file)",
        validators=[FileAllowed(["ipynb"])],
    )
    is_rework = BooleanField("Select this if it's a reworked strategy")
    old_strategy = StringField("Name of the original Strategy")
    not_run_cluster_backtest = BooleanField("Cluster Backtest")
    not_run_kivifolio = BooleanField("Kivifolio")
    submit = SubmitField("Submit Strategy")

    def validate_old_strategy(self, old_strategy: StringField):
        """Checks if the strategy with this name already exists in live

        Args:
            strategy_name (StringField): Name of the strategy as received in the form
        """
        if len(old_strategy.data) == 0:
            old_strategy.data = None
            return
        pushed_strat = Strategy.query.filter(
            Strategy.strategy_name == old_strategy.data,
        ).first()
        if pushed_strat is None:
            raise ValidationError("No strategy exists with this name!!")
        if (
            pushed_strat.strat_state
            == Status.query.filter_by(state_description="TEST").first().id
        ):
            raise ValidationError("Original strategy must not be in test-only mode!!")


class StrategyReviewForm(FlaskForm):
    """
    Form for reviewing a strategy.
    """

    timecheck = StringField("Time Check")
    correlation_check = StringField("Correlation Check")
    trade_distribution_check = StringField("Trade Distribution Check")
    risk_analysis = StringField("Risk Analysis")
    num_days_trading = StringField("Number of trading days")
    comments = TextAreaField("Comments")
    to_change = TextAreaField("To Change")
    strategy_accept_live = SubmitField("Accept (Live)")
    strategy_accept_test = SubmitField("Accept (Test)")
    strategy_reject = SubmitField("Reject Strategy")
    save = SubmitField("Save Review")
    strat_date = DateField("Live Start Day", validators=[Optional()])
    is_reworked_start = BooleanField(
        "Check to select reworked strategy's start date as live start date"
    )


class ClusterBacktestForm(FlaskForm):
    """
    Form to submit a slave backtest
    """

    cluster = SelectField(
        "Clusters",
        choices=[],
        validate_choice=False,
        validators=[DataRequired()],
    )
    slaves = StringField("Slaves for Backtesting")
    is_restart = BooleanField(
        "Check whether to restart cluster backtest", validators=[Optional()]
    )
    submit = SubmitField("Submit Slave Backtest")


class ChangeClusterMappingForm(FlaskForm):
    strategies = SelectField(
        "Strategies",
        choices=[],
        validate_choice=False,
    )
    cluster_mapping = SelectMultipleField(
        choices=[],
        validate_choice=False,
        validators=[ClusterNameCheck()],
    )
    submit = SubmitField("Submit")


class RiskManagementForm(FlaskForm):
    """
    Form for managing exposure limits. Takes in limit values in Lakhs.
    """

    optidx_sell_limit = IntegerField(
        "Optidx Sell Limit", validators=[DataRequired(), NumberRange(min=0, max=20000)]
    )
    optidx_buy_limit = IntegerField(
        "Optidx Buy Limit", validators=[DataRequired(), NumberRange(min=0, max=20000)]
    )
    optstk_sell_limit = IntegerField(
        "Optstk Sell Limit", validators=[DataRequired(), NumberRange(min=0, max=20000)]
    )
    optstk_buy_limit = IntegerField(
        "Optstk Buy Limit", validators=[DataRequired(), NumberRange(min=0, max=20000)]
    )
    futidx_limit = IntegerField(
        "Futidx Limit", validators=[DataRequired(), NumberRange(min=0, max=20000)]
    )
    futstk_limit = IntegerField(
        "Futstk Limit", validators=[DataRequired(), NumberRange(min=0, max=20000)]
    )
    cash_limit = IntegerField(
        "Cash Limit", validators=[DataRequired(), NumberRange(min=0, max=20000)]
    )
    update = SubmitField("Update Limits")


class DeleteTradelogForm(FlaskForm):
    strategies = SelectMultipleField(
        choices=[],
        validate_choice=False,
    )
    submit = SubmitField("Submit")
