from typing import List
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, UserMixin
from flask_minio import <PERSON>o
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import func, not_
from sqlalchemy.engine import Row

db = SQLAlchemy()
minio = Minio()

login_manager = LoginManager()
login_manager.login_view = "auth.login"
login_manager.session_protection = "strong"


@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))


class User(UserMixin, db.Model):
    __tablename__ = "users"
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), index=True, unique=True)
    email = db.Column(db.String(128), unique=True)
    password_enc = db.Column(db.String(128))
    role_id = db.Column(db.Integer, db.<PERSON><PERSON><PERSON>("roles.id"))
    manager = db.Column(db.String(64), db.<PERSON><PERSON><PERSON>("users.username"))
    developers = db.relationship(
        "User", backref=db.backref("manager_details", remote_side=[username])
    )
    strategies = db.relationship("Strategy", backref="user", lazy="dynamic")
    strategy_access = db.relationship(
        "StrategyAccess",
        backref="user",
        lazy="dynamic",
        passive_deletes=True,
        passive_updates=True,
    )


class Role(db.Model):
    __tablename__ = "roles"
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True)
    users = db.relationship("User", backref="role", lazy="dynamic")


class PendingBacktests(db.Model):
    __tablename__ = "pending_backtest"
    id = db.Column(db.Integer, primary_key=True)
    request_time = db.Column(db.DateTime)
    strategy_name = db.Column(db.String(128))
    service_state = db.Column(db.String(20), default="00000")
    allow_modification = db.Column(db.Integer, default=1)


class LiveSheet(db.Model):
    __bind_key__ = "portfolio_stats"
    __tablename__ = "live_sheet"
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    logit_id = db.Column(db.BigInteger, nullable=True)
    entry_timestamp = db.Column(db.DateTime, nullable=True)
    segment = db.Column(db.String(128), nullable=True)
    symbol = db.Column(db.String(256), nullable=True)
    expiry = db.Column(db.Date, nullable=True)
    total_quantity = db.Column(db.Float, nullable=True)
    strategy = db.Column(db.String(256), nullable=True)
    entry_price = db.Column(db.Float, nullable=True)
    type = db.Column(db.String(64), nullable=True)
    strike = db.Column(db.Float, nullable=True)
    exposure = db.Column(db.Float, nullable=True)
    slave_strat = db.Column(db.String(256), nullable=True)
    slave_dev = db.Column(db.String(256), nullable=True)
    book_long = db.Column(db.String(64), nullable=True)
    book_short = db.Column(db.String(64), nullable=True)
    exchange = db.Column(db.String(64), nullable=True)
    ltp = db.Column(db.Float, nullable=True)


class DeadSheet(db.Model):
    __bind_key__ = "portfolio_stats"
    __tablename__ = "dead_sheet"
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    logit_id = db.Column(db.BigInteger, nullable=True)
    entry_timestamp = db.Column(db.DateTime, nullable=True)
    segment = db.Column(db.String(128), nullable=True)
    symbol = db.Column(db.String(256), nullable=True)
    expiry = db.Column(db.Date, nullable=True)
    total_quantity = db.Column(db.Float, nullable=True)
    strategy = db.Column(db.String(256), nullable=True)
    entry_price = db.Column(db.Float, nullable=True)
    type = db.Column(db.String(64), nullable=True)
    strike = db.Column(db.Float, nullable=True)
    exit_timestamp = db.Column(db.DateTime, nullable=True)
    exit_price = db.Column(db.Float, nullable=True)
    stt = db.Column(db.Float, nullable=True)
    brokerage = db.Column(db.Float, nullable=True)
    others = db.Column(db.Float, nullable=True)
    exposure = db.Column(db.Float, nullable=True)
    PnL = db.Column(db.Float, nullable=True)
    slave_strat = db.Column(db.String(256), nullable=True)
    slave_dev = db.Column(db.String(256), nullable=True)
    book_long = db.Column(db.String(64), nullable=True)
    book_short = db.Column(db.String(64), nullable=True)
    exchange = db.Column(db.String(64), nullable=True)
    entry_date = db.Column(db.Date, nullable=True)
    exit_date = db.Column(db.Date, nullable=True)

    @staticmethod
    def get_strategy_dead_trades(strategy_name: str) -> List[Row]:
        return (
            db.session.query(
                func.DATE(DeadSheet.exit_timestamp),
                func.sum(DeadSheet.PnL),
                func.count(DeadSheet.id),
            )
            .filter(
                (DeadSheet.strategy == strategy_name)
                | (DeadSheet.slave_strat == strategy_name)
            )
            .group_by(func.DATE(DeadSheet.exit_timestamp))
            .order_by(func.DATE(DeadSheet.exit_timestamp))
            .all()
        )

    @staticmethod
    def get_developer_dead_trades(developer_name: List[str]) -> List[Row]:
        return (
            db.session.query(
                DeadSheet.exit_date,
                DeadSheet.exchange,
                func.sum(DeadSheet.PnL),
                func.count(DeadSheet.id),
            )
            .filter(DeadSheet.slave_dev.in_(developer_name))
            .group_by(DeadSheet.exit_date, DeadSheet.exchange)
            .order_by(DeadSheet.exit_date, DeadSheet.exchange)
            .all()
        )

    @staticmethod
    def get_all_dead_trades() -> List[Row]:
        return (
            db.session.query(
                DeadSheet.exit_date,
                DeadSheet.exchange,
                func.sum(DeadSheet.PnL),
                func.count(DeadSheet.id),
            )
            .group_by(DeadSheet.exit_date, DeadSheet.exchange)
            .order_by(DeadSheet.exit_date, DeadSheet.exchange)
            .all()
        )


class Strategy(db.Model):
    __tablename__ = "curr_info"
    strategy_name = db.Column(db.String(128), primary_key=True)
    live_start_day = db.Column(db.Date)
    last_run_day = db.Column(db.Date, nullable=True)
    developer = db.Column(db.String(64), db.ForeignKey("users.username"))
    backtest_start_date = db.Column(db.Date)
    overlap_days = db.Column(db.Integer)
    segment = db.Column(db.String(64))
    exchange_name = db.Column(db.String(64))
    long_short = db.Column(db.Integer)
    book_long = db.Column(db.String(128), nullable=True)
    book_short = db.Column(db.String(128), nullable=True)
    strat_state = db.Column(db.Integer, db.ForeignKey("strategy_state.id"))
    submission_day = db.Column(db.Date, nullable=True)
    trigger_coeff = db.Column(db.Float, nullable=True)
    limit_coeff = db.Column(db.Float, nullable=True)
    expiration_time = db.Column(db.Time, nullable=True)
    comments = db.Column(db.String, nullable=True)
    reworked_strategy = db.Column(db.String(128))
    custom_slippage = db.Column(db.Float, nullable=True)
    cluster_mapping = db.relationship(
        "StrategyClusterMapping",
        backref="strategy",
        lazy="select",
        passive_deletes=True,
        passive_updates=True,
    )
    review = db.relationship(
        "StrategyReview",
        backref="strategy",
        lazy="dynamic",
        passive_deletes=True,
        passive_updates=True,
    )
    meta_data = db.relationship(
        "StrategyMetaData",
        backref="strategy",
        lazy="dynamic",
        passive_deletes=True,
        passive_updates=True,
    )
    strategy_access = db.relationship(
        "StrategyAccess",
        backref="strategy",
        lazy="dynamic",
        passive_deletes=True,
        passive_updates=True,
    )


class StrategyClusterMapping(db.Model):
    __tablename__ = "strategy_cluster_mapping"
    id = db.Column(db.Integer, primary_key=True)
    strategy_name = db.Column(db.String(128), db.ForeignKey("curr_info.strategy_name"))
    cluster_name = db.Column(db.String(64))
    mapping_state = db.Column(db.Integer, db.ForeignKey("cluster_mapping_status.id"))
    mapping_status = db.relationship("ClusterMappingStatus", backref="cluster_mappings")

    @staticmethod
    def check_cluster_for_strategy(strategy_name: str, environment: str) -> bool:
        """Checks whether there is a cluster for a particular strategy or not in a given environment

        Args:
            strategy_name (str): name of the strategy
            environment (str): environment to be checked

        Returns:
            bool: True/False on the basis of cluster exists or not
        """
        exists = (
            db.session.query(StrategyClusterMapping)
            .filter_by(
                strategy_name=strategy_name,
                mapping_status=ClusterMappingStatus.get_status(description=environment),
            )
            .first()
            is not None
        )
        return exists


class Status(db.Model):
    __tablename__ = "strategy_state"
    id = db.Column(db.Integer, primary_key=True)
    state_description = db.Column(db.String(64))
    strategies = db.relationship("Strategy", backref="status", lazy="dynamic")


class ClusterMappingStatus(db.Model):
    __tablename__ = "cluster_mapping_status"
    id = db.Column(db.Integer, primary_key=True)
    state_description = db.Column(db.String(8))

    @staticmethod
    def get_status(description: str):
        return ClusterMappingStatus.query.filter_by(
            state_description=description
        ).first()


class StrategyMetaData(db.Model):
    __tablename__ = "strategy_meta_data"
    strategy_name = db.Column(
        db.String(128),
        db.ForeignKey(
            "curr_info.strategy_name", ondelete="CASCADE", onupdate="CASCADE"
        ),
        primary_key=True,
    )
    max_dd_submit = db.Column(db.Float, nullable=True)
    monthly_sharpe_submit = db.Column(db.Float, nullable=True)
    ret_dd_submit = db.Column(db.Float, nullable=True)
    monthly_ret_submit = db.Column(db.Float, nullable=True)
    max_dd_monte = db.Column(db.Float, nullable=True)
    percentile_dd = db.Column(db.Float, nullable=True)
    curr_backtest_dd = db.Column(db.Float, nullable=True)
    last_sentinel_run = db.Column(db.Date, nullable=True)
    monthly_ret_post = db.Column(db.Float, nullable=True)
    monthly_sharpe_post = db.Column(db.Float, nullable=True)
    monthly_ret_live = db.Column(db.Float, nullable=True)
    monthly_sharpe_live = db.Column(db.Float, nullable=True)
    max_dd_live = db.Column(db.Float, nullable=True)
    ret_dd_live = db.Column(db.Float, nullable=True)
    trading_days_live = db.Column(db.Integer, nullable=True)
    inout_sample_bhatt_dist = db.Column(db.Float, nullable=True)
    daily_sharpe_live = db.Column(db.Float, nullable=True)


class StrategyReview(db.Model):
    __tablename__ = "strategy_review"
    strategy_name = db.Column(
        db.String(128),
        db.ForeignKey(
            "curr_info.strategy_name", ondelete="CASCADE", onupdate="CASCADE"
        ),
        primary_key=True,
    )
    timecheck = db.Column(db.String(1000), nullable=True)
    correlation_check = db.Column(db.String(1000), nullable=True)
    trade_distribution_check = db.Column(db.String(1000), nullable=True)
    risk_analysis = db.Column(db.String(1000), nullable=True)
    num_days_trading = db.Column(db.String(1000), nullable=True)
    comments = db.Column(db.Text, nullable=True)
    to_change = db.Column(db.Text, nullable=True)
    to_do = db.Column(db.Text, nullable=True)


class StrategyAccess(db.Model):
    __tablename__ = "strategy_access"
    id = db.Column(db.Integer, primary_key=True)
    strategy_name = db.Column(
        db.String(128),
        db.ForeignKey(
            "curr_info.strategy_name", ondelete="CASCADE", onupdate="CASCADE"
        ),
    )
    username = db.Column(
        db.String(128),
        db.ForeignKey("users.username", ondelete="CASCADE", onupdate="CASCADE"),
    )
