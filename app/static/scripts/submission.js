invalid_filter_popup = document.getElementById("invalid_filter_popup");
invalid_filter_popup_close = document.getElementById(
    "invalid_filter_popup_close"
);
document.forms[0].addEventListener("input", autoFillDate);
form = document.getElementById("strat_add_form");
old_strategy_field = document.getElementById("old_strategy");
textarea = document.getElementsByTagName("textarea");
updateFormField();

form.segment.onchange = function updateRequirements() {
    let segment = form.segment.value;
    if (segment == "CASH") {
        form.trigger_coeff.required = true;
        form.limit_coeff.required = true;
        form.expiration_time.required = true;
    } else {
        form.trigger_coeff.required = false;
        form.limit_coeff.required = false;
        form.expiration_time.required = false;
    }
};

form.is_rework.onchange = function () {
    updateFormField();
};

function updateFormField() {
    let is_rework = form.is_rework.checked;
    if (is_rework == true) {
        form.old_strategy.required = true;
        old_strategy_field.style.display = "block";
    } else {
        form.old_strategy.required = false;
        old_strategy_field.style.display = "none";
    }
}

let BACKTEST_PERIOD = {
    "FUTSTK": "2010-01-01",
    "FUTIDX": "2010-01-01",
    "FUTIDX_GIFT": "2013-01-01",
    "CASH": "2010-01-01",
    "OPTIDX": "2018-01-01",
    "OPTIDX-FINNIFTY": "2021-01-01",
    "OPTIDX-MIDCPNIFTY": "2023-05-31",
    "OPTSTK": "2018-01-01",
    "FUTCOM": "2012-01-01",
    "FUTCUR": "2010-01-01",
    "OPTCUR": "2019-01-01",
    "OPTCOM": "2021-01-01",
    "OPTIDX_KRX": "2020-01-01",
    "OPTIDX_BSE": "2023-10-01",
    "FUTIDX_BSE": "2023-10-01",
    "FUTIDX_US": "2017-07-09",
    "OPTIDX_US":"2017-12-27",
    "DEFAULT": "2015-01-01",
};

function autoFillDate(e) {
    const form = e.currentTarget;
    const active = e.target;
    const fields = form.elements;
    if (active.id == "segment") {
        if (active.value in BACKTEST_PERIOD) {
            fields.backtest_date.value = BACKTEST_PERIOD[active.value];
        } else {
            fields.backtest_date.value = BACKTEST_PERIOD["DEFAULT"];
        }
    }
}

for (let i = 0; i < textarea.length; i++) {
    textarea[i].setAttribute(
        "style",
        "height:" + textarea[i].scrollHeight + "px;overflow-y:hidden;"
    );
    textarea[i].addEventListener("input", OnInput, false);
}

function OnInput() {
    this.style.height = 0;
    this.style.height = this.scrollHeight + "px";
}

invalid_filter_popup_close.onclick = function () {
    invalid_filter_popup.style.visibility = "hidden";
};


