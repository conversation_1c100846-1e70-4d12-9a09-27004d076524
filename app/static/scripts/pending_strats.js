pickle_reset_btns = document.getElementsByName("pickle_reset_btns");
reason_success = document.getElementById("reason_success");
sucess_popup = document.getElementById("sucess_popup");
reason_failure = document.getElementById("reason_failure");
failure_popup = document.getElementById("failure_popup");
success_popup_close = document.getElementById("success_popup_close");

for (let index = 0; index < pickle_reset_btns.length; index++) {
    pickle_reset_btns[index].addEventListener('click', function () {
        clear_and_exit(this, "TEST");
    });
}

failure_popup_close.onclick = function () {
    failure_popup.style.visibility = "hidden";
}

success_popup_close.onclick = function () {
    window.location.reload();
}