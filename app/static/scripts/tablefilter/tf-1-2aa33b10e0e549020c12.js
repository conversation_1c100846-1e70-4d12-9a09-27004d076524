(window.webpackJsonp=window.webpackJsonp||[]).push([[1],{440:function(t,e,n){var r={"./array":20,"./array.js":20,"./const":4,"./const.js":4,"./cookie":60,"./cookie.js":60,"./dom":2,"./dom.js":2,"./emitter":89,"./emitter.js":89,"./event":5,"./event.js":5,"./extensions/advancedGrid/adapterEzEditTable":441,"./extensions/advancedGrid/adapterEzEditTable.js":441,"./extensions/advancedGrid/advancedGrid":443,"./extensions/advancedGrid/advancedGrid.js":443,"./extensions/colOps/colOps":444,"./extensions/colOps/colOps.js":444,"./extensions/colsVisibility/colsVisibility":445,"./extensions/colsVisibility/colsVisibility.js":445,"./extensions/filtersVisibility/filtersVisibility":446,"./extensions/filtersVisibility/filtersVisibility.js":446,"./extensions/sort/adapterSortabletable":442,"./extensions/sort/adapterSortabletable.js":442,"./extensions/sort/sort":447,"./extensions/sort/sort.js":447,"./feature":10,"./feature.js":10,"./modules/alternateRows":85,"./modules/alternateRows.js":85,"./modules/baseDropdown":48,"./modules/baseDropdown.js":48,"./modules/checkList":91,"./modules/checkList.js":91,"./modules/clearButton":84,"./modules/clearButton.js":84,"./modules/dateType":74,"./modules/dateType.js":74,"./modules/dropdown":90,"./modules/dropdown.js":90,"./modules/gridLayout":77,"./modules/gridLayout.js":77,"./modules/hash":92,"./modules/hash.js":92,"./modules/help":75,"./modules/help.js":75,"./modules/highlightKeywords":79,"./modules/highlightKeywords.js":79,"./modules/loader":78,"./modules/loader.js":78,"./modules/markActiveColumns":81,"./modules/markActiveColumns.js":81,"./modules/noResults":86,"./modules/noResults.js":86,"./modules/paging":87,"./modules/paging.js":87,"./modules/popupFilter":80,"./modules/popupFilter.js":80,"./modules/rowsCounter":82,"./modules/rowsCounter.js":82,"./modules/state":76,"./modules/state.js":76,"./modules/statusBar":83,"./modules/statusBar.js":83,"./modules/storage":93,"./modules/storage.js":93,"./modules/toolbar":18,"./modules/toolbar.js":18,"./number":22,"./number.js":22,"./root":9,"./root.js":9,"./settings":1,"./settings.js":1,"./sort":31,"./sort.js":31,"./string":8,"./string.js":8,"./tablefilter":127,"./tablefilter.js":127,"./types":3,"./types.js":3};function webpackContext(t){var e=webpackContextResolve(t);return n(e)}function webpackContextResolve(t){if(n.o(r,t))return r[t];var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}webpackContext.keys=function webpackContextKeys(){return Object.keys(r)},webpackContext.resolve=webpackContextResolve,(t.exports=webpackContext).id=440},441:function(t,e,n){"use strict";n.r(e),n.d(e,"default",function(){return r});var o=n(10),f=n(2),i=n(4),s=n(1),a=n(9);function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function _typeof(t){return typeof t}:function _typeof(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _defineProperties(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function _setPrototypeOf(t,e){return(_setPrototypeOf=Object.setPrototypeOf||function _setPrototypeOf(t,e){return t.__proto__=e,t})(t,e)}function _createSuper(r){var o=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(t){return!1}}();return function _createSuperInternal(){var t,e=_getPrototypeOf(r);if(o){var n=_getPrototypeOf(this).constructor;t=Reflect.construct(e,arguments,n)}else t=e.apply(this,arguments);return function _possibleConstructorReturn(t,e){return!e||"object"!==_typeof(e)&&"function"!=typeof e?function _assertThisInitialized(t){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(t):e}(this,t)}}function _getPrototypeOf(t){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf:function _getPrototypeOf(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var r=function(){!function _inherits(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&_setPrototypeOf(t,e)}(AdapterEzEditTable,o["Feature"]);var r=_createSuper(AdapterEzEditTable);function AdapterEzEditTable(t,e){var n;return function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,AdapterEzEditTable),(n=r.call(this,t,AdapterEzEditTable)).desc=Object(s.defaultsStr)(e.description,"ezEditTable adapter"),n.filename=Object(s.defaultsStr)(e.filename,"ezEditTable.js"),n.vendorPath=e.vendor_path,n.loadStylesheet=Boolean(e.load_stylesheet),n.stylesheet=Object(s.defaultsStr)(e.stylesheet,n.vendorPath+"ezEditTable.css"),n.stylesheetName=Object(s.defaultsStr)(e.stylesheet_name,"ezEditTableCss"),e.scroll_into_view=!1!==e.scroll_into_view&&t.gridLayout,n._ezEditTable=null,n.cfg=e,n.enable(),n}return function _createClass(t,e,n){return e&&_defineProperties(t.prototype,e),n&&_defineProperties(t,n),t}(AdapterEzEditTable,[{key:"init",value:function init(){var t=this;if(!this.initialized){var e=this.tf;if(a.root.EditTable)this._setAdvancedGrid();else{var n=this.vendorPath+this.filename;e.import(this.filename,n,function(){return t._setAdvancedGrid()})}this.loadStylesheet&&!e.isImported(this.stylesheet,"link")&&e.import(this.stylesheetName,this.stylesheet,null,"link"),this.emitter.on(["filter-focus","filter-blur"],function(){return t._toggleForInputFilter()}),this.initialized=!0}}},{key:"_setAdvancedGrid",value:function _setAdvancedGrid(){var t,b=this.tf,e=this.cfg;t=0<Object(f.tag)(b.dom(),"thead").length&&!e.startRow?void 0:e.startRow||b.refRow,e.base_path=e.base_path||b.basePath+"ezEditTable/";var n=e.editable,r=e.selection;r&&(e.default_selection=e.default_selection||"row"),e.active_cell_css=e.active_cell_css||"ezETSelectedCell";var y=0,m=0;if(r){var o=function onAfterSelection(o,i,t){function va(t){if("row"===o.defaultSelection)s.SelectRowByIndex(t);else{o.ClearSelections();var e=i.cellIndex,n=b.dom().rows[t];"both"===o.defaultSelection&&s.SelectRowByIndex(t),n&&s.SelectCell(n.cells[e])}if(b.validRowsIndex.length!==b.getRowsNb()){var r=b.dom().rows[t];r&&r.scrollIntoView(!1),l&&(l.cellIndex===b.getCellsNb()-1&&b.gridLayout?b.tblCont.scrollLeft=1e8:0===l.cellIndex&&b.gridLayout?b.tblCont.scrollLeft=0:l.scrollIntoView(!1))}}var s=o.Selection;if(b.validRowsIndex){var e,n=b.validRowsIndex,r=n.length,a="row"!==o.defaultSelection?i.parentNode:i,l="TD"===i.nodeName?i:null,c=void 0!==t?o.Event.GetKey(t):0,u=-1!==n.indexOf(a.rowIndex),d=b.feature("paging"),f=34===c||33===c?d&&d.pageLength||o.nbRowsPerPage:1;if(u)34!==c&&33!==c?(y=n.indexOf(a.rowIndex),m=a.rowIndex):(e=34===c?y+f<=r-1?n[y+f]:[r-1]:y-f<=n[0]?n[0]:n[y-f],m=e,y=n.indexOf(e),va(e));else{if(a.rowIndex>m)if(a.rowIndex>=n[r-1])e=n[r-1];else{var h=y+f;e=r-1<h?n[r-1]:n[h]}else if(a.rowIndex<=n[0])e=n[0];else{var p=n[y-f];e=p||n[0]}m=a.rowIndex,va(e)}}},i=function onBeforeSelection(t,e){var n="row"!==t.defaultSelection?e.parentNode:e;if(b.paging&&1<b.feature("paging").nbPages){var r=b.feature("paging");t.nbRowsPerPage=r.pageLength;var o=b.validRowsIndex,i=o.length,s=parseInt(r.startPagingRow,10)+parseInt(r.pageLength,10),a=n.rowIndex;a===o[i-1]&&r.currentPageNb!==r.nbPages?r.setPage("last"):a===o[0]&&1!==r.currentPageNb?r.setPage("first"):a>o[s-1]&&a<o[i-1]?r.setPage("next"):a<o[r.startPagingRow]&&a>o[0]&&r.setPage("previous")}};if(b.paging&&(b.feature("paging").onAfterChangePage=function(t){var e=t.tf.extension("advancedGrid")._ezEditTable.Selection,n=e.GetActiveRow();n&&n.scrollIntoView(!1);var r=e.GetActiveCell();r&&r.scrollIntoView(!1)}),"row"===e.default_selection){var s=e.on_before_selected_row;e.on_before_selected_row=function(){var t=arguments;i(t[0],t[1]),s&&s.call(null,t[0],t[1],t[2])};var a=e.on_after_selected_row;e.on_after_selected_row=function(){var t=arguments;o(t[0],t[1],t[2]),a&&a.call(null,t[0],t[1],t[2])}}else{var l=e.on_before_selected_cell;e.on_before_selected_cell=function(){var t=arguments;i(t[0],t[1]),l&&l.call(null,t[0],t[1],t[2])};var c=e.on_after_selected_cell;e.on_after_selected_cell=function(){var t=arguments;o(t[0],t[1],t[2]),c&&c.call(null,t[0],t[1],t[2])}}}if(n){var u=e.on_added_dom_row;if(e.on_added_dom_row=function(){var t=arguments;b.nbFilterableRows++,b.paging?(b.nbFilterableRows++,b.paging=!1,b.feature("paging").destroy(),b.feature("paging").reset()):b.emitter.emit("rows-changed",b,this),b.alternateRows&&b.feature("alternateRows").init(),u&&u.call(null,t[0],t[1],t[2])},e.actions&&e.actions.delete){var d=e.actions.delete.on_after_submit;e.actions.delete.on_after_submit=function(){var t=arguments;b.nbFilterableRows--,b.paging?(b.nbFilterableRows--,b.paging=!1,b.feature("paging").destroy(),b.feature("paging").reset(!1)):b.emitter.emit("rows-changed",b,this),b.alternateRows&&b.feature("alternateRows").init(),d&&d.call(null,t[0],t[1])}}}try{this._ezEditTable=new EditTable(b.id,e,t),this._ezEditTable.Init()}catch(t){throw new Error('Failed to instantiate EditTable object.\n    \n"ezEditTable" dependency not found.')}this.initialized=!0}},{key:"reset",value:function reset(){var t=this._ezEditTable;t&&(this.cfg.selection&&t.Selection.Set(),this.cfg.editable&&t.Editable.Set())}},{key:"toggle",value:function toggle(){var t=this._ezEditTable;t.editable?t.Editable.Remove():t.Editable.Set(),t.selection?t.Selection.Remove():t.Selection.Set()}},{key:"_toggleForInputFilter",value:function _toggleForInputFilter(){var t=this.tf;if(t.getActiveFilterId()){var e=t.getColumnIndexFromFilterId(t.getActiveFilterId());t.getFilterType(e)===i.INPUT&&this.toggle()}}},{key:"destroy",value:function destroy(){var t=this;if(this.initialized){var e=this._ezEditTable;e&&(this.cfg.selection&&(e.Selection.ClearSelections(),e.Selection.Remove()),this.cfg.editable&&e.Editable.Remove()),this.emitter.off(["filter-focus","filter-blur"],function(){return t._toggleForInputFilter()}),this.initialized=!1}}}]),AdapterEzEditTable}();r.meta={altName:"advancedGrid"}},442:function(t,e,n){"use strict";n.r(e),n.d(e,"default",function(){return r});var o=n(10),a=n(3),c=n(2),u=n(5),i=n(22),d=n(4),s=n(1);function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function _typeof(t){return typeof t}:function _typeof(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _defineProperties(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function _setPrototypeOf(t,e){return(_setPrototypeOf=Object.setPrototypeOf||function _setPrototypeOf(t,e){return t.__proto__=e,t})(t,e)}function _createSuper(r){var o=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(t){return!1}}();return function _createSuperInternal(){var t,e=_getPrototypeOf(r);if(o){var n=_getPrototypeOf(this).constructor;t=Reflect.construct(e,arguments,n)}else t=e.apply(this,arguments);return function _possibleConstructorReturn(t,e){return!e||"object"!==_typeof(e)&&"function"!=typeof e?function _assertThisInitialized(t){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(t):e}(this,t)}}function _getPrototypeOf(t){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf:function _getPrototypeOf(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var r=function(){!function _inherits(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&_setPrototypeOf(t,e)}(AdapterSortableTable,o["Feature"]);var r=_createSuper(AdapterSortableTable);function AdapterSortableTable(t,e){var n;return function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,AdapterSortableTable),(n=r.call(this,t,AdapterSortableTable)).name=e.name,n.desc=Object(s.defaultsStr)(e.description,"Sortable table"),n.sorted=!1,n.sortTypes=Object(s.defaultsArr)(e.types,t.colTypes),n.sortColAtStart=Object(s.defaultsArr)(e.sort_col_at_start,null),n.asyncSort=Boolean(e.async_sort),n.triggerIds=Object(s.defaultsArr)(e.trigger_ids,[]),n.imgPath=Object(s.defaultsStr)(e.images_path,t.themesPath),n.imgBlank=Object(s.defaultsStr)(e.image_blank,"blank.png"),n.imgClassName=Object(s.defaultsStr)(e.image_class_name,"sort-arrow"),n.imgAscClassName=Object(s.defaultsStr)(e.image_asc_class_name,"ascending"),n.imgDescClassName=Object(s.defaultsStr)(e.image_desc_class_name,"descending"),n.customKey=Object(s.defaultsStr)(e.custom_key,"data-tf-sortKey"),n.onSortLoaded=Object(s.defaultsFn)(e.on_sort_loaded,a.EMPTY_FN),n.onBeforeSort=Object(s.defaultsFn)(e.on_before_sort,a.EMPTY_FN),n.onAfterSort=Object(s.defaultsFn)(e.on_after_sort,a.EMPTY_FN),n.stt=null,n.enable(),n}return function _createClass(t,e,n){return e&&_defineProperties(t.prototype,e),n&&_defineProperties(t,n),t}(AdapterSortableTable,[{key:"init",value:function init(){if(!this.initialized){var e=this.tf,n=this;if(Object(a.isUndef)(SortableTable))throw new Error("SortableTable class not found.");this.emitter.emit("add-date-type-formats",this.tf,this.sortTypes),this.overrideSortableTable(),this.setSortTypes(),this.onSortLoaded(e,this),this.stt.onbeforesort=function(){n.onBeforeSort(e,n.stt.sortColumn),e.paging&&e.feature("paging").disable()},this.stt.onsort=function(){if(n.sorted=!0,e.paging){var t=e.feature("paging");e.getValidRows(!0),t.enable(),t.setPage(t.getPage())}n.onAfterSort(e,n.stt.sortColumn,n.stt.descending),n.emitter.emit("column-sorted",e,n.stt.sortColumn,n.stt.descending)};var t=n.sortColAtStart;t&&this.stt.sort(t[0],t[1]),this.emitter.on(["sort"],Object(u.bound)(this.sortByColumnIndexHandler,this)),this.initialized=!0,this.emitter.emit("sort-initialized",e,this)}}},{key:"sortByColumnIndex",value:function sortByColumnIndex(t,e){this.stt.sort(t,e)}},{key:"sortByColumnIndexHandler",value:function sortByColumnIndexHandler(t,e,n){this.sortByColumnIndex(e,n)}},{key:"overrideSortableTable",value:function overrideSortableTable(){var l=this,a=this.tf;SortableTable.prototype.headerOnclick=function(t){if(l.initialized){for(var e=t.target||t.srcElement;e.tagName!==d.CELL_TAG&&e.tagName!==d.HEADER_TAG;)e=e.parentNode;this.sort(SortableTable.msie?SortableTable.getCellIndex(e):e.cellIndex)}},SortableTable.getCellIndex=function(t){var e,n=t.parentNode.cells,r=n.length;for(e=0;n[e]!==t&&e<r;e++);return e},SortableTable.prototype.initHeader=function(t){var e=this;if(!e.tHead){if(!a.gridLayout)return;e.tHead=a.feature("gridLayout").headTbl.tHead}e.headersRow=a.headersRow;var n=e.tHead.rows[e.headersRow].cells;e.sortTypes=t||[];for(var r,o,i=n.length,s=0;s<i;s++)o=n[s],null!==e.sortTypes[s]&&"None"!==e.sortTypes[s]?(o.style.cursor="pointer",r=Object(c.createElm)("img",["src",l.imgPath+l.imgBlank]),o.appendChild(r),null!==e.sortTypes[s]&&o.setAttribute("_sortType",e.sortTypes[s]),Object(u.addEvt)(o,"click",e._headerOnclick)):(o.setAttribute("_sortType",t[s]),o._sortType="None");e.updateHeaderArrows()},SortableTable.prototype.updateHeaderArrows=function(){var t,e,n;if(l.asyncSort&&0<l.triggerIds.length){var r=l.triggerIds;t=[],e=r.length;for(var o=0;o<e;o++)t.push(Object(c.elm)(r[o]))}else{if(!this.tHead)return;e=(t=this.tHead.rows[this.headersRow].cells).length}for(var i=0;i<e;i++){var s=t[i];if(s){var a=s.getAttribute("_sortType");null!==a&&"None"!==a&&("img"!==(n=s.lastChild||s).nodeName.toLowerCase()&&(n=Object(c.createElm)("img",["src",l.imgPath+l.imgBlank]),s.appendChild(n)),i===this.sortColumn?n.className=l.imgClassName+" "+(this.descending?l.imgDescClassName:l.imgAscClassName):n.className=l.imgClassName)}}},SortableTable.prototype.getRowValue=function(t,e,n){var r=this._sortTypeInfo[e];if(r&&r.getRowValue)return r.getRowValue(t,n);var o=t.cells[n],i=SortableTable.getInnerText(o);return this.getValueFromString(i,e)},SortableTable.getInnerText=function(t){if(t)return t.getAttribute(l.customKey)?t.getAttribute(l.customKey):a.getCellValue(t)}}},{key:"addSortType",value:function addSortType(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var r=e[0],o=e[1],i=e[2],s=e[3];SortableTable.prototype.addSortType(r,o,i,s)}},{key:"setSortTypes",value:function setSortTypes(){var r=this,o=this.tf,i=this.sortTypes,s=[];o.eachCol(function(t){var e;if(i[t])if(e=i[t],Object(a.isObj)(e)){if(e.type===d.DATE)e=r._addDateType(t,i);else if(e.type===d.FORMATTED_NUMBER){var n=e.decimal||o.decimalSeparator;e=r._addNumberType(t,n)}}else(e=e.toLowerCase())===d.DATE?e=r._addDateType(t,i):e===d.FORMATTED_NUMBER||e===d.NUMBER?e=r._addNumberType(t,o.decimalSeparator):e===d.NONE&&(e="None");else e=d.STRING;s.push(e)}),this.addSortType("caseinsensitivestring",SortableTable.toUpperCase),this.addSortType(d.STRING),this.addSortType(d.IP_ADDRESS,ipAddress,sortIP),this.stt=new SortableTable(o.dom(),s),this.asyncSort&&0<this.triggerIds.length&&function(){for(var n=r.triggerIds,t=0;t<n.length;t++)if(null!==n[t]){var e=Object(c.elm)(n[t]);e&&(e.style.cursor="pointer",Object(u.addEvt)(e,"click",function(t){var e=t.target;r.tf.sort&&r.stt.asyncSort(n.indexOf(e.id))}),e.setAttribute("_sortType",s[t]))}}()}},{key:"_addDateType",value:function _addDateType(t,e){var n=this.tf,r=n.feature("dateType"),o=r.getOptions(t,e).locale||n.locale,i="".concat(d.DATE,"-").concat(o);return this.addSortType(i,function(t){var e=r.parse(t,o);return isNaN(+e)?new Date(-864e11):e}),i}},{key:"_addNumberType",value:function _addNumberType(t,e){var n="".concat(d.FORMATTED_NUMBER).concat("."===e?"":"-custom");return this.addSortType(n,function(t){return Object(i.parse)(t,e)}),n}},{key:"destroy",value:function destroy(){if(this.initialized){var t=this.tf;this.emitter.off(["sort"],Object(u.bound)(this.sortByColumnIndexHandler,this)),this.sorted=!1,this.stt.destroy();for(var e=t.getFiltersId(),n=0;n<e.length;n++){var r=t.getHeaderElement(n),o=Object(c.tag)(r,"img");1===o.length&&r.removeChild(o[0])}this.initialized=!1}}}]),AdapterSortableTable}();function ipAddress(t){var e=t.split(".");for(var n in e){for(var r=e[n];r.length<3;)r="0"+r;e[n]=r}return e.join(".")}function sortIP(t,e){var n=ipAddress(t.value.toLowerCase()),r=ipAddress(e.value.toLowerCase());return n===r?0:n<r?-1:1}r.meta={altName:"sort"}},443:function(t,e,n){"use strict";n.r(e);var r=n(441);e.default=r.default},444:function(t,e,n){"use strict";n.r(e),n.d(e,"default",function(){return u});var o=n(10),h=n(2),C=n(3),s=n(31),i=n(4),r=n(448),p=n.n(r),a=n(1),l=n(5);function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function _typeof(t){return typeof t}:function _typeof(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _defineProperties(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function _setPrototypeOf(t,e){return(_setPrototypeOf=Object.setPrototypeOf||function _setPrototypeOf(t,e){return t.__proto__=e,t})(t,e)}function _createSuper(r){var o=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(t){return!1}}();return function _createSuperInternal(){var t,e=_getPrototypeOf(r);if(o){var n=_getPrototypeOf(this).constructor;t=Reflect.construct(e,arguments,n)}else t=e.apply(this,arguments);return function _possibleConstructorReturn(t,e){return!e||"object"!==_typeof(e)&&"function"!=typeof e?function _assertThisInitialized(t){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(t):e}(this,t)}}function _getPrototypeOf(t){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf:function _getPrototypeOf(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var c=["after-filtering","after-page-change","after-page-length-change"],u=function(){!function _inherits(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&_setPrototypeOf(t,e)}(ColOps,o["Feature"]);var r=_createSuper(ColOps);function ColOps(t,e){var n;return function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,ColOps),(n=r.call(this,t,ColOps)).onBeforeOperation=Object(a.defaultsFn)(e.on_before_operation,C.EMPTY_FN),n.onAfterOperation=Object(a.defaultsFn)(e.on_after_operation,C.EMPTY_FN),n.opts=e,n.labelIds=Object(a.defaultsArr)(e.id,[]),n.colIndexes=Object(a.defaultsArr)(e.col,[]),n.operations=Object(a.defaultsArr)(e.operation,[]),n.outputTypes=Object(a.defaultsArr)(e.write_method,[]),n.formatResults=Object(a.defaultsArr)(e.format_result,[]),n.totRowIndexes=Object(a.defaultsArr)(e.tot_row_index,[]),n.excludeRows=Object(a.defaultsArr)(e.exclude_row,[]),n.decimalPrecisions=Object(a.defaultsArr)(e.decimal_precision,2),n.enable(),n}return function _createClass(t,e,n){return e&&_defineProperties(t.prototype,e),n&&_defineProperties(t,n),t}(ColOps,[{key:"init",value:function init(){this.initialized||(this.emitter.on(c,Object(l.bound)(this.calcAll,this)),this.calcAll(),this.initialized=!0)}},{key:"calcAll",value:function calcAll(){var t=this.tf;if(t.isInitialized()){this.onBeforeOperation(t,this),this.emitter.emit("before-column-operation",t,this);var e=this.colIndexes,n=this.operations,r=this.outputTypes,o=this.totRowIndexes,i=this.excludeRows,s=this.formatResults,a=this.decimalPrecisions,l=[];e.forEach(function(t){-1===l.indexOf(t)&&l.push(t)});for(var c=l.length,u=t.dom().rows,d=[],f=0;f<c;f++){d.push(t.getVisibleColumnData(l[f],!1,i));for(var h=d[f],p=0,b=[],y=[],m=[],g=void 0,v=[],_=0,T=0;T<e.length;T++)e[T]===l[f]&&(b[_]=(n[T]||"sum").toLowerCase(),y[_]=a[T],m[_]=this.labelIds[T],g=Object(C.isArray)(r)?r[T]:null,v[_]=this.configureFormat(l[f],s[T]),_++);for(var S=0;S<_;S++)this.emitter.emit("before-column-calc",t,this,l[f],h,b[S],y[S]),p=Number(this.calc(h,b[S],null)),this.emitter.emit("column-calc",t,this,l[f],p,b[S],y[S]),this.writeResult(p,m[S],g,y[S],v[S]);var O=o&&o[f]?u[o[f]]:null;O&&(O.style.display="")}this.onAfterOperation(t,this),this.emitter.emit("after-column-operation",t,this)}}},{key:"columnCalc",value:function columnCalc(t,e,n){var r=1<arguments.length&&void 0!==e?e:"sum",o=2<arguments.length?n:void 0,i=this.excludeRows||[],s=tf.getVisibleColumnData(t,!1,i);return Number(this.calc(s,r,o))}},{key:"calc",value:function calc(t,e,n){var r=1<arguments.length&&void 0!==e?e:"sum",o=2<arguments.length?n:void 0,i=0;switch("q1"!==r&&"q3"!==r&&"median"!==r||(t=this.sortColumnValues(t,s.numSortAsc)),r){case"mean":i=this.calcMean(t);break;case"sum":i=this.calcSum(t);break;case"min":i=this.calcMin(t);break;case"max":i=this.calcMax(t);break;case"median":i=this.calcMedian(t);break;case"q1":i=this.calcQ1(t);break;case"q3":i=this.calcQ3(t)}return Object(C.isEmpty)(o)?i:i.toFixed(o)}},{key:"calcSum",value:function calcSum(t){var e=0<arguments.length&&void 0!==t?t:[];return Object(C.isEmpty)(e)?0:e.reduce(function(t,e){return Number(t)+Number(e)})}},{key:"calcMean",value:function calcMean(t){var e=0<arguments.length&&void 0!==t?t:[],n=this.calcSum(e)/e.length;return Number(n)}},{key:"calcMax",value:function calcMax(t){var e=0<arguments.length&&void 0!==t?t:[];return Math.max.apply(null,e)}},{key:"calcMin",value:function calcMin(t){var e=0<arguments.length&&void 0!==t?t:[];return Math.min.apply(null,e)}},{key:"calcMedian",value:function calcMedian(t){var e=0<arguments.length&&void 0!==t?t:[],n=e.length,r=0;return n%2==1?(r=Math.floor(n/2),Number(e[r])):(Number(e[n/2])+Number(e[n/2-1]))/2}},{key:"calcQ1",value:function calcQ1(t){var e,n=0<arguments.length&&void 0!==t?t:[],r=n.length;return 4*(e=Math.floor(r/4))===r?(Number(n[e-1])+Number(n[e]))/2:Number(n[e])}},{key:"calcQ3",value:function calcQ3(t){var e,n=0<arguments.length&&void 0!==t?t:[],r=n.length,o=0;return 4*(e=Math.floor(r/4))===r?(o=3*e,(Number(n[o])+Number(n[o-1]))/2):Number(n[r-e-1])}},{key:"sortColumnValues",value:function sortColumnValues(t,e){var n=1<arguments.length?e:void 0;return(0<arguments.length&&void 0!==t?t:[]).sort(n)}},{key:"writeResult",value:function writeResult(t,e,n,r,o){var i=0<arguments.length&&void 0!==t?t:0,s=1<arguments.length?e:void 0,a=2<arguments.length&&void 0!==n?n:"innerhtml",l=3<arguments.length&&void 0!==r?r:2,c=4<arguments.length&&void 0!==o?o:{},u=Object(h.elm)(s);if(u)switch(i=i.toFixed(l),i=isNaN(i)||!isFinite(i)?"":p()(c)(i),a.toLowerCase()){case"innerhtml":u.innerHTML=i;break;case"setvalue":u.value=i;break;case"createtextnode":var d=u.firstChild,f=Object(h.createText)(i);u.replaceChild(f,d)}}},{key:"configureFormat",value:function configureFormat(t,e){var n=1<arguments.length&&void 0!==e?e:{},r=this.tf;if(r.hasType(t,[i.FORMATTED_NUMBER])){var o=r.colTypes[t];o.decimal&&!n.decimal&&(n.decimal=o.decimal),o.thousands&&!n.integerSeparator&&(n.integerSeparator=o.thousands)}else n.decimal=n.decimal||"",n.integerSeparator=n.integerSeparator||"";return n}},{key:"destroy",value:function destroy(){this.initialized&&(this.emitter.off(c,Object(l.bound)(this.calcAll,this)),this.initialized=!1)}}]),ColOps}()},445:function(t,e,n){"use strict";n.r(e),n.d(e,"default",function(){return o});var r=n(10),b=n(2),i=n(3),y=n(5),s=n(9),a=n(4),l=n(1),c=n(18);function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function _typeof(t){return typeof t}:function _typeof(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _defineProperties(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function _setPrototypeOf(t,e){return(_setPrototypeOf=Object.setPrototypeOf||function _setPrototypeOf(t,e){return t.__proto__=e,t})(t,e)}function _createSuper(r){var o=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(t){return!1}}();return function _createSuperInternal(){var t,e=_getPrototypeOf(r);if(o){var n=_getPrototypeOf(this).constructor;t=Reflect.construct(e,arguments,n)}else t=e.apply(this,arguments);return function _possibleConstructorReturn(t,e){return!e||"object"!==_typeof(e)&&"function"!=typeof e?function _assertThisInitialized(t){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(t):e}(this,t)}}function _getPrototypeOf(t){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf:function _getPrototypeOf(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var o=function(){!function _inherits(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&_setPrototypeOf(t,e)}(ColsVisibility,r["Feature"]);var o=_createSuper(ColsVisibility);function ColsVisibility(t,e){var n;!function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,ColsVisibility);var r=(n=o.call(this,t,ColsVisibility)).config;return n.name=e.name,n.desc=Object(l.defaultsStr)(e.description,"Columns visibility manager"),n.spanEl=null,n.btnEl=null,n.contEl=null,n.tickToHide=Object(l.defaultsBool)(e.tick_to_hide,!0),n.manager=Object(l.defaultsBool)(e.manager,!0),n.headersTbl=e.headers_table||null,n.headersIndex=Object(l.defaultsNb)(e.headers_index,1),n.contElTgtId=Object(l.defaultsStr)(e.container_target_id,null),n.headersText=Object(l.defaultsArr)(e.headers_text,[]),n.btnTgtId=Object(l.defaultsStr)(e.btn_target_id,null),n.btnText=Object(l.defaultsStr)(e.btn_text,"Columns&#9660;"),n.btnHtml=Object(l.defaultsStr)(e.btn_html,null),n.btnCssClass=Object(l.defaultsStr)(e.btn_css_class,"colVis"),n.btnCloseText=Object(l.defaultsStr)(e.btn_close_text,"Close"),n.btnCloseHtml=Object(l.defaultsStr)(e.btn_close_html,null),n.btnCloseCssClass=Object(l.defaultsStr)(e.btn_close_css_class,n.btnCssClass),n.stylesheet=Object(l.defaultsStr)(e.stylesheet,"colsVisibility.css"),n.spanCssClass=Object(l.defaultsStr)(e.span_css_class,"colVisSpan"),n.contCssClass=Object(l.defaultsStr)(e.cont_css_class,"colVisCont"),n.listCssClass=Object(l.defaultsStr)(r.list_css_class,"cols_checklist"),n.listItemCssClass=Object(l.defaultsStr)(r.checklist_item_css_class,"cols_checklist_item"),n.listSlcItemCssClass=Object(l.defaultsStr)(r.checklist_selected_item_css_class,"cols_checklist_slc_item"),n.text=Object(l.defaultsStr)(e.text,n.tickToHide?"Hide: ":"Show: "),n.atStart=Object(l.defaultsArr)(e.at_start,[]),n.enableHover=Boolean(e.enable_hover),n.enableTickAll=Boolean(e.enable_tick_all),n.tickAllText=Object(l.defaultsStr)(e.tick_all_text,"Select all:"),n.toolbarPosition=Object(l.defaultsStr)(e.toolbar_position,c.RIGHT),n.hiddenCols=[],n.boundMouseup=null,n.onLoaded=Object(l.defaultsFn)(e.on_loaded,i.EMPTY_FN),n.onBeforeOpen=Object(l.defaultsFn)(e.on_before_open,i.EMPTY_FN),n.onAfterOpen=Object(l.defaultsFn)(e.on_after_open,i.EMPTY_FN),n.onBeforeClose=Object(l.defaultsFn)(e.on_before_close,i.EMPTY_FN),n.onAfterClose=Object(l.defaultsFn)(e.on_after_close,i.EMPTY_FN),n.onBeforeColHidden=Object(l.defaultsFn)(e.on_before_col_hidden,i.EMPTY_FN),n.onAfterColHidden=Object(l.defaultsFn)(e.on_after_col_hidden,i.EMPTY_FN),n.onBeforeColDisplayed=Object(l.defaultsFn)(e.on_before_col_displayed,i.EMPTY_FN),n.onAfterColDisplayed=Object(l.defaultsFn)(e.on_after_col_displayed,i.EMPTY_FN),t.gridLayout&&(n.headersTbl=t.feature("gridLayout").headTbl,n.headersIndex=0),t.import(e.name+"Style",t.getStylePath()+n.stylesheet,null,"link"),n.enable(),n}return function _createClass(t,e,n){return e&&_defineProperties(t.prototype,e),n&&_defineProperties(t,n),t}(ColsVisibility,[{key:"onMouseup",value:function onMouseup(t){for(var e=Object(y.targetEvt)(t);e&&e!==this.contEl&&e!==this.btnEl;)e=e.parentNode;e!==this.contEl&&e!==this.btnEl&&this.toggle()}},{key:"toggle",value:function toggle(){Object(y.removeEvt)(s.root,"mouseup",this.boundMouseup);var t=this.contEl.style.display;"inline"!==t&&this.onBeforeOpen(this),"inline"===t&&this.onBeforeClose(this),this.contEl.style.display="inline"===t?a.NONE:"inline","inline"!==t&&(this.onAfterOpen(this),Object(y.addEvt)(s.root,"mouseup",this.boundMouseup)),"inline"===t&&this.onAfterClose(this)}},{key:"checkItem",value:function checkItem(t){var e=t.parentNode;if(e&&t){var n=t.firstChild.checked,r=t.firstChild.getAttribute("id").split("_")[1];r=parseInt(r,10),n?Object(b.addClass)(e,this.listSlcItemCssClass):Object(b.removeClass)(e,this.listSlcItemCssClass);var o=!1;(this.tickToHide&&n||!this.tickToHide&&!n)&&(o=!0),this.setHidden(r,o)}}},{key:"init",value:function init(){var n=this;!this.initialized&&this.manager&&(this.emitter.emit("initializing-extension",this,!Object(i.isNull)(this.btnTgtId)),this.emitter.on(["hide-column"],function(t,e){return n.hideCol(e)}),this.buildBtn(),this.buildManager(),this.initialized=!0,this.boundMouseup=this.onMouseup.bind(this),this.emitter.emit("columns-visibility-initialized",this.tf,this),this.emitter.emit("extension-initialized",this),this._hideAtStart())}},{key:"buildBtn",value:function buildBtn(){var e=this;if(!this.btnEl){var t=this.tf,n=Object(b.createElm)("span");n.className=this.spanCssClass;var r=this.btnTgtId?Object(b.elm)(this.btnTgtId):t.feature("toolbar").container(this.toolbarPosition);if(this.btnTgtId)r.appendChild(n);else{var o=r.firstChild;o.parentNode.insertBefore(n,o)}if(this.btnHtml){n.innerHTML=this.btnHtml;var i=n.firstChild;this.enableHover?Object(y.addEvt)(i,"mouseover",function(t){return e.toggle(t)}):Object(y.addEvt)(i,"click",function(t){return e.toggle(t)})}else{var s=Object(b.createElm)("a",["href","javascript:;"]);s.className=this.btnCssClass,s.title=this.desc,s.innerHTML=this.btnText,n.appendChild(s),this.enableHover?Object(y.addEvt)(s,"mouseover",function(t){return e.toggle(t)}):Object(y.addEvt)(s,"click",function(t){return e.toggle(t)})}this.spanEl=n,this.btnEl=this.spanEl.firstChild,this.onLoaded(this)}}},{key:"buildManager",value:function buildManager(){var n=this,r=this.tf,t=this.contElTgtId?Object(b.elm)(this.contElTgtId):Object(b.createElm)("div");t.className=this.contCssClass;var e=Object(b.createElm)("p");e.innerHTML=this.text,t.appendChild(e);var o=Object(b.createElm)("ul");o.className=this.listCssClass;var i=this.headersTbl||r.dom(),s=this.headersTbl?this.headersIndex:r.getHeadersRowIndex(),a=i.rows[s];if(this.enableTickAll){var l=Object(b.createCheckItem)("col__"+r.id,this.tickAllText,this.tickAllText);Object(b.addClass)(l,this.listItemCssClass),o.appendChild(l),l.check.checked=!this.tickToHide,Object(y.addEvt)(l.check,"click",function(){for(var t=0;t<a.cells.length;t++){var e=Object(b.elm)("col_"+t+"_"+r.id);e&&l.check.checked!==e.checked&&(e.click(),e.checked=l.check.checked)}})}for(var c=0;c<a.cells.length;c++){var u=a.cells[c],d=this.headersText[c]||this._getHeaderText(u),f=Object(b.createCheckItem)("col_"+c+"_"+r.id,d,d);Object(b.addClass)(f,this.listItemCssClass),this.tickToHide||Object(b.addClass)(f,this.listSlcItemCssClass),o.appendChild(f),this.tickToHide||(f.check.checked=!0),Object(y.addEvt)(f.check,"click",function(t){var e=Object(y.targetEvt)(t).parentNode;n.checkItem(e)})}var h,p=Object(b.createElm)("p",["align","center"]);this.btnCloseHtml?(p.innerHTML=this.btnCloseHtml,h=p.firstChild,Object(y.addEvt)(h,"click",function(t){return n.toggle(t)})):((h=Object(b.createElm)("a",["href","javascript:;"])).className=this.btnCloseCssClass,h.innerHTML=this.btnCloseText,Object(y.addEvt)(h,"click",function(t){return n.toggle(t)}),p.appendChild(h)),t.appendChild(o),t.appendChild(p),this.btnEl.parentNode.insertBefore(t,this.btnEl),this.contEl=t}},{key:"setHidden",value:function setHidden(t,e){var n=this.tf,r=n.dom();e?this.onBeforeColHidden(this,t):this.onBeforeColDisplayed(this,t),this._hideElements(r,t,e),this.headersTbl&&this._hideElements(this.headersTbl,t,e);var o=this.hiddenCols.indexOf(t);e?-1===o&&this.hiddenCols.push(t):-1!==o&&this.hiddenCols.splice(o,1),e?(this.onAfterColHidden(this,t),this.emitter.emit("column-hidden",n,this,t,this.hiddenCols)):(this.onAfterColDisplayed(this,t),this.emitter.emit("column-shown",n,this,t,this.hiddenCols))}},{key:"showCol",value:function showCol(t){if(!Object(i.isUndef)(t)&&this.isColHidden(t))if(this.manager&&this.contEl){var e=Object(b.elm)("col_"+t+"_"+this.tf.id);e&&e.click()}else this.setHidden(t,!1)}},{key:"hideCol",value:function hideCol(t){if(!Object(i.isUndef)(t)&&!this.isColHidden(t))if(this.manager&&this.contEl){var e=Object(b.elm)("col_"+t+"_"+this.tf.id);e&&e.click()}else this.setHidden(t,!0)}},{key:"isColHidden",value:function isColHidden(t){return-1!==this.hiddenCols.indexOf(t)}},{key:"toggleCol",value:function toggleCol(t){Object(i.isUndef)(t)||this.isColHidden(t)?this.showCol(t):this.hideCol(t)}},{key:"getHiddenCols",value:function getHiddenCols(){return this.hiddenCols}},{key:"destroy",value:function destroy(){var n=this;this.initialized&&(Object(b.elm)(this.contElTgtId)?Object(b.elm)(this.contElTgtId).innerHTML="":(this.contEl.innerHTML="",Object(b.removeElm)(this.contEl),this.contEl=null),this.btnEl.innerHTML="",Object(b.removeElm)(this.btnEl),this.btnEl=null,this.emitter.off(["hide-column"],function(t,e){return n.hideCol(e)}),this.boundMouseup=null,this.initialized=!1)}},{key:"_getHeaderText",value:function _getHeaderText(t){if(!t.hasChildNodes)return"";for(var e=0;e<t.childNodes.length;e++){var n=t.childNodes[e];if(3===n.nodeType)return n.nodeValue;if(1===n.nodeType){if(n.id&&-1!==n.id.indexOf("popUp"))continue;return Object(b.getText)(n)}}return""}},{key:"_hideElements",value:function _hideElements(t,e,n){this._hideCells(t,e,n),this._hideCol(t,e,n)}},{key:"_hideCells",value:function _hideCells(t,e,n){for(var r=0;r<t.rows.length;r++){var o=t.rows[r].cells[e];o&&(o.style.display=n?a.NONE:"")}}},{key:"_hideCol",value:function _hideCol(t,e,n){var r=Object(b.tag)(t,"col");0!==r.length&&(r[e].style.display=n?a.NONE:"")}},{key:"_hideAtStart",value:function _hideAtStart(){var e=this;this.atStart.forEach(function(t){e.hideCol(t)})}}]),ColsVisibility}()},446:function(t,e,n){"use strict";n.r(e),n.d(e,"default",function(){return r});var o=n(10),s=n(2),i=n(3),a=n(5),l=n(1),c=n(18);function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function _typeof(t){return typeof t}:function _typeof(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _defineProperties(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function _setPrototypeOf(t,e){return(_setPrototypeOf=Object.setPrototypeOf||function _setPrototypeOf(t,e){return t.__proto__=e,t})(t,e)}function _createSuper(r){var o=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(t){return!1}}();return function _createSuperInternal(){var t,e=_getPrototypeOf(r);if(o){var n=_getPrototypeOf(this).constructor;t=Reflect.construct(e,arguments,n)}else t=e.apply(this,arguments);return function _possibleConstructorReturn(t,e){return!e||"object"!==_typeof(e)&&"function"!=typeof e?function _assertThisInitialized(t){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(t):e}(this,t)}}function _getPrototypeOf(t){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf:function _getPrototypeOf(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var r=function(){!function _inherits(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&_setPrototypeOf(t,e)}(FiltersVisibility,o["Feature"]);var r=_createSuper(FiltersVisibility);function FiltersVisibility(t,e){var n;return function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,FiltersVisibility),(n=r.call(this,t,FiltersVisibility)).name=e.name,n.desc=Object(l.defaultsStr)(e.description,"Filters row visibility manager"),n.stylesheet=Object(l.defaultsStr)(e.stylesheet,"filtersVisibility.css"),n.icnExpand=Object(l.defaultsStr)(e.expand_icon_name,"icn_exp.png"),n.icnCollapse=Object(l.defaultsStr)(e.collapse_icon_name,"icn_clp.png"),n.contEl=null,n.btnEl=null,n.icnExpandHtml='<img src="'+t.themesPath+n.icnExpand+'" alt="Expand filters" >',n.icnCollapseHtml='<img src="'+t.themesPath+n.icnCollapse+'" alt="Collapse filters" >',n.defaultText="Toggle filters",n.targetId=e.target_id||null,n.enableIcon=Object(l.defaultsBool)(e.enable_icon,!0),n.btnText=Object(l.defaultsStr)(e.btn_text,""),n.collapseBtnHtml=n.enableIcon?n.icnCollapseHtml+n.btnText:n.btnText||n.defaultText,n.expandBtnHtml=n.enableIcon?n.icnExpandHtml+n.btnText:n.btnText||n.defaultText,n.btnHtml=Object(l.defaultsStr)(e.btn_html,null),n.btnCssClass=Object(l.defaultsStr)(e.btn_css_class,"btnExpClpFlt"),n.contCssClass=Object(l.defaultsStr)(e.cont_css_class,"expClpFlt"),n.filtersRowIndex=Object(l.defaultsNb)(e.filters_row_index,t.getFiltersRowIndex()),n.visibleAtStart=Object(l.defaultsNb)(e.visible_at_start,!0),n.toolbarPosition=Object(l.defaultsStr)(e.toolbar_position,c.RIGHT),n.onBeforeShow=Object(l.defaultsFn)(e.on_before_show,i.EMPTY_FN),n.onAfterShow=Object(l.defaultsFn)(e.on_after_show,i.EMPTY_FN),n.onBeforeHide=Object(l.defaultsFn)(e.on_before_hide,i.EMPTY_FN),n.onAfterHide=Object(l.defaultsFn)(e.on_after_hide,i.EMPTY_FN),t.import(e.name+"Style",t.getStylePath()+n.stylesheet,null,"link"),n.enable(),n}return function _createClass(t,e,n){return e&&_defineProperties(t.prototype,e),n&&_defineProperties(t,n),t}(FiltersVisibility,[{key:"init",value:function init(){var n=this;this.initialized||(this.emitter.emit("initializing-extension",this,!Object(i.isNull)(this.targetId)),this.buildUI(),this.initialized=!0,this.emitter.on(["show-filters"],function(t,e){return n.show(e)}),this.emitter.emit("filters-visibility-initialized",this.tf,this),this.emitter.emit("extension-initialized",this))}},{key:"buildUI",value:function buildUI(){var t=this,e=this.tf,n=Object(s.createElm)("span");n.className=this.contCssClass;var r,o=this.targetId?Object(s.elm)(this.targetId):e.feature("toolbar").container(this.toolbarPosition);if(this.targetId)o.appendChild(n);else{var i=o.firstChild;i.parentNode.insertBefore(n,i)}this.btnHtml?(n.innerHTML=this.btnHtml,r=n.firstChild):((r=Object(s.createElm)("a",["href","javascript:void(0);"])).className=this.btnCssClass,r.title=this.btnText||this.defaultText,r.innerHTML=this.collapseBtnHtml,n.appendChild(r)),Object(a.addEvt)(r,"click",function(){return t.toggle()}),this.contEl=n,this.btnEl=r,this.visibleAtStart||this.toggle()}},{key:"toggle",value:function toggle(){var t=this.tf,e=""===(t.gridLayout?t.feature("gridLayout").headTbl:t.dom()).rows[this.filtersRowIndex].style.display;this.show(!e)}},{key:"show",value:function show(t){var e=!(0<arguments.length&&void 0!==t)||t,n=this.tf,r=(n.gridLayout?n.feature("gridLayout").headTbl:n.dom()).rows[this.filtersRowIndex];e&&this.onBeforeShow(this),e||this.onBeforeHide(this),r.style.display=e?"":"none",this.enableIcon&&!this.btnHtml&&(this.btnEl.innerHTML=e?this.collapseBtnHtml:this.expandBtnHtml),e&&this.onAfterShow(this),e||this.onAfterHide(this),this.emitter.emit("filters-toggled",n,this,e)}},{key:"destroy",value:function destroy(){var n=this;this.initialized&&(this.emitter.off(["show-filters"],function(t,e){return n.show(e)}),this.btnEl.innerHTML="",Object(s.removeElm)(this.btnEl),this.btnEl=null,this.contEl.innerHTML="",Object(s.removeElm)(this.contEl),this.contEl=null,this.initialized=!1)}}]),FiltersVisibility}()},447:function(t,e,n){"use strict";n.r(e);var r=n(442);n(9).root.SortableTable||n(449),e.default=r.default},448:function(t,e){function formatter(o){if((o=o||{}).negativeType=o.negativeType||("R"===o.negative?"right":"left"),"string"!=typeof o.negativeLeftSymbol)switch(o.negativeType){case"left":o.negativeLeftSymbol="-";break;case"brackets":o.negativeLeftSymbol="(";break;default:o.negativeLeftSymbol=""}if("string"!=typeof o.negativeRightSymbol)switch(o.negativeType){case"right":o.negativeRightSymbol="-";break;case"brackets":o.negativeRightSymbol=")";break;default:o.negativeRightSymbol=""}function format(t,e){if(e=e||{},!t&&0!==t)return"";var n=[],r="-"===(t=""+t).charAt(0);return t=t.replace(/^\-/g,""),o.negativeLeftOut||e.noUnits||n.push(o.prefix),r&&n.push(o.negativeLeftSymbol),o.negativeLeftOut&&!e.noUnits&&n.push(o.prefix),t=t.split("."),null!=o.round&&function round(t,e){if(t[1]&&0<=e&&t[1].length>e){var n=t[1].slice(0,e);if(5<=+t[1].substr(e,1)){for(var r="";"0"===n.charAt(0);)r+="0",n=n.substr(1);(n=r+(n=+n+1+"")).length>e&&(t[0]=+t[0]+ +n.charAt(0)+"",n=n.substring(1))}t[1]=n}return t}(t,o.round),null!=o.truncate&&(t[1]=function truncate(t,e){t&&(t+="");return t&&t.length>e?t.substr(0,e):t}(t[1],o.truncate)),0<o.padLeft&&(t[0]=function padLeft(t,e){t+="";var n=[];for(;n.length+t.length<e;)n.push("0");return n.join("")+t}(t[0],o.padLeft)),0<o.padRight&&(t[1]=function padRight(t,e){t?t+="":t="";var n=[];for(;n.length+t.length<e;)n.push("0");return t+n.join("")}(t[1],o.padRight)),!e.noSeparator&&t[1]&&(t[1]=function addDecimalSeparators(t,e){if(t+="",!e)return t;var n=/(\d{3})(\d+)/;for(;n.test(t);)t=t.replace(n,"$1"+e+"$2");return t}(t[1],o.decimalsSeparator)),!e.noSeparator&&t[0]&&(t[0]=function addIntegerSeparators(t,e){if(t+="",!e)return t;var n=/(\d+)(\d{3})/;for(;n.test(t);)t=t.replace(n,"$1"+e+"$2");return t}(t[0],o.integerSeparator)),n.push(t[0]),t[1]&&(n.push(o.decimal),n.push(t[1])),o.negativeRightOut&&!e.noUnits&&n.push(o.suffix),r&&n.push(o.negativeRightSymbol),o.negativeRightOut||e.noUnits||n.push(o.suffix),n.join("")}function unformat(t,e){e=e||[],o.allowedSeparators&&o.allowedSeparators.forEach(function(t){e.push(t)}),e.push(o.integerSeparator),e.push(o.decimalsSeparator);var n=t=(t=t.replace(o.prefix,"")).replace(o.suffix,"");do{t=n;for(var r=0;r<e.length;r++)n=n.replace(e[r],"")}while(n!=t);return t}return"boolean"!=typeof o.negativeLeftOut&&(o.negativeLeftOut=!1!==o.negativeOut),"boolean"!=typeof o.negativeRightOut&&(o.negativeRightOut=!1!==o.negativeOut),o.prefix=o.prefix||"",o.suffix=o.suffix||"","string"!=typeof o.integerSeparator&&(o.integerSeparator="string"==typeof o.separator?o.separator:","),o.decimalsSeparator="string"==typeof o.decimalsSeparator?o.decimalsSeparator:"",o.decimal=o.decimal||".",o.padLeft=o.padLeft||-1,o.padRight=o.padRight||-1,format.negative=o.negative,format.negativeOut=o.negativeOut,format.negativeType=o.negativeType,format.negativeLeftOut=o.negativeLeftOut,format.negativeLeftSymbol=o.negativeLeftSymbol,format.negativeRightOut=o.negativeRightOut,format.negativeRightSymbol=o.negativeRightSymbol,format.prefix=o.prefix,format.suffix=o.suffix,format.separate=o.separate,format.integerSeparator=o.integerSeparator,format.decimalsSeparator=o.decimalsSeparator,format.decimal=o.decimal,format.padLeft=o.padLeft,format.padRight=o.padRight,format.truncate=o.truncate,format.round=o.round,format.unformat=unformat,format}t.exports=formatter,t.exports.default=formatter},449:function(t,e,n){n(450)(n(451))},450:function(t,e){t.exports=function(t){function log(t){"undefined"!=typeof console&&(console.error||console.log)("[Script Loader]",t)}try{"undefined"!=typeof execScript&&function isIE(){return"undefined"!=typeof attachEvent&&"undefined"==typeof addEventListener}()?execScript(t):"undefined"!=typeof eval?eval.call(null,t):log("EvalError: No eval function available")}catch(t){log(t)}}},451:function(t,e){t.exports='/*----------------------------------------------------------------------------\\\r\n|                            Sortable Table 1.12                              |\r\n|-----------------------------------------------------------------------------|\r\n|                         Created by Erik Arvidsson                           |\r\n|                  (http://webfx.eae.net/contact.html#erik)                   |\r\n|                      For WebFX (http://webfx.eae.net/)                      |\r\n|-----------------------------------------------------------------------------|\r\n| A DOM 1 based script that allows an ordinary HTML table to be sortable.     |\r\n|-----------------------------------------------------------------------------|\r\n|                  Copyright (c) 1998 - 2006 Erik Arvidsson                   |\r\n|-----------------------------------------------------------------------------|\r\n| Licensed under the Apache License, Version 2.0 (the "License"); you may not |\r\n| use this file except in compliance with the License.  You may obtain a copy |\r\n| of the License at http://www.apache.org/licenses/LICENSE-2.0                |\r\n| - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - |\r\n| Unless  required  by  applicable law or  agreed  to  in  writing,  software |\r\n| distributed under the License is distributed on an  "AS IS" BASIS,  WITHOUT |\r\n| WARRANTIES OR  CONDITIONS OF ANY KIND,  either express or implied.  See the |\r\n| License  for the  specific language  governing permissions  and limitations |\r\n| under the License.                                                          |\r\n|-----------------------------------------------------------------------------|\r\n| 2003-01-10 | First version                                                  |\r\n| 2003-01-19 | Minor changes to the date parsing                              |\r\n| 2003-01-28 | JScript 5.0 fixes (no support for \'in\' operator)               |\r\n| 2003-02-01 | Sloppy typo like error fixed in getInnerText                   |\r\n| 2003-07-04 | Added workaround for IE cellIndex bug.                         |\r\n| 2003-11-09 | The bDescending argument to sort was not correctly working     |\r\n|            | Using onclick DOM0 event if no support for addEventListener    |\r\n|            | or attachEvent                                                 |\r\n| 2004-01-13 | Adding addSortType and removeSortType which makes it a lot     |\r\n|            | easier to add new, custom sort types.                          |\r\n| 2004-01-27 | Switch to use descending = false as the default sort order.    |\r\n|            | Change defaultDescending to suit your needs.                   |\r\n| 2004-03-14 | Improved sort type None look and feel a bit                    |\r\n| 2004-08-26 | Made the handling of tBody and tHead more flexible. Now you    |\r\n|            | can use another tHead or no tHead, and you can chose some      |\r\n|            | other tBody.                                                   |\r\n| 2006-04-25 | Changed license to Apache Software License 2.0                 |\r\n|-----------------------------------------------------------------------------|\r\n| Created 2003-01-10 | All changes are in the log above. | Updated 2006-04-25 |\r\n\\----------------------------------------------------------------------------*/\r\n\r\n\r\nfunction SortableTable(oTable, oSortTypes) {\r\n\r\n\tthis.sortTypes = oSortTypes || [];\r\n\r\n\tthis.sortColumn = null;\r\n\tthis.descending = null;\r\n\r\n\tvar oThis = this;\r\n\tthis._headerOnclick = function (e) {\r\n\t\toThis.headerOnclick(e);\r\n\t};\r\n\r\n\tif (oTable) {\r\n\t\tthis.setTable( oTable );\r\n\t\tthis.document = oTable.ownerDocument || oTable.document;\r\n\t}\r\n\telse {\r\n\t\tthis.document = document;\r\n\t}\r\n\r\n\r\n\t// only IE needs this\r\n\tvar win = this.document.defaultView || this.document.parentWindow;\r\n\tthis._onunload = function () {\r\n\t\toThis.destroy();\r\n\t};\r\n\tif (win && typeof win.attachEvent != "undefined") {\r\n\t\twin.attachEvent("onunload", this._onunload);\r\n\t}\r\n}\r\n\r\nSortableTable.gecko = navigator.product == "Gecko";\r\nSortableTable.msie = /msie/i.test(navigator.userAgent);\r\n// Mozilla is faster when doing the DOM manipulations on\r\n// an orphaned element. MSIE is not\r\nSortableTable.removeBeforeSort = SortableTable.gecko;\r\n\r\nSortableTable.prototype.onsort = function () {};\r\n\r\n// default sort order. true -> descending, false -> ascending\r\nSortableTable.prototype.defaultDescending = false;\r\n\r\n// shared between all instances. This is intentional to allow external files\r\n// to modify the prototype\r\nSortableTable.prototype._sortTypeInfo = {};\r\n\r\nSortableTable.prototype.setTable = function (oTable) {\r\n\tif ( this.tHead )\r\n\t\tthis.uninitHeader();\r\n\tthis.element = oTable;\r\n\tthis.setTHead( oTable.tHead );\r\n\tthis.setTBody( oTable.tBodies[0] );\r\n};\r\n\r\nSortableTable.prototype.setTHead = function (oTHead) {\r\n\tif (this.tHead && this.tHead != oTHead )\r\n\t\tthis.uninitHeader();\r\n\tthis.tHead = oTHead;\r\n\tthis.initHeader( this.sortTypes );\r\n};\r\n\r\nSortableTable.prototype.setTBody = function (oTBody) {\r\n\tthis.tBody = oTBody;\r\n};\r\n\r\nSortableTable.prototype.setSortTypes = function ( oSortTypes ) {\r\n\tif ( this.tHead )\r\n\t\tthis.uninitHeader();\r\n\tthis.sortTypes = oSortTypes || [];\r\n\tif ( this.tHead )\r\n\t\tthis.initHeader( this.sortTypes );\r\n};\r\n\r\n// adds arrow containers and events\r\n// also binds sort type to the header cells so that reordering columns does\r\n// not break the sort types\r\nSortableTable.prototype.initHeader = function (oSortTypes) {\r\n\tif (!this.tHead) return;\r\n\tvar cells = this.tHead.rows[0].cells;\r\n\tvar doc = this.tHead.ownerDocument || this.tHead.document;\r\n\tthis.sortTypes = oSortTypes || [];\r\n\tvar l = cells.length;\r\n\tvar img, c;\r\n\tfor (var i = 0; i < l; i++) {\r\n\t\tc = cells[i];\r\n\t\tif (this.sortTypes[i] != null && this.sortTypes[i] != "None") {\r\n\t\t\timg = doc.createElement("IMG");\r\n\t\t\timg.src = "images/blank.png";\r\n\t\t\tc.appendChild(img);\r\n\t\t\tif (this.sortTypes[i] != null)\r\n\t\t\t\tc._sortType = this.sortTypes[i];\r\n\t\t\tif (typeof c.addEventListener != "undefined")\r\n\t\t\t\tc.addEventListener("click", this._headerOnclick, false);\r\n\t\t\telse if (typeof c.attachEvent != "undefined")\r\n\t\t\t\tc.attachEvent("onclick", this._headerOnclick);\r\n\t\t\telse\r\n\t\t\t\tc.onclick = this._headerOnclick;\r\n\t\t}\r\n\t\telse\r\n\t\t{\r\n\t\t\tc.setAttribute( "_sortType", oSortTypes[i] );\r\n\t\t\tc._sortType = "None";\r\n\t\t}\r\n\t}\r\n\tthis.updateHeaderArrows();\r\n};\r\n\r\n// remove arrows and events\r\nSortableTable.prototype.uninitHeader = function () {\r\n\tif (!this.tHead) return;\r\n\tvar cells = this.tHead.rows[0].cells;\r\n\tvar l = cells.length;\r\n\tvar c;\r\n\tfor (var i = 0; i < l; i++) {\r\n\t\tc = cells[i];\r\n\t\tif (c._sortType != null && c._sortType != "None") {\r\n\t\t\tc.removeChild(c.lastChild);\r\n\t\t\tif (typeof c.removeEventListener != "undefined")\r\n\t\t\t\tc.removeEventListener("click", this._headerOnclick, false);\r\n\t\t\telse if (typeof c.detachEvent != "undefined")\r\n\t\t\t\tc.detachEvent("onclick", this._headerOnclick);\r\n\t\t\tc._sortType = null;\r\n\t\t\tc.removeAttribute( "_sortType" );\r\n\t\t}\r\n\t}\r\n};\r\n\r\nSortableTable.prototype.updateHeaderArrows = function () {\r\n\tif (!this.tHead) return;\r\n\tvar cells = this.tHead.rows[0].cells;\r\n\tvar l = cells.length;\r\n\tvar img;\r\n\tfor (var i = 0; i < l; i++) {\r\n\t\tif (cells[i]._sortType != null && cells[i]._sortType != "None") {\r\n\t\t\timg = cells[i].lastChild;\r\n\t\t\tif (i == this.sortColumn)\r\n\t\t\t\timg.className = "sort-arrow " + (this.descending ? "descending" : "ascending");\r\n\t\t\telse\r\n\t\t\t\timg.className = "sort-arrow";\r\n\t\t}\r\n\t}\r\n};\r\n\r\nSortableTable.prototype.headerOnclick = function (e) {\r\n\t// find TD element\r\n\tvar el = e.target || e.srcElement;\r\n\twhile (el.tagName != "TD")\r\n\t\tel = el.parentNode;\r\n\r\n\tthis.sort(SortableTable.msie ? SortableTable.getCellIndex(el) : el.cellIndex);\r\n};\r\n\r\n// IE returns wrong cellIndex when columns are hidden\r\nSortableTable.getCellIndex = function (oTd) {\r\n\tvar cells = oTd.parentNode.childNodes\r\n\tvar l = cells.length;\r\n\tvar i;\r\n\tfor (i = 0; cells[i] != oTd && i < l; i++)\r\n\t\t;\r\n\treturn i;\r\n};\r\n\r\nSortableTable.prototype.getSortType = function (nColumn) {\r\n\treturn this.sortTypes[nColumn] || "String";\r\n};\r\n\r\n// only nColumn is required\r\n// if bDescending is left out the old value is taken into account\r\n// if sSortType is left out the sort type is found from the sortTypes array\r\n\r\nSortableTable.prototype.sort = function (nColumn, bDescending, sSortType) {\r\n\tif (!this.tBody) return;\r\n\tif (sSortType == null)\r\n\t\tsSortType = this.getSortType(nColumn);\r\n\r\n\t// exit if None\r\n\tif (sSortType == "None")\r\n\t\treturn;\r\n\r\n\tif (bDescending == null) {\r\n\t\tif (this.sortColumn != nColumn)\r\n\t\t\tthis.descending = this.defaultDescending;\r\n\t\telse\r\n\t\t\tthis.descending = !this.descending;\r\n\t}\r\n\telse\r\n\t\tthis.descending = bDescending;\r\n\r\n\tthis.sortColumn = nColumn;\r\n\r\n\tif (typeof this.onbeforesort == "function")\r\n\t\tthis.onbeforesort();\r\n\r\n\tvar f = this.getSortFunction(sSortType, nColumn);\r\n\tvar a = this.getCache(sSortType, nColumn);\r\n\tvar tBody = this.tBody;\r\n\r\n\ta.sort(f);\r\n\r\n\tif (this.descending)\r\n\t\ta.reverse();\r\n\r\n\tif (SortableTable.removeBeforeSort) {\r\n\t\t// remove from doc\r\n\t\tvar nextSibling = tBody.nextSibling;\r\n\t\tvar p = tBody.parentNode;\r\n\t\tp.removeChild(tBody);\r\n\t}\r\n\r\n\t// insert in the new order\r\n\tvar l = a.length;\r\n\tfor (var i = 0; i < l; i++)\r\n\t\ttBody.appendChild(a[i].element);\r\n\r\n\tif (SortableTable.removeBeforeSort) {\r\n\t\t// insert into doc\r\n\t\tp.insertBefore(tBody, nextSibling);\r\n\t}\r\n\r\n\tthis.updateHeaderArrows();\r\n\r\n\tthis.destroyCache(a);\r\n\r\n\tif (typeof this.onsort == "function")\r\n\t\tthis.onsort();\r\n};\r\n\r\nSortableTable.prototype.asyncSort = function (nColumn, bDescending, sSortType) {\r\n\tvar oThis = this;\r\n\tthis._asyncsort = function () {\r\n\t\toThis.sort(nColumn, bDescending, sSortType);\r\n\t};\r\n\twindow.setTimeout(this._asyncsort, 1);\r\n};\r\n\r\nSortableTable.prototype.getCache = function (sType, nColumn) {\r\n\tif (!this.tBody) return [];\r\n\tvar rows = this.tBody.rows;\r\n\tvar l = rows.length;\r\n\tvar a = new Array(l);\r\n\tvar r;\r\n\tfor (var i = 0; i < l; i++) {\r\n\t\tr = rows[i];\r\n\t\ta[i] = {\r\n\t\t\tvalue:\t\tthis.getRowValue(r, sType, nColumn),\r\n\t\t\telement:\tr\r\n\t\t};\r\n\t};\r\n\treturn a;\r\n};\r\n\r\nSortableTable.prototype.destroyCache = function (oArray) {\r\n\tvar l = oArray.length;\r\n\tfor (var i = 0; i < l; i++) {\r\n\t\toArray[i].value = null;\r\n\t\toArray[i].element = null;\r\n\t\toArray[i] = null;\r\n\t}\r\n};\r\n\r\nSortableTable.prototype.getRowValue = function (oRow, sType, nColumn) {\r\n\t// if we have defined a custom getRowValue use that\r\n\tif (this._sortTypeInfo[sType] && this._sortTypeInfo[sType].getRowValue)\r\n\t\treturn this._sortTypeInfo[sType].getRowValue(oRow, nColumn);\r\n\r\n\tvar s;\r\n\tvar c = oRow.cells[nColumn];\r\n\tif (typeof c.innerText != "undefined")\r\n\t\ts = c.innerText;\r\n\telse\r\n\t\ts = SortableTable.getInnerText(c);\r\n\treturn this.getValueFromString(s, sType);\r\n};\r\n\r\nSortableTable.getInnerText = function (oNode) {\r\n\tvar s = "";\r\n\tvar cs = oNode.childNodes;\r\n\tvar l = cs.length;\r\n\tfor (var i = 0; i < l; i++) {\r\n\t\tswitch (cs[i].nodeType) {\r\n\t\t\tcase 1: //ELEMENT_NODE\r\n\t\t\t\ts += SortableTable.getInnerText(cs[i]);\r\n\t\t\t\tbreak;\r\n\t\t\tcase 3:\t//TEXT_NODE\r\n\t\t\t\ts += cs[i].nodeValue;\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t}\r\n\treturn s;\r\n};\r\n\r\nSortableTable.prototype.getValueFromString = function (sText, sType) {\r\n\tif (this._sortTypeInfo[sType])\r\n\t\treturn this._sortTypeInfo[sType].getValueFromString( sText );\r\n\treturn sText;\r\n\t/*\r\n\tswitch (sType) {\r\n\t\tcase "Number":\r\n\t\t\treturn Number(sText);\r\n\t\tcase "CaseInsensitiveString":\r\n\t\t\treturn sText.toUpperCase();\r\n\t\tcase "Date":\r\n\t\t\tvar parts = sText.split("-");\r\n\t\t\tvar d = new Date(0);\r\n\t\t\td.setFullYear(parts[0]);\r\n\t\t\td.setDate(parts[2]);\r\n\t\t\td.setMonth(parts[1] - 1);\r\n\t\t\treturn d.valueOf();\r\n\t}\r\n\treturn sText;\r\n\t*/\r\n\t};\r\n\r\nSortableTable.prototype.getSortFunction = function (sType, nColumn) {\r\n\tif (this._sortTypeInfo[sType])\r\n\t\treturn this._sortTypeInfo[sType].compare;\r\n\treturn SortableTable.basicCompare;\r\n};\r\n\r\nSortableTable.prototype.destroy = function () {\r\n\tthis.uninitHeader();\r\n\tvar win = this.document.parentWindow;\r\n\tif (win && typeof win.detachEvent != "undefined") {\t// only IE needs this\r\n\t\twin.detachEvent("onunload", this._onunload);\r\n\t}\r\n\tthis._onunload = null;\r\n\tthis.element = null;\r\n\tthis.tHead = null;\r\n\tthis.tBody = null;\r\n\tthis.document = null;\r\n\tthis._headerOnclick = null;\r\n\tthis.sortTypes = null;\r\n\tthis._asyncsort = null;\r\n\tthis.onsort = null;\r\n};\r\n\r\n// Adds a sort type to all instance of SortableTable\r\n// sType : String - the identifier of the sort type\r\n// fGetValueFromString : function ( s : string ) : T - A function that takes a\r\n//    string and casts it to a desired format. If left out the string is just\r\n//    returned\r\n// fCompareFunction : function ( n1 : T, n2 : T ) : Number - A normal JS sort\r\n//    compare function. Takes two values and compares them. If left out less than,\r\n//    <, compare is used\r\n// fGetRowValue : function( oRow : HTMLTRElement, nColumn : int ) : T - A function\r\n//    that takes the row and the column index and returns the value used to compare.\r\n//    If left out then the innerText is first taken for the cell and then the\r\n//    fGetValueFromString is used to convert that string the desired value and type\r\n\r\nSortableTable.prototype.addSortType = function (sType, fGetValueFromString, fCompareFunction, fGetRowValue) {\r\n\tthis._sortTypeInfo[sType] = {\r\n\t\ttype:\t\t\t\tsType,\r\n\t\tgetValueFromString:\tfGetValueFromString || SortableTable.idFunction,\r\n\t\tcompare:\t\t\tfCompareFunction || SortableTable.basicCompare,\r\n\t\tgetRowValue:\t\tfGetRowValue\r\n\t};\r\n};\r\n\r\n// this removes the sort type from all instances of SortableTable\r\nSortableTable.prototype.removeSortType = function (sType) {\r\n\tdelete this._sortTypeInfo[sType];\r\n};\r\n\r\nSortableTable.basicCompare = function compare(n1, n2) {\r\n\tif (n1.value < n2.value)\r\n\t\treturn -1;\r\n\tif (n2.value < n1.value)\r\n\t\treturn 1;\r\n\treturn 0;\r\n};\r\n\r\nSortableTable.idFunction = function (x) {\r\n\treturn x;\r\n};\r\n\r\nSortableTable.toUpperCase = function (s) {\r\n\treturn s.toUpperCase();\r\n};\r\n\r\nSortableTable.toDate = function (s) {\r\n\tvar parts = s.split("-");\r\n\tvar d = new Date(0);\r\n\td.setFullYear(parts[0]);\r\n\td.setDate(parts[2]);\r\n\td.setMonth(parts[1] - 1);\r\n\treturn d.valueOf();\r\n};\r\n\r\n\r\n// add sort types\r\nSortableTable.prototype.addSortType("Number", Number);\r\nSortableTable.prototype.addSortType("CaseInsensitiveString", SortableTable.toUpperCase);\r\nSortableTable.prototype.addSortType("Date", SortableTable.toDate);\r\nSortableTable.prototype.addSortType("String");\r\n// None is a special case\r\n'}}]);