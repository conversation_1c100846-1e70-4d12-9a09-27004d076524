table_corr_header = document.getElementById("corr_table_header");
table_corr = document.getElementById("corr_table");
rows_corr = table_corr.querySelectorAll("tr");
cols_corr = table_corr_header.getElementsByTagName("th");
table_body_corr = table_corr.querySelector("tbody");
sortable_headers = document.getElementsByName("sortable_headers");


reverse_table_corr_header = document.getElementById("reverse_corr_table_header");
reverse_table_corr = document.getElementById("reverse_corr_table");
reverse_rows_corr = reverse_table_corr.querySelectorAll("tr");
reverse_cols_corr = reverse_table_corr_header.getElementsByTagName("th");
reverse_table_body_corr = reverse_table_corr.querySelector("tbody");
reverse_sortable_headers = document.getElementsByName("sortable_headers_reverse");

strategy_buttons = document.getElementsByName("strategy_cell");

let comapre_func_map = {
    "strategy name": compareStrings,
    "start date": compareDates,
}

const directions_corr = Array.from(sortable_headers).map(function (header) {
    return '';
});

const reverse_directions_corr = Array.from(reverse_sortable_headers).map(function (header) {
    return '';
});

for (let index = 0; index < sortable_headers.length; index++) {
    let header_value = sortable_headers[index].innerHTML.toLowerCase();
    let compare_func  = header_value in comapre_func_map ? comapre_func_map[header_value] : compareFloats;
    sortable_headers[index].addEventListener('click', () => {
        sortTable(index, directions_corr, rows_corr, table_body_corr, compare_func)
    });
}

for (let index = 0; index < reverse_sortable_headers.length; index++) {
    let header_value = reverse_sortable_headers[index].innerHTML.toLowerCase();
    let compare_func  = header_value in comapre_func_map ? comapre_func_map[header_value] : compareFloats;
    reverse_sortable_headers[index].addEventListener('click', () => {
        sortTable(index, reverse_directions_corr, reverse_rows_corr, reverse_table_body_corr, compare_func)
    });
}

function openStrats(evt, strat_type) {
    let i, tabcontent, tablinks;
    tabcontent = document.getElementsByClassName("tabcontent");
    for (i = 0; i < tabcontent.length; i++) {
        tabcontent[i].style.display = "none";
    }

    tablinks = document.getElementsByClassName("tablinks");
    for (i = 0; i < tablinks.length; i++) {
        tablinks[i].className = tablinks[i].className.replace(" active", "");
    }

    document.getElementById(strat_type).style.display = "block";
    evt.currentTarget.className += " active";
}

strategy_buttons.forEach(item => {
    item.addEventListener('click', event => {
        const a = document.createElement('a');
        document.body.appendChild(a);
        a.setAttribute('style', 'display: none');
        a.href = "/" + event.target.getAttribute("value") + "/expand";
        a.target = "_blank";
        a.click();
    })
});

