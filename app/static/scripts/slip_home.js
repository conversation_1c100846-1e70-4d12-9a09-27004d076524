const segmentDropdown = document.getElementById("segment-dropdown");
const timewise_graph = document.getElementById("timewise_graph");
const time_graph_popup = document.getElementById("time_graph_popup");
const time_graph_generate = document.getElementById("time_graph_generate");
const time_graph_popup_close = document.getElementById(
    "time_graph_popup_close"
);
const wrong_date_popup = document.getElementById("wrong_date_popup");
const wrong_date_popup_close = document.getElementById(
    "wrong_date_popup_close"
);
const popupParagraph = wrong_date_popup.querySelector("p");

segmentDropdown.addEventListener("change", () => {
    let url_to_fetch = "/slip_dashboard/" + segmentDropdown.value;
    window.location.href = url_to_fetch;
});

function checkDateInput() {
    const start_date = document.getElementById("start_date").value;
    const end_date = document.getElementById("end_date").value;
    if (start_date && end_date) {
        time_graph_generate.disabled = false;
    } else {
        time_graph_generate.disabled = true;
    }
}

timewise_graph.onclick = function () {
    time_graph_popup.style.visibility = "visible";
    wrong_date_popup.style.visibility = "hidden";
};

time_graph_popup_close.onclick = function () {
    time_graph_popup.style.visibility = "hidden";
};

time_graph_generate.onclick = function () {
    time_graph_popup.style.visibility = "hidden";
    const start_date_str = document.getElementById("start_date").value;
    const end_date_str = document.getElementById("end_date").value;

    const start_date = new Date(start_date_str);
    const end_date = new Date(end_date_str);
    const today = new Date();

    if (start_date >= end_date) {
        popupParagraph.textContent = "Start date should be less than end date";
        wrong_date_popup.style.visibility = "visible";
    } else if (end_date > today) {
        popupParagraph.textContent =
            "End date should be lower than today's date";
        wrong_date_popup.style.visibility = "visible";
    } else {
        let url_to_fetch =
            "/time_graph?segment=" +
            segmentDropdown.value +
            "&start_date=" +
            start_date_str +
            "&end_date=" +
            end_date_str;
        window.location.href = url_to_fetch;
    }
};

wrong_date_popup_close.onclick = function () {
    wrong_date_popup.style.visibility = "hidden";
};
