cluster_backtest_popup = document.getElementById("cluster_backtest_popup");
reason_failure = document.getElementById("reason_failure");
cluster_backtest_popup_close = document.getElementById(
    "cluster_backtest_popup_close"
);
cluster_backtest_popup_close.onclick = function () {
    cluster_backtest_popup.style.visibility = "hidden";
};

restart_buttons = document.getElementsByName("restart_button");
function restart_backtest() {
    $.ajax({
        url: "/post_cluster_backtest",
        type: "POST",
        data: {
            cluster: this.id,
            slaves: this.value,
            is_restart: true,
            csrf_token: csrf_token.value,
        },
        success: function (response) {
            if (response == "failed") {
                reason_failure.innerHTML = "Error in restarting backtest";
                cluster_backtest_popup.style.visibility = "visible";
            } else {
                location.reload();
            }
        },
        error: function (response) {
            reason_failure.innerHTML = "Error in restarting backtest";
            cluster_backtest_popup.style.visibility = "visible";
        },
    });
}

for (let index = 0; index < restart_buttons.length; index++) {
    restart_buttons[index].addEventListener("click", restart_backtest);
}

cluster_selected = document.getElementById("cluster");
slave_div = document.getElementById("slave_div");

function append_checkbox(options) {
    var checkbox = document.createElement("input");
    checkbox.setAttribute("type", "checkbox");
    checkbox.setAttribute(options.field, options.field_value);
    checkbox.setAttribute("class", "form-check-input");
    checkbox.setAttribute("value", options.label_value);
    checkbox.setAttribute("style", "margin-right:8px;");
    slave_div.appendChild(checkbox);
    var label = document.createElement("label");
    label.setAttribute("class", "form-check-label");
    label.innerHTML = options.label_value;
    slave_div.appendChild(label);
    slave_div.appendChild(document.createElement("br"));
}

function get_slaves() {
    $.ajax({
        url: "/get_slaves",
        type: "POST",
        data: {
            cluster: this.value,
        },
        success: function (response) {
            if (response != "failed") {
                slave_div.innerHTML = "";
                append_checkbox({
                    field: "id",
                    field_value: "all_slaves_check",
                    label_value: "select all",
                });
                for (slave of response) {
                    append_checkbox({
                        field: "name",
                        field_value: "slave_check",
                        label_value: slave,
                    });
                }
                all_slaves_check = document.getElementById("all_slaves_check");
                slave_check = document.getElementsByName("slave_check");
                slaves_backtest_dict = {};
                all_slaves_check.addEventListener("change", (event) => {
                    if (all_slaves_check.checked) {
                        slave_check.forEach((slave) => {
                            slave.checked = true;
                            slaves_backtest_dict[slave.value] = 1;
                        });
                    } else {
                        slave_check.forEach((slave) => {
                            slave.checked = false;
                            slaves_backtest_dict[slave.value] = 0;
                        });
                    }
                });

                slave_check.forEach((slave) => {
                    slave.addEventListener("change", (event) => {
                        if (slave.checked) {
                            slaves_backtest_dict[slave.value] = 1;
                        } else {
                            slaves_backtest_dict[slave.value] = 0;
                        }
                    });
                });
            } else {
                slave_div.innerHTML = "";
                reason_failure.innerHTML = "Slaves does not exist";
                cluster_backtest_popup.style.visibility = "visible";
            }
        },
        error: function (response) {
            reason_failure.innerHTML = "Error in fetching slaves";
            cluster_backtest_popup.style.visibility = "visible";
        },
    });
}
cluster_selected.addEventListener("change", get_slaves);

slaves_selected = document.getElementById("slaves");
cluster_backtest_form = document.getElementById("cluster_backtest_form");
cluster_backtest_form.onsubmit = function () {
    slaves_selected.value = "";
    for (let slave in slaves_backtest_dict) {
        if (slaves_backtest_dict[slave]) {
            slaves_selected.value = slaves_selected.value + slave + ":";
        }
    }
    slaves_selected.value = slaves_selected.value.slice(0, -1);
};

add_cluster_backtest = document.getElementById("add_cluster_backtest");
add_cluster_backtest.addEventListener("click", function () {
    document.getElementById("cluster_backtest_form").style.display = "";
    this.style.display = "none";
});

delete_cluster = document.getElementsByName("delete_cluster");
function delete_cluster_backtest() {
    $.ajax({
        url: "/delete_cluster_backtest",
        type: "POST",
        data: {
            cluster: this.id,
            slaves: this.value,
        },
        success: function (response) {
            if (response != "failed") {
                location.reload();
            } else if (response == "empty_cluster") {
                reason_failure.innerHTML =
                    "Cluster did't exist in the backtest list";
                cluster_backtest_popup.style.visibility = "visible";
            } else {
                reason_failure.innerHTML = "Error in deleting cluster backtest";
                cluster_backtest_popup.style.visibility = "visible";
            }
        },
        error: function (response) {
            reason_failure.innerHTML = "Error in deleting cluster backtest";
            cluster_backtest_popup.style.visibility = "visible";
        },
    });
}
for (let index = 0; index < delete_cluster.length; index++) {
    delete_cluster[index].addEventListener("click", delete_cluster_backtest);
}

download_results = document.getElementsByName("download");
function download_cluster_backtest() {
    let formData = new FormData();
    formData.append("cluster", this.id);
    formData.append("slaves", this.value);
    fetch("/download_custom_cluster_backtest", {
        method: "POST",
        body: formData,
    })
        .then((response) => {
            if (!response.ok) {
                reason_failure.innerHTML = "Files are not present";
                cluster_backtest_popup.style.visibility = "visible";
            } else {
                return response.blob();
            }
        })
        .then((blob) => {
            if (blob !== undefined) {
                const a = document.createElement("a");
                document.body.appendChild(a);
                a.setAttribute("style", "display: none");
                const url = window.URL.createObjectURL(blob);
                a.href = url;
                a.download = this.id + "_backtest_results.zip";
                a.click();
                window.URL.revokeObjectURL(url);
            }
        });
}
for (let index = 0; index < download_results.length; index++) {
    download_results[index].addEventListener(
        "click",
        download_cluster_backtest
    );
}
