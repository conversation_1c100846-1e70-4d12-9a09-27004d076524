table_live_header = document.getElementById("live_strats_table_header");
table_live = document.getElementById("live_strats_table");
table_rejected_header = document.getElementById("rejected_strats_table_header");
table_rejected = document.getElementById("rejected_strats_table");
table_dead_header = document.getElementById("dead_strats_table_header");
table_dead = document.getElementById("dead_strats_table");

sortable_headers_live = document.getElementsByName("sortable_headers_live");
sortable_headers_rejected = document.getElementsByName("sortable_headers_rejected");
sortable_headers_dead = document.getElementsByName("sortable_headers_dead");


inactive_btns = document.getElementsByName("inactive_btns");
reset_btns = document.getElementsByName("reset_btns");
reset_btns_test = document.getElementsByName("reset_btns_test");

rows_live = table_live.querySelectorAll("tr");
cols_live = table_live_header.getElementsByTagName("th");
table_body_live = table_live.querySelector("tbody");

rows_rejected = table_rejected.querySelectorAll("tr");
cols_rejected = table_rejected_header.getElementsByTagName("th");
table_body_rejected = table_rejected.querySelector("tbody");

rows_dead = table_dead.querySelectorAll("tr");
cols_dead = table_dead_header.getElementsByTagName("th");
table_body_dead = table_dead.querySelector("tbody");

sucess_popup = document.getElementById("sucess_popup");
reason_success = document.getElementById("reason_success");
success_popup_close = document.getElementById("success_popup_close");
failure_popup = document.getElementById("failure_popup");
reason_failure = document.getElementById("reason_failure");
failure_popup_close = document.getElementById("failure_popup_close");
clusters_removed = document.getElementById("clusters_removed");
strategy_buttons = document.getElementsByName("strategy_cell");
dead_backtest = document.getElementsByName("dead_backtest");
rejected_backtest = document.getElementsByName("rejected_backtest");

let comapre_func_map = {
    "strategy name": compareStrings,
    "developer name": compareStrings,
    "segment name": compareStrings,
    "last run day": compareDates,
    "rejected date": compareDates,
}

const directions_live = Array.from(sortable_headers_live).map(function (header) {
    return '';
});
const directions_rejected = Array.from(sortable_headers_rejected).map(function (header) {
    return '';
});
const directions_dead = Array.from(sortable_headers_dead).map(function (header) {
    return '';
});

function make_dead() {
    confirmation = confirm("Are you sure you want to kill the strategy?")
    if (confirmation == true) {
        $.ajax({
            url: "/make_strategy_dead",
            type: 'POST',
            data: {
                strategy: this.value
            },
            success: function (response) {
                if (response != "failed") {
                    reason_success.innerHTML = "Strategy removed from following clusters:";
                    for (let index = 0; index < response.length; index++) {
                        entry = document.createElement("li");
                        entry.appendChild(document.createTextNode(response[index]));
                        clusters_removed.appendChild(entry);
                    }
                    sucess_popup.style.visibility = "visible";
                } else {
                    reason_failure.innerHTML = "Strategy could not be removed";
                    failure_popup.style.visibility = "visible";
                }
            },
            error: function (response) {
                reason_failure.innerHTML = "Strategy could not be removed";
                failure_popup.style.visibility = "visible";
            }
        });
    }
}

for (let index = 0; index < inactive_btns.length; index++) {
    inactive_btns[index].addEventListener('click', make_dead);
}


for (let index = 0; index < reset_btns.length; index++) {
    reset_btns[index].addEventListener('click', function () {
        clear_and_exit(this, "LIVE");
    });
}

for (let index = 0; index < reset_btns_test.length; index++) {
    reset_btns_test[index].addEventListener('click', function () {
        clear_and_exit(this, "TEST");
    });
}

for (let index = 0; index < sortable_headers_live.length; index++) {
    let header_value = sortable_headers_live[index].innerHTML.toLowerCase().trim();
    let compare_func  = header_value in comapre_func_map ? comapre_func_map[header_value] : compareFloats;
    sortable_headers_live[index].addEventListener('click', () => {
        sortTable(index, directions_live, rows_live, table_body_live, compare_func)
    });
}

for (let index = 0; index < sortable_headers_rejected.length; index++) {
    let header_value = sortable_headers_rejected[index].innerHTML.toLowerCase().trim();
    let compare_func  = header_value in comapre_func_map ? comapre_func_map[header_value] : compareFloats;
    sortable_headers_rejected[index].addEventListener('click', () => {
        sortTable(index, directions_rejected, rows_rejected, table_body_rejected, compare_func)
    });
}

for (let index = 0; index < sortable_headers_dead.length; index++) {
    let header_value = sortable_headers_dead[index].innerHTML.toLowerCase().trim();
    let compare_func  = header_value in comapre_func_map ? comapre_func_map[header_value] : compareFloats;
    sortable_headers_dead[index].addEventListener('click', () => {
        sortTable(index, directions_dead, rows_dead, table_body_dead, compare_func)
    });
}

success_popup_close.onclick = function () {
    window.location.reload();
}

failure_popup_close.onclick = function () {
    failure_popup.style.visibility = "hidden";
}

function openStrats(evt, strat_type) {
    let i, tabcontent, tablinks;
    tabcontent = document.getElementsByClassName("tabcontent");
    for (i = 0; i < tabcontent.length; i++) {
        tabcontent[i].style.display = "none";
    }

    tablinks = document.getElementsByClassName("tablinks");
    for (i = 0; i < tablinks.length; i++) {
        tablinks[i].className = tablinks[i].className.replace(" active", "");
    }

    document.getElementById(strat_type).style.display = "block";
    evt.currentTarget.className += " active";
}

strategy_buttons.forEach(item => {
    item.addEventListener('click', event => {
        const a = document.createElement('a');
        document.body.appendChild(a);
        a.setAttribute('style', 'display: none');
        a.href = event.target.getAttribute("value") + "/expand";
        a.target = "_blank";
        a.click();
    })
});

function add_to_sentinel(){
    btn_clicked = this;
    if (btn_clicked.innerText == 'Add') {
        action = 'Add'
    } else if(btn_clicked.innerText == 'Delete'){
        action = 'Delete'
    } else {
        action = 'Invalid'
    }
    if (action == 'Add' || action == 'Delete') {
        $.ajax({
            url: "/add_to_sentinel",
            type: 'POST',
            data: {
                strategy: btn_clicked.value,
                action: action,
                service: btn_clicked.name
            },
            success: function (response) {
                if (response == "success") {
                    if (action == "Add") {
                        btn_clicked.innerText = "Delete";
                        btn_clicked.style.backgroundColor = "red";
                        btn_clicked.style.borderColor = "red";
                    } else {
                        btn_clicked.innerText = "Add";
                        btn_clicked.style.backgroundColor = "green";
                        btn_clicked.style.borderColor = "green";
                    }
                } else if (response == "status_failed") {
                    reason_failure.innerHTML = "Strategy status invalid";
                    failure_popup.style.visibility = "visible";
                } else if (response == "exceeded_limit") {
                    reason_failure.innerHTML = "Can't add more than 5 strategies";
                    failure_popup.style.visibility = "visible";
                } else if (response == "invalid_strategy") {
                    reason_failure.innerHTML = "Invalid strategy";
                    failure_popup.style.visibility = "visible";
                } else if (response == "duplicate_strategy") {
                    reason_failure.innerHTML = "Strategy already added to sentinel backtest";
                    failure_popup.style.visibility = "visible";
                    document.getElementById("failure_popup_close").addEventListener("click", function () {
                        failure_popup.style.visibility = "hidden";
                        location.reload()
                    })
                } else if (response == "empty_strategy") {
                    reason_failure.innerHTML = "Strategy already deleted from sentinel backtest";
                    failure_popup.style.visibility = "visible";
                    document.getElementById("failure_popup_close").addEventListener("click", function () {
                        failure_popup.style.visibility = "hidden";
                        location.reload()
                    })
                } else {
                    reason_failure.innerHTML = "Request to sentinel backtest failed";
                    failure_popup.style.visibility = "visible";
                }
            },
            error: function (response) {
                reason_failure.innerHTML = "Request to sentinel backtest failed";
                failure_popup.style.visibility = "visible";
            }
        });
    } else {
        reason_failure.innerHTML = "Invalid backtest request";
        failure_popup.style.visibility = "visible";
    }
}

for (let index = 0; index < dead_backtest.length; index++) {
    dead_backtest[index].addEventListener('click', add_to_sentinel);
}

for (let index = 0; index < rejected_backtest.length; index++){
    rejected_backtest[index].addEventListener('click', add_to_sentinel);
}


no_filter_cols = {
    'live':['Comments / Todo', 'Remove'],
    'rejected':['Comments / Todo', 'Add to Backtest'],
    'dead':['Comments / Todo', 'Add to Backtest'],
}
cols_list = {
    'live':cols_live,
    'rejected':cols_rejected,
    'dead':cols_dead,
}
cols_to_none = {
    "live_strats_table":[],
    "rejected_strats_table":[],
    "dead_strats_table":[]
}
for(var state in cols_list){
    cols = cols_list[state]
    col_names = []
    for(var index=0; index<cols.length; index++){
        col_names.push(cols[index].textContent.trim());
    }
    cols_list[state] = col_names
}
for(var state in no_filter_cols){
    cols = no_filter_cols[state]
    for(var i in cols){
        index = cols_list[state].indexOf(cols[i])
        if(index!=-1){
            cols_to_none[state+"_strats_table"].push("col_"+index)
        }
    }
}

for(var table in cols_to_none){
    cols = cols_to_none[table];
    config = {
        base_path: "static/scripts/tablefilter/",
        auto_filter: {
                          delay: 100 //milliseconds
                    },
       filters_row_index: 0,
       alternate_rows: true,
       rows_counter: true,
       btn_reset: true,
       status_bar: true,
       msg_filter: 'Filtering...',
    };
    for(var col of cols){
        config[col]='none';
    }
    tf = new TableFilter(table, 0, config);
    tf.init();
}

