perf_fail_popup = document.getElementById("perf_fail_popup");
perf_fail_close = document.getElementById("perf_fail_close");
table_entries = document.getElementsByTagName("td");
strategy_name = document.getElementById("profiler").getAttribute("value");
cluster_mapping = document
    .getElementById("cluster_mapping")
    .getAttribute("value");
tag_id = document.getElementById("tagid");
metric_fail_reason = document.getElementById("metric_fail_reason");
kivifolio_report_btn = document.getElementById("kivifolio_report_btn");
add_user = document.getElementById("add-user");
add_user_popup = document.getElementById("add-user-popup");
add_user_close = document.getElementById("add-user-close");
add_user_submit = document.getElementById("add-user-submit");
new_user = document.getElementById("new_user");
removal_buttons = document.getElementsByClassName("close-icon");
todo_button = document.getElementById("to_do_button");
todo_popup = document.getElementById("to_do_popup");
todo_close = document.getElementById("to_do_close");
todo_save = document.getElementById("to_do_save");
todo_list = document.getElementById("todo_list");
success_popup = document.getElementById("success_popup");
success_popup_close = document.getElementById("success_popup_close");
sentinel_kivifolio_btn = document.getElementById("sentinel_kivifolio_btn");
reworked_strategy_button = document.getElementById("reworked_strategy");

kivifolio_report_btn.onclick = function () {
    $.ajax({
        url: "/strat_expand/kivifolio_report",
        type: "POST",
        data: {
            strategy: strategy_name,
        },
        success: function (response) {
            if (response == "failed") {
                metric_fail_reason.innerHTML = "Kivifolio report not found";
                perf_fail_popup.style.visibility = "visible";
            } else {
                const a = document.createElement("a");
                document.body.appendChild(a);
                a.setAttribute("style", "display: none");
                a.href = "/strat_expand/kivifolio_report/" + strategy_name;
                a.target = "_blank";
                a.click();
            }
        },
        error: function (response) {
            metric_fail_reason.innerHTML = "Kivifolio report not found";
            perf_fail_popup.style.visibility = "visible";
        },
    });
};

function fetchClusterPerformance(e) {
    const cluster_name = e.getAttribute("name");
    $.ajax({
        url: "/strat_expand/cluster_performance",
        type: "POST",
        data: {
            strategy: strategy_name,
            cluster: cluster_name,
        },
        success: function (response) {
            if (response == "failed") {
                metric_fail_reason.innerHTML =
                    "Cluster Performance files not found";
                perf_fail_popup.style.visibility = "visible";
            } else {
                const a = document.createElement("a");
                document.body.appendChild(a);
                a.setAttribute("style", "display: none");
                a.href =
                    "/strat_expand/cluster_performance/" +
                    strategy_name +
                    "/" +
                    cluster_name;
                a.target = "_blank";
                a.click();
            }
        },
        error: function (response) {
            metric_fail_reason.innerHTML =
                "Cluster Performance files not found";
            perf_fail_popup.style.visibility = "visible";
        },
    });
}

perf_fail_close.onclick = function () {
    perf_fail_popup.style.visibility = "hidden";
};

for (let index = 0; index < table_entries.length; index++) {
    if (table_entries[index].innerHTML.trim() == "") {
        table_entries[index].style.visibility = "hidden";
        table_entries[index].innerHTML = "0";
    }
}
$.ajax({
    url: "/get_next_function_chart",
    type: "POST",
    data: {
        strategy: strategy_name,
    },
    success: function (response) {
        $("#tagid").append(response);
    },
    error: function (response) {
        console.log(response);
    },
});

function fetchPerformanceMetric(e) {
    week = e.getAttribute("name")
    $.ajax({
        url: "/strat_expand/performance",
        type: "POST",
        data: {
            strategy: strategy_name,
            week: week
        },
        success: function (response) {
            if (response == "failed") {
                metric_fail_reason.innerHTML =
                    "Performance metric files not found";
                perf_fail_popup.style.visibility = "visible";
            } else {
                const a = document.createElement("a");
                document.body.appendChild(a);
                a.setAttribute("style", "display: none");
                a.href = "/strat_expand/performance/" + strategy_name + "/" + week;
                a.target = "_blank";
                a.click();
            }
        },
        error: function (response) {
            metric_fail_reason.innerHTML = "Performance metric files not found";
            perf_fail_popup.style.visibility = "visible";
        },
    });
};

function fetchVarReport(e) {
    week = e.getAttribute("name")
    $.ajax({
        url: "/strat_expand/var_report",
        type: "POST",
        data: {
            strategy: strategy_name,
            week: week
        },
        success: function (response) {
            if (response == "failed") {
                metric_fail_reason.innerHTML =
                    "VaR Report not found";
                perf_fail_popup.style.visibility = "visible";
            } else {
                const a = document.createElement("a");
                document.body.appendChild(a);
                a.setAttribute("style", "display: none");
                a.href = "/strat_expand/var_report/" + strategy_name + "/" + week;
                a.target = "_blank";
                a.click();
            }
        },
        error: function (response) {
            metric_fail_reason.innerHTML = "VaR Report not found";
            perf_fail_popup.style.visibility = "visible";
        },
    });
};

add_user.onclick = function () {
    add_user_popup.style.visibility = "visible";
};

add_user_close.onclick = function () {
    add_user_popup.style.visibility = "hidden";
};

add_user_submit.onclick = function () {
    $.ajax({
        url: "/strat_expand/add_user",
        type: "POST",
        data: {
            strategy_name: strategy_name,
            username: new_user.value,
        },
        success: function (response) {
            if (response == "failed") {
                metric_fail_reason.innerHTML =
                    "User access could not be granted";
                perf_fail_popup.style.visibility = "visible";
            } else {
                const a = document.createElement("a");
                document.body.appendChild(a);
                a.setAttribute("style", "display: none");
                a.href = "/" + strategy_name + "/expand";
                a.click();
            }
        },
        error: function (response) {
            metric_fail_reason.innerHTML = "User access could not be granted";
            perf_fail_popup.style.visibility = "visible";
        },
    });
};

for (let index = 0; index < removal_buttons.length; index++) {
    removal_buttons[index].addEventListener("click", remove_user);
}

function remove_user() {
    $.ajax({
        url: "/strat_expand/remove_user",
        type: "POST",
        data: {
            strategy_name: strategy_name,
            username: this.value,
        },
        success: function (response) {
            if (response == "failed") {
                metric_fail_reason.innerHTML =
                    "User access could not be revoked";
                perf_fail_popup.style.visibility = "visible";
            } else {
                const a = document.createElement("a");
                document.body.appendChild(a);
                a.setAttribute("style", "display: none");
                a.href = "/" + strategy_name + "/expand";
                a.click();
            }
        },
        error: function (response) {
            metric_fail_reason.innerHTML = "User access could not be revoked";
            perf_fail_popup.style.visibility = "visible";
        },
    });
}

todo_button.onclick = function () {
    todo_popup.style.visibility = "visible";
};

todo_close.onclick = function () {
    todo_popup.style.visibility = "hidden";
};

todo_save.onclick = function () {
    $.ajax({
        url: "/strat_expand/add_todo",
        type: "POST",
        data: {
            strategy_name: strategy_name,
            todo: todo_list.value,
        },
        success: function (response) {
            if (response == "failed") {
                metric_fail_reason.innerHTML =
                    "To-do list could not be updated";
                perf_fail_popup.style.visibility = "visible";
                todo_popup.style.visibility = "hidden";
            } else {
                todo_popup.style.visibility = "hidden";
                success_popup.style.visibility = "visible";
            }
        },
        error: function (response) {
            metric_fail_reason.innerHTML = "To-do list could not be updated";
            perf_fail_popup.style.visibility = "visible";
            todo_popup.style.visibility = "hidden";
        },
    });
};
todo_list.addEventListener("keydown", function (e) {
    if (e.key == "Tab") {
        e.preventDefault();
        var start = this.selectionStart;
        var end = this.selectionEnd;

        // set textarea value to: text before caret + tab + text after caret
        this.value =
            this.value.substring(0, start) + "\t" + this.value.substring(end);

        // put caret at right position again
        this.selectionStart = this.selectionEnd = start + 1;
    }
});

success_popup_close.onclick = function () {
    success_popup.style.visibility = "hidden";
};

sentinel_kivifolio_btn.addEventListener("click", () => {
    btn_text = sentinel_kivifolio_btn.innerText;
    if (btn_text == "Request Kivifolio") {
        action = "Add";
    } else if (btn_text == "Remove Kivifolio") {
        action = "Delete";
    } else {
        action = "Invalid";
    }
    if (action == "Add" || action == "Delete") {
        $.ajax({
            url: "/add_to_sentinel",
            type: "POST",
            data: {
                strategy: sentinel_kivifolio_btn.value,
                action: action,
                service: "kivifolio",
            },
            success: function (response) {
                if (response == "success") {
                    if (action == "Add") {
                        sentinel_kivifolio_btn.innerText = "Remove Kivifolio";
                        sentinel_kivifolio_btn.style.backgroundColor = "red";
                        sentinel_kivifolio_btn.style.borderColor = "red";
                    } else {
                        sentinel_kivifolio_btn.innerText = "Request Kivifolio";
                        sentinel_kivifolio_btn.style.backgroundColor = "green";
                        sentinel_kivifolio_btn.style.borderColor = "green";
                    }
                } else if (response == "status_failed") {
                    metric_fail_reason.innerHTML =
                        "Only live strategies can be added";
                    perf_fail_popup.style.visibility = "visible";
                } else if (response == "invalid_strategy") {
                    metric_fail_reason.innerHTML = "Invalid Strategy";
                    perf_fail_popup.style.visibility = "visible";
                } else if (response == "duplicate_strategy") {
                    metric_fail_reason.innerHTML =
                        "Strategy already added to sentinel kivifolio";
                    perf_fail_popup.style.visibility = "visible";
                    document
                        .getElementById("perf_fail_close")
                        .addEventListener("click", function () {
                            perf_fail_popup.style.visibility = "hidden";
                            location.reload();
                        });
                } else if (response == "empty_strategy") {
                    metric_fail_reason.innerHTML =
                        "Strategy already deleted from sentinel kivifolio";
                    perf_fail_popup.style.visibility = "visible";
                    document
                        .getElementById("perf_fail_close")
                        .addEventListener("click", function () {
                            perf_fail_popup.style.visibility = "hidden";
                            location.reload();
                        });
                } else {
                    metric_fail_reason.innerHTML =
                        "Request to sentinel kivifolio failed";
                    perf_fail_popup.style.visibility = "visible";
                }
            },
            error: function (response) {
                metric_fail_reason.innerHTML =
                    "Request to sentinel kivifolio failed";
                perf_fail_popup.style.visibility = "visible";
            },
        });
    } else {
        metric_fail_reason.innerHTML = "Invalid kivifolio request";
        perf_fail_popup.style.visibility = "visible";
    }
});

$(document).ready(function () {
    const dataheight = $("#live-cluster-mapping-data").outerHeight();
    $("#live-cluster-mapping-header").css("height", dataheight + "px");
    const dataheight_test = $("#test-cluster-mapping-data").outerHeight();
    $("#test-cluster-mapping-header").css("height", dataheight_test + "px");
});
reworked_strategy_button.addEventListener('click', function () {
    btn_text = reworked_strategy_button.innerText;
    const a = document.createElement('a');
    document.body.appendChild(a);
    a.setAttribute('style', 'display: none');
    a.href = "/" + btn_text + "/expand";
    a.target = "_blank";
    a.click();
})

function showDropdown(id) {
    var dropdownMenu = document.getElementById(id);
    dropdownMenu.classList.add("show");
}

function hideDropdown(id) {
    var dropdownMenu = document.getElementById(id);
    dropdownMenu.classList.remove("show");
}

$(document).on("click", function (e) {
    if (!$(".dropdown").is(e.target) && $(".dropdown").has(e.target).length === 0) {
        hideDropdown();
    }
});

