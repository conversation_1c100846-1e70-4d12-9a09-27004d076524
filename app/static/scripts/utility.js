function sortTable(index, direction_to_use, rows, table_body, compareFunction) {
    const direction = direction_to_use[index] || "asc";
    const multiplier = direction === "asc" ? 1 : -1;

    const new_rows = Array.from(rows);
    new_rows.sort(function (rowA, rowB) {
        let cellA = rowA.querySelectorAll("td")[index].innerHTML.toLowerCase();
        let cellB = rowB.querySelectorAll("td")[index].innerHTML.toLowerCase();

        return compareFunction(cellA, cellB) * multiplier;
    });

    [].forEach.call(rows, function (row) {
        table_body.removeChild(row);
    });

    new_rows.forEach(function (newRow) {
        table_body.appendChild(newRow);
    });

    direction_to_use[index] = direction === "asc" ? "desc" : "asc";
}

function compareDates(dateA, dateB) {
    const parsedDateA = new Date(dateA);
    const parsedDateB = new Date(dateB);

    const isValidDateA = !isNaN(parsedDateA.getTime());
    const isValidDateB = !isNaN(parsedDateB.getTime());

    if (isValidDateA && isValidDateB) {
        return parsedDateA - parsedDateB;
    }

    // Fallback to string comparison if one or both values are not valid dates
    return dateA.localeCompare(dateB);
}

function compareFloats(floatA, floatB) {
    const parsedFloatA = parseFloat(floatA);
    const parsedFloatB = parseFloat(floatB);

    if (!isNaN(parsedFloatA) && !isNaN(parsedFloatB)) {
        return parsedFloatA - parsedFloatB;
    }

    // Fallback to string comparison if one or both values are not valid floats
    return floatA.localeCompare(floatB);
}

function compareStrings(stringA, stringB) {
    return stringA.localeCompare(stringB);
}

function clear_and_exit(btn_clicked, environment) {
    sucess_popup = document.getElementById("sucess_popup");
    reason_success = document.getElementById("reason_success");
    failure_popup = document.getElementById("failure_popup");
    reason_failure = document.getElementById("reason_failure");
    
    if (btn_clicked.innerText == 'Reset Live' || btn_clicked.innerText == 'Reset Test') {
        action = 'reset'
    } else if(btn_clicked.innerText == 'UnReset Live' || btn_clicked.innerText == 'UnReset Test'){
        action = 'unreset'
    } else {
        action = 'Invalid'
    }
    if (action == 'reset' || action == 'unreset') {
        confirmation = confirm(`Are you sure you want to ${action} the strategy in the ${environment} environment?`);
        if (confirmation == true) {
            $.ajax({
                url: "/reset_strategy",
                type: 'POST',
                data: {
                    strategy: btn_clicked.value,
                    action: action,
                    environment: environment,
                },
                success: function (response) {
                    if (response == "success") {
                        if(action == "reset"){
                            reason_success.innerHTML = `Strategy set for pickle deletion and exits in the ${environment} environment.`;
                        }
                        else{
                            reason_success.innerHTML = `Strategy removed from pickle deletion and exits in the ${environment} environment.`;
                        }
                        sucess_popup.style.visibility = "visible";
                    } else {
                        reason_failure.innerHTML = response;
                        failure_popup.style.visibility = "visible";
                    }
                },
                error: function (response) {
                    reason_failure.innerHTML = `Error in strategy ${action} for the ${environment} environment.`;
                    failure_popup.style.visibility = "visible";
                }
            });
        }
    } else {
        reason_failure.innerHTML = "Invalid reset request";
        failure_popup.style.visibility = "visible";
    }
}  


