policy_popup = document.getElementById("policy_popup");
reason_failure = document.getElementById("reason_failure");
policy_popup_close = document.getElementById(
    "policy_popup_close"
);
policy_popup_close.onclick = function () {
    policy_popup.style.visibility = "hidden";
};
var simplemde = null;
var current_path = null;
function viewMore(event) {
    var fullContentElement = event.target.closest('.custom-card').querySelector('.full-content');
    var fullContent = fullContentElement.innerHTML;
    openPreview(fullContent);

}

function openPreview(content) {
    var md = window.markdownit();
    var htmlContent = md.render(content);
    $('#previewModal .modal-body').html(htmlContent);
    $('#previewModal').modal('show');
}

function editPolicy(e) {
    current_path = e.getAttribute("id");
    $.ajax({
        url: "/convert_to_md_format",
        type: "POST",
        data: {
            path: current_path
        },
        success: function (response) {
            openEditor(response[1], response[0]);
        },
        error: function () {
        }
    });

}

function openEditor(content, heading) {
    if (simplemde !== null) {
        simplemde.toTextArea();
        simplemde = null;
    }
    $('#policyHeading').val(heading)
    simplemde = new SimpleMDE({
        element: document.getElementById("editor"),
        spellChecker:false
    });
    simplemde.value(content);
    $('#editorModal').modal('show');
}

function saveChanges() {
    var updatedContent = simplemde.value();
    let name = $('#policyHeading').val()
    if (!name.trim()) {
        alert("Policy name must not be empty");
        return;
    }

    $.ajax({
        url: "policy",
        type: "POST",
        data: {
            content: updatedContent,
            title: name,
            path: current_path
        },
        success: function (response) {
            $('#editorModal').modal('hide');
            location.reload();
        },
        error: function () {
        }
    });
}

function deletePolicy(e) {
    let confirmation = confirm("Are you sure you want to delete this policy?");
    if (confirmation == true) {
        current_path = e.getAttribute("id");
        $.ajax({
            url: "delete_md_file",
            type: "POST",
            data: {
                path: current_path,
                type:"policy"
            },
            success: function (response) {
                if (response != "success") {
                    reason_failure.innerHTML = "Could not delete the policy";
                    policy_popup.style.visibility = "visible";
                }
                else {
                    location.reload()
                }
            },
            error: function () {
                reason_failure.innerHTML = "Could not delete the policy";
                policy_popup.style.visibility = "visible";
            }
        });
    }
}

function addPolicy() {
    current_path = null;
    if (simplemde !== null) {
        simplemde.toTextArea();
        simplemde = null;
    }
    $('#policyHeading').val("")
    $('#editor').val('# New Policy');
    simplemde = new SimpleMDE({
        element: document.getElementById("editor"),
        spellChecker:false
    });
    $('#editorModal').modal('show');
}