multiple_download_btn = document.getElementById("btn-download");
select_all_button = document.getElementById("checkbox_all");
strategy_list = document.getElementsByName("strat_name");
let checkBoxes = document.getElementsByName("checkbox_input");
sortable_headers_review = document.getElementsByName("sortable_headers_review");
table_live = document.getElementById("strats_body");
table_live_header = document.getElementById("strats");
rows_live = table_live.querySelectorAll("tr");
cols_live = table_live_header.getElementsByTagName("th");
table_body_live = table_live.querySelector("tbody");

fail_close = document.getElementById("fail_close");
fail_reason = document.getElementById("fail_reason");
fail_popup = document.getElementById("fail_popup");
encryption_popup = document.getElementById("encryption_popup");
encryption_close = document.getElementById("encryption_close");
encryption_submit = document.getElementById("encryption_submit"); 
file = document.getElementById("file"); 

pickle_reset_btns = document.getElementsByName("pickle_reset_btns");
reason_success = document.getElementById("reason_success");
sucess_popup = document.getElementById("sucess_popup");
reason_failure = document.getElementById("reason_failure");
failure_popup = document.getElementById("failure_popup");
success_popup_close = document.getElementById("success_popup_close");

let compare_func_map = {
    "date": compareDates,
    "strategy name": compareStrings,
    "developer name": compareStrings,
    "segment name": compareStrings,
    "cluster": compareStrings,
}
const directions_live = Array.from(sortable_headers_review).map(function (header) {
    return '';
});

for (let index = 0; index < sortable_headers_review.length; index++) {
    let header_value = sortable_headers_review[index].innerHTML.toLowerCase().trim();
    let compare_func  = header_value in compare_func_map ? compare_func_map[header_value] : compareFloats;
    sortable_headers_review[index].addEventListener('click', () => {
        sortTable(index+1, directions_live, rows_live, table_body_live, compare_func)
    });
}

let listener = function () {
    if (!this.checked) {
        select_all_button.checked = false;
    }
};

for (let i = 0; i < checkBoxes.length; i++) {
    checkBoxes[i].addEventListener("click", listener, false);
}

encryption_submit.onclick = function () {
    if (file.files.length === 0) {
        alert("Please select a file!");
    } else {
        let selected_file = file.files[0];
        let formData = new FormData();
        formData.append("file", selected_file);
        formData.append(
            "strategy_list",
            JSON.stringify(encryption_submit.strategy_list)
        );
        formData.append(
            "status",
            "TEST"
        )
        fetch("/strat_download", {
            method: "POST",
            body: formData,
        })
            .then((response) => {
                if (!response.ok) {
                    throw new Error("Validation request failed");
                }
                return response.blob();
            })
            .then((blob) => {
                const a = document.createElement("a");
                document.body.appendChild(a);
                a.setAttribute("style", "display: none");
                const url = window.URL.createObjectURL(blob);
                a.href = url;
                a.download = "samba.zip";
                a.click();
                window.URL.revokeObjectURL(url);
            })
            .catch((error) => {
                fail_popup.style.visibility = "visible";
            });
        encryption_popup.style.visibility = "hidden";
    }
};

encryption_close.onclick = function () {
    encryption_popup.style.visibility = "hidden";
};

fail_close.onclick = function () {
    fail_popup.style.visibility = "hidden";
};

multiple_download_btn.onclick = function () {
    encryption_popup.style.visibility = "visible";
    let strats_for_review = [];
    for (let i = 0; i < checkBoxes.length; i++) {
        if (checkBoxes[i].checked && checkBoxes[i] !== select_all_button) {
            strats_for_review.push(strategy_list[i].getAttribute("value"));
        }
    }
    encryption_submit.strategy_list = strats_for_review;
};

select_all_button.onclick = function () {
    if (select_all_button.checked) {
        for (let i = 0; i < checkBoxes.length; i++) {
            checkBoxes[i].checked = true;
        }
    } else {
        for (let i = 0; i < checkBoxes.length; i++) {
            checkBoxes[i].checked = false;
        }
    }
};

strategy_list.forEach(item => {
    item.addEventListener('click', event => {
        const a = document.createElement('a');
        document.body.appendChild(a);
        a.setAttribute('style', 'display: none');
        console.log(event.target.getAttribute("value"),"this")
        a.href = event.target.getAttribute("value") + "/expand";
        a.target = "_blank";
        a.click();
    })
});


//  Adding filter
no_filter_cols = ['']
cols_list = cols_live
cols_to_none = []
col_names = []
for(var index = 0; index < cols_list.length; index++){
        col_names.push(cols_list[index].textContent.trim());
    }
cols_list = col_names
for(var i in no_filter_cols){
    index = cols_list.indexOf(no_filter_cols[i])
    if(index != -1){
        cols_to_none.push("col_" + index)
    }
}
config = {
    base_path: "static/scripts/tablefilter/",
    auto_filter: {    
                  delay: 100 //milliseconds
                 },
    filters_row_index: 0,
    alternate_rows: true,
    sticky_headers: true,	
  
};
for(var col of cols_to_none){
    config[col] = 'none';
}
tf = new TableFilter("strats_body", 0, config);
tf.init()

for (let index = 0; index < pickle_reset_btns.length; index++) {
    pickle_reset_btns[index].addEventListener('click', function () {
        clear_and_exit(this, "TEST");
    });
}

failure_popup_close.onclick = function () {
    failure_popup.style.visibility = "hidden";
}

success_popup_close.onclick = function () {
    window.location.reload();
}