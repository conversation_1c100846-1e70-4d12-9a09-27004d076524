table_live_header = document.getElementById("live_strats_table_header");
table_live = document.getElementById("live_strats_table");
sortable_headers_live = document.getElementsByName("sortable_headers_live");

rows_live = table_live.querySelectorAll("tr");
cols_live = table_live_header.getElementsByTagName("th");
table_body_live = table_live.querySelector("tbody");

strategy_buttons = document.getElementsByName("strategy_cell");

let comapre_func_map = {
    "strategy name": compareStrings,
    "developer name": compareStrings,
    "segment name": compareStrings,
    "last run day": compareDates,
    "rejected date": compareDates,
}

const directions_live = Array.from(sortable_headers_live).map(function (header) {
    return '';
});


for (let index = 0; index < sortable_headers_live.length; index++) {
    let header_value = sortable_headers_live[index].innerHTML.toLowerCase();
    let compare_func  = header_value in comapre_func_map ? comapre_func_map[header_value] : compareFloats;
    sortable_headers_live[index].addEventListener('click', () => {
        sortTable(index, directions_live, rows_live, table_body_live, compare_func)
    });
}

strategy_buttons.forEach(item => {
    item.addEventListener('click', event => {
        const a = document.createElement('a');
        document.body.appendChild(a);
        a.setAttribute('style', 'display: none');
        a.href = event.target.getAttribute("value") + "/expand";
        a.target = "_blank";
        a.click();
    })
});

// cols_list = {
//     'live':cols_live,
// }
// cols_to_none = {
//     "live_strats_table":[],
// }
// col_names = []
// for(var index=0; index<cols_live.length; index++){
//     col_names.push(cols_live[index].textContent.trim());
// }

// for(var state in cols_list){
//     cols = cols_list[state]
//     col_names = []
//     for(var index=0; index<cols.length; index++){
//         col_names.push(cols[index].textContent.trim());
//     }
//     cols_list[state] = col_names
// }
// for(var state in no_filter_cols){
//     cols = no_filter_cols[state]
//     for(var i in cols){
//         index = cols_list[state].indexOf(cols[i])
//         if(index!=-1){
//             cols_to_none[state+"_strats_table"].push("col_"+index)
//         }
//     }
// }

config = {
    base_path: "static/scripts/tablefilter/",
    auto_filter: {
                        delay: 100 //milliseconds
                },
    filters_row_index: 0,
    alternate_rows: true,
    rows_counter: true,
    btn_reset: true,
    status_bar: true,
    msg_filter: 'Filtering...',
};
tf = new TableFilter("live_strats_table", 0, config);
tf.init();

