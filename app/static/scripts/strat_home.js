const developer_portfolio_btn = document.getElementById("developer_portfolio");
const userDropdown = document.getElementById("user-dropdown");
var attachmentDropdown = document.getElementById('attachments-dropdown');
error_popup = document.getElementById("error_popup");
reason_failure = document.getElementById("reason_failure");
error_popup_close = document.getElementById(
    "error_popup_close"
);
error_popup_close.onclick = function () {
    error_popup.style.visibility = "hidden";
};
var current_path = null;
var attachments = null;
var simplemde = null;
var toBeDeleted = null;

function fetchPortfolioPerformance(e) {
    const user = e.getAttribute("value");
    append_html(user)
}

function redirectToDeveloperPortfolio(e) {
    user = userDropdown.value;
    append_html(user)

}

function append_html(user) {
    const a = document.createElement("a");
    document.body.appendChild(a);
    a.setAttribute("style", "display: none");
    a.href = `portfolio_performance/${user}`;
    a.target = "_blank";
    a.click();

}
function viewMore(event) {
    var fullContentElement = event.target.closest('.custom-card').querySelector('.full-content');
    var fullContent = fullContentElement.innerHTML;
    var path = event.target.getAttribute('data-path');
    var attachment = event.target.getAttribute('data-attachment');
    openPreview(fullContent, path, attachment);

}
function openPreview(content, path, attachment) {
    var md = window.markdownit();
    var htmlContent = md.render(content);
    $('#previewModal .modal-body').html(htmlContent);
    var attachmentData = attachment.split(', ');
    var buttonGroup = $("#download_attachment_btn");
    buttonGroup.empty();
    for (var i = 0; i < attachmentData.length; i++) {
        if (attachmentData[i] == "") {
            continue;
        }
        var listItem = $('<li class="download-attachments"><button onclick="openHtmlFile(this)" value="' + path + '" name="' + attachmentData[i] + '" class="dropdown-item portfolio-performance">' + attachmentData[i] + '</button></li>');
        buttonGroup.append(listItem);
    }

    $('#previewModal').modal('show');
}

function editPoint(btn) {
    var attachmentsSection = document.getElementById("attachmentsSection");
    attachmentsSection.style.display = "block";
    var title = btn.dataset.title;
    var descriptionContent = btn.dataset.content;
    var description = btn.dataset.description;
    current_path = btn.dataset.path;
    var attachment = btn.dataset.attachment;
    var attachments = attachment.split(', ');
    attachmentDropdown.innerHTML = "";
    attachmentDropdown.innerHTML = '<option disabled selected value> Attachments</option>';
    for (var i = 0; i < attachments.length; i++) {
        if (attachments[i] == "") {
            continue;
        }
        attachmentDropdown.innerHTML += '<option value="' + attachments[i] + '">' + attachments[i] + '</option>';
    }
    openEditor(descriptionContent, title, description)
}

function openEditor(content, heading, description) {
    if (simplemde !== null) {
        simplemde.toTextArea();
        simplemde = null;
    }
    $('#Heading').val(heading)
    $('#Description').val(description)
    simplemde = new SimpleMDE({
        element: document.getElementById("editor"),
        spellChecker:false
    });
    simplemde.value(content);
    $('#editorModal').modal('show');
}
function CloseEditor() {
    if (simplemde !== null) {
        simplemde.toTextArea();
        simplemde = null;
    }
    if (ifDeletedWithoutSaving) {
        location.reload()
    }
    toBeDeleted = null;
}
var ifDeletedWithoutSaving = false

function saveChanges() {
    var updatedContent = simplemde.value();
    let name = $('#Heading').val()
    let description = $('#Description').val()
    if (!name.trim()) {
        alert("Title must not be empty");
        return;
    }
    if (name.length > 55) {
        alert('Title length exceeds the maximum allowed length of 55 characters!')
        return;
    }
    if (!description.trim()) {
        alert("Description must not be empty")
        return
    }
    if (description.length > 120) {
        alert('Description exceeds the maximum allowed length of 120 characters!')
        return;
    }
    $.ajax({
        url: "strat_home",
        type: "POST",
        data: {
            content: updatedContent,
            title: name,
            path: current_path,
            description: description
        },
        success: function (response) {
            $('#editorModal').modal('hide');
            location.reload();
        },
        error: function () {
        }
    });
}


function deletePoint(e) {
    let confirmation = confirm("Are you sure you want to delete this point?");
    if (confirmation == true) {
        let path = e.getAttribute("id");
        $.ajax({
            url: "/delete_md_file",
            type: "POST",
            data: {
                path: path,
                type: "new_area"
            },
            success: function (response) {
                location.reload()
            },
            error: function () {
            }
        });
    }
}
function openAttachmentInput(btn) {
    var attachmentInput = btn.parentElement.querySelector('.attachment-input');
    attachmentInput.click();
}

function handleAttachmentChange(input) {
    var files = input.files;
    current_path = input.id;
    var formData = new FormData();
    for (var i = 0; i < files.length; i++) {
        formData.append('htmlFiles', files[i]);
    }
    formData.append('path', current_path);
    $.ajax({
        url: '/upload_html_files',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function (response) {
            alert("Attachments uploaded successfully");
            location.reload()
        },
        error: function () {
        }
    });
}
function openHtmlFile(e) {
    var htmlFileName = e.getAttribute("name");
    var fullPath = e.getAttribute("value");
    var directoryName = fullPath.substring(0, fullPath.lastIndexOf("/"));
    var path = directoryName + "/" + htmlFileName;
    $.ajax({
        url: "/fetch_html_file",
        type: "POST",
        data: {
            path: path,
        },
        success: function (response) {
            var newTab = window.open();
            newTab.document.write(response);
        },
        error: function () {
            reason_failure.innerHTML = "Could not delete the idea";
            error_popup.style.visibility = "visible";
        }
    });
}

function handleDeleteAttachment(selectElement) {
    var selectedOption = selectElement.options[selectElement.selectedIndex];
    toBeDeleted = selectedOption.value;
}

document.getElementById('delete-attachment-btn').addEventListener('click', function () {
    deleteAttachment();
});

function deleteAttachment() {
    if (toBeDeleted === null) {
        alert("Select attachment to be deleted");
        return;
    }
    var directoryName = current_path.substring(0, current_path.lastIndexOf("/"));
    var path = directoryName + "/" + toBeDeleted;
    let confirmation = confirm("Are you sure you want to delete?");
    if (confirmation == true) {
        $.ajax({
            url: "/delete_html_file",
            type: "POST",
            data: {
                path: path,
            },
            success: function (response) {
                if (response === "success") {
                    var optionToRemove = attachmentDropdown.querySelector('option[value="' + toBeDeleted + '"]');
                    if (optionToRemove) {
                        optionToRemove.remove();
                    }
                }
                else {
                    reason_failure.innerHTML = "Could not delete the attachment";
                    error_popup.style.visibility = "visible";

                }
                toBeDeleted == null;
                ifDeletedWithoutSaving = true;
                document.getElementById("attachments-dropdown").selectedIndex = 0;
            },
            error: function () {
                reason_failure.innerHTML = "Could not delete the attachment";
                error_popup.style.visibility = "visible";
            }
        });
    }
}
function addPoints() {
    if (simplemde !== null) {
        simplemde.toTextArea();
        simplemde = null;
    }
    $('#Heading').val("")
    $('#Description').val("")
    $('#editor').val('# New Idea');
    simplemde = new SimpleMDE({
        element: document.getElementById("editor"),
        spellChecker:false
    });
    current_path = null;
    var attachmentsSection = document.getElementById("attachmentsSection");
    attachmentsSection.style.display = "none";
    $('#editorModal').modal('show');
}
