sidebar = document.getElementById("sidebar");
logo = document.getElementById("logo");
toggle_button = document.getElementById("toggle_button");
var state = 0;
var toggled = false;
heading = document.getElementById("heading");
toggle_button.onclick = function () {
    if (state == 0) {
        toggled = true;
        openNav();
    }
};

$(window).scroll(function () {
    if ($(this).scrollTop() > 0 && state == 0) {
        toggle_button.style.visibility = "hidden";
    } else {
        toggle_button.style.visibility = "visible";
    }
});

function openNav() {
    state = 1;
    sidebar.style.width = "260px";
    sidebar.style.left = "0em";
    var images = document.getElementsByClassName("img-sidebar");
    toggle_button.src = "/static/images/back.png";
    for (i = 0; i < images.length; i++) {
        images[i].style.visibility = "visible";
    }
    var logos = document.getElementsByClassName("links_name");
    for (i = 0; i < logos.length; i++) {
        logos[i].style.display = "block";
    }
    logo.style.visibility = "visible";
    toggle_button.style.marginLeft = "235px";
    heading.style.visibility = "hidden";
}

function closeNav() {
    state = 0;
    sidebar.style.width = "0px";
    sidebar.style.left = "-10em";
    var images = document.getElementsByClassName("img-sidebar");
    toggle_button.src = "/static/images/menu.svg";
    toggle_button.style.height = "43px";
    for (i = 0; i < images.length; i++) {
        images[i].style.visibility = "hidden";
    }
    var logos = document.getElementsByClassName("links_name");
    for (i = 0; i < logos.length; i++) {
        logos[i].style.display = "none";
    }
    logo.style.visibility = "hidden";
    toggle_button.style.marginLeft = "150px";
    heading.style.visibility = "visible";
    if ($(this).scrollTop() > 0) {
        toggle_button.style.visibility = "hidden";
    }
}

$(document).click(function (event) {
    let clickover = $(event.target);
    if (toggled) {
        toggled = false;
    } else if (state == 1 && !clickover.hasClass("sidebar")) {
        closeNav();
    }
});
