/**
 * Advanced Slippage Dashboard JavaScript
 * Handles interactive functionality for the advanced slippage analysis dashboard
 */

class AdvancedSlippageDashboard {
    constructor() {
        this.currentData = null;
        this.filteredData = null;
        this.charts = {};
        this.currentPage = 1;
        this.pageSize = 50;
        this.sortColumn = null;
        this.sortDirection = 'asc';
        this.dummyDataGenerator = new DummyDataGenerator();
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupTabNavigation();
        this.setupQuickDateButtons();
        this.setupMultiSelectDropdowns();
        this.setupTableControls();
        this.setupComparisonControls();
        this.loadInitialData();

        // Add real-time controls after initial setup
        setTimeout(() => {
            this.addRealTimeControls();
        }, 1000);

        // Setup window resize handler
        window.addEventListener('resize', () => {
            this.handleChartResize();
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            this.stopRealTimeSimulation();
        });

        // Add entrance animations to dashboard components
        this.addEntranceAnimations();
    }

    addEntranceAnimations() {
        // Add staggered animations to main components
        const components = [
            '.summary-metrics',
            '.filter-section',
            '.charts-container',
            '.data-tables-container',
            '.comparison-section'
        ];

        components.forEach((selector, index) => {
            const element = document.querySelector(selector);
            if (element) {
                element.classList.add('stagger-animation');
                element.style.animationDelay = `${index * 0.2}s`;
            }
        });

        // Add fade-in animation to cards
        const cards = document.querySelectorAll('.content-card');
        cards.forEach((card, index) => {
            card.classList.add('fade-in');
            card.style.animationDelay = `${index * 0.1}s`;
        });

        // Add slide animations to filter groups
        const filterGroups = document.querySelectorAll('.filter-group');
        filterGroups.forEach((group, index) => {
            if (index % 2 === 0) {
                group.classList.add('slide-in-left');
            } else {
                group.classList.add('slide-in-right');
            }
            group.style.animationDelay = `${index * 0.1}s`;
        });
    }

    setupEventListeners() {
        // Analysis button
        document.getElementById('run-analysis-btn').addEventListener('click', () => {
            this.runSlippageAnalysis();
        });

        // Comparison button
        document.getElementById('run-comparison-btn').addEventListener('click', () => {
            this.runTradeComparison();
        });

        // Load slaves button
        document.getElementById('load-slaves-btn').addEventListener('click', () => {
            this.loadAvailableSlaves();
        });

        // Window resize handler for charts
        window.addEventListener('resize', this.debounce(() => {
            this.handleChartResize();
        }, 250));
    }

    setupTabNavigation() {
        const tabButtons = document.querySelectorAll('.nav-link');
        const tabPanes = document.querySelectorAll('.tab-pane');

        tabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                
                // Remove active class from all tabs and panes
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabPanes.forEach(pane => {
                    pane.classList.remove('show', 'active');
                });

                // Add active class to clicked tab
                button.classList.add('active');
                
                // Show corresponding pane
                const targetId = button.getAttribute('data-target');
                const targetPane = document.getElementById(targetId);
                if (targetPane) {
                    targetPane.classList.add('show', 'active');
                }

                // Resize charts when tab becomes visible
                setTimeout(() => {
                    this.handleChartResize();
                }, 100);
            });
        });
    }

    setupQuickDateButtons() {
        const quickDateButtons = document.querySelectorAll('.quick-date-btn');

        quickDateButtons.forEach(button => {
            button.addEventListener('click', () => {
                const days = parseInt(button.getAttribute('data-days'));
                const endDate = new Date();
                const startDate = new Date();
                startDate.setDate(endDate.getDate() - days);

                // Format dates for input fields
                const formatDate = (date) => {
                    return date.toISOString().split('T')[0];
                };

                document.getElementById('start-date').value = formatDate(startDate);
                document.getElementById('end-date').value = formatDate(endDate);

                // Also update comparison dates
                document.getElementById('comparison-start-date').value = formatDate(startDate);
                document.getElementById('comparison-end-date').value = formatDate(endDate);
            });
        });
    }

    setupMultiSelectDropdowns() {
        // Create multi-select dropdowns for strategies and slaves
        this.createMultiSelectDropdown('strategy-select', 'Strategies', []);
        this.createMultiSelectDropdown('slave-select', 'Slaves', []);
        this.createMultiSelectDropdown('exchange-select-multi', 'Exchanges', [
            { value: 'IND', label: 'IND (India Combined)', selected: true },
            { value: 'NSE', label: 'NSE (National Stock Exchange)' },
            { value: 'BSE', label: 'BSE (Bombay Stock Exchange)' },
            { value: 'US', label: 'US (United States)' }
        ]);
    }

    createMultiSelectDropdown(containerId, label, options) {
        const container = document.getElementById(containerId);
        if (!container) {
            // Create container if it doesn't exist
            const filterGroup = document.createElement('div');
            filterGroup.className = 'filter-group';
            filterGroup.innerHTML = `
                <label class="form-label">${label}</label>
                <div id="${containerId}" class="multi-select-container"></div>
            `;

            // Find appropriate parent to append to
            const filterRow = document.querySelector('.filter-row');
            if (filterRow) {
                filterRow.appendChild(filterGroup);
            }
        }

        const multiSelectContainer = document.getElementById(containerId) || container;

        const multiSelectHTML = `
            <div class="multi-select-wrapper">
                <div class="multi-select-trigger" data-target="${containerId}-dropdown">
                    <span class="multi-select-display">Select ${label.toLowerCase()}...</span>
                    <span class="multi-select-count" style="display: none;">0</span>
                    <i class="fa fa-chevron-down multi-select-arrow"></i>
                </div>
                <div class="multi-select-dropdown" id="${containerId}-dropdown" style="display: none;">
                    <div class="multi-select-header">
                        <input type="text" class="multi-select-search" placeholder="Search ${label.toLowerCase()}...">
                        <div class="multi-select-actions">
                            <button type="button" class="multi-select-action-btn select-all">Select All</button>
                            <button type="button" class="multi-select-action-btn clear-all">Clear All</button>
                        </div>
                    </div>
                    <div class="multi-select-options" id="${containerId}-options">
                        ${this.generateMultiSelectOptions(options)}
                    </div>
                </div>
            </div>
        `;

        multiSelectContainer.innerHTML = multiSelectHTML;
        this.bindMultiSelectEvents(containerId);
    }

    generateMultiSelectOptions(options) {
        return options.map(option => {
            const value = typeof option === 'string' ? option : option.value;
            const label = typeof option === 'string' ? option : option.label;
            const selected = typeof option === 'object' && option.selected ? 'checked' : '';

            return `
                <div class="multi-select-option" data-value="${value}">
                    <input type="checkbox" class="multi-select-checkbox" ${selected}>
                    <span class="multi-select-label">${label}</span>
                </div>
            `;
        }).join('');
    }

    bindMultiSelectEvents(containerId) {
        const trigger = document.querySelector(`[data-target="${containerId}-dropdown"]`);
        const dropdown = document.getElementById(`${containerId}-dropdown`);
        const searchInput = dropdown.querySelector('.multi-select-search');
        const selectAllBtn = dropdown.querySelector('.select-all');
        const clearAllBtn = dropdown.querySelector('.clear-all');
        const options = dropdown.querySelectorAll('.multi-select-option');

        // Toggle dropdown
        trigger.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleMultiSelectDropdown(dropdown);
        });

        // Search functionality
        searchInput.addEventListener('input', (e) => {
            this.filterMultiSelectOptions(dropdown, e.target.value);
        });

        // Keyboard navigation
        searchInput.addEventListener('keydown', (e) => {
            this.handleMultiSelectKeyboard(e, dropdown);
        });

        // Select all
        selectAllBtn.addEventListener('click', () => {
            this.selectAllMultiSelectOptions(dropdown, true);
            this.updateMultiSelectDisplay(containerId);
        });

        // Clear all
        clearAllBtn.addEventListener('click', () => {
            this.selectAllMultiSelectOptions(dropdown, false);
            this.updateMultiSelectDisplay(containerId);
        });

        // Individual option selection
        options.forEach(option => {
            const checkbox = option.querySelector('.multi-select-checkbox');
            checkbox.addEventListener('change', () => {
                this.updateMultiSelectDisplay(containerId);
            });
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!dropdown.contains(e.target) && !trigger.contains(e.target)) {
                dropdown.style.display = 'none';
            }
        });

        // Initial display update
        this.updateMultiSelectDisplay(containerId);
    }

    toggleMultiSelectDropdown(dropdown) {
        const isVisible = dropdown.style.display === 'block';
        const trigger = dropdown.previousElementSibling;

        // Close all other dropdowns and remove active state
        document.querySelectorAll('.multi-select-dropdown').forEach(dd => {
            dd.style.display = 'none';
        });
        document.querySelectorAll('.multi-select-trigger').forEach(trigger => {
            trigger.classList.remove('active');
        });

        // Toggle current dropdown
        if (isVisible) {
            dropdown.style.display = 'none';
            trigger.classList.remove('active');
        } else {
            dropdown.style.display = 'block';
            trigger.classList.add('active');

            // Focus search input if available
            const searchInput = dropdown.querySelector('.multi-select-search');
            if (searchInput) {
                setTimeout(() => searchInput.focus(), 100);
            }
        }
    }

    filterMultiSelectOptions(dropdown, searchTerm) {
        const options = dropdown.querySelectorAll('.multi-select-option');
        const term = searchTerm.toLowerCase();
        let visibleCount = 0;

        options.forEach(option => {
            const label = option.querySelector('.multi-select-label').textContent.toLowerCase();
            const isVisible = label.includes(term);
            option.style.display = isVisible ? 'flex' : 'none';
            if (isVisible) visibleCount++;
        });

        // Show/hide "no results" message
        let noResultsMsg = dropdown.querySelector('.no-results-message');
        if (visibleCount === 0 && term.length > 0) {
            if (!noResultsMsg) {
                noResultsMsg = document.createElement('div');
                noResultsMsg.className = 'no-results-message';
                noResultsMsg.innerHTML = `
                    <div style="padding: 15px; text-align: center; color: var(--text-muted); font-style: italic;">
                        <i class="fa fa-search"></i> No results found for "${searchTerm}"
                    </div>
                `;
                dropdown.querySelector('.multi-select-options').appendChild(noResultsMsg);
            } else {
                noResultsMsg.querySelector('div').innerHTML = `
                    <i class="fa fa-search"></i> No results found for "${searchTerm}"
                `;
                noResultsMsg.style.display = 'block';
            }
        } else if (noResultsMsg) {
            noResultsMsg.style.display = 'none';
        }
    }

    selectAllMultiSelectOptions(dropdown, select) {
        const visibleOptions = dropdown.querySelectorAll('.multi-select-option:not([style*="display: none"])');

        visibleOptions.forEach(option => {
            const checkbox = option.querySelector('.multi-select-checkbox');
            checkbox.checked = select;
        });
    }

    updateMultiSelectDisplay(containerId) {
        const dropdown = document.getElementById(`${containerId}-dropdown`);
        const trigger = document.querySelector(`[data-target="${containerId}-dropdown"]`);
        const display = trigger.querySelector('.multi-select-display');
        const count = trigger.querySelector('.multi-select-count');

        const selectedOptions = dropdown.querySelectorAll('.multi-select-checkbox:checked');
        const selectedCount = selectedOptions.length;

        // Update visual state of trigger
        if (selectedCount > 0) {
            trigger.classList.add('has-selections');
        } else {
            trigger.classList.remove('has-selections');
        }

        // Update selected option styling
        dropdown.querySelectorAll('.multi-select-option').forEach(option => {
            const checkbox = option.querySelector('.multi-select-checkbox');
            if (checkbox.checked) {
                option.classList.add('selected');
            } else {
                option.classList.remove('selected');
            }
        });

        // Update display text
        if (selectedCount === 0) {
            display.textContent = `Select ${containerId.replace('-select', '').replace('-', ' ')}...`;
            display.style.display = 'inline';
            count.style.display = 'none';
        } else if (selectedCount === 1) {
            const selectedLabel = selectedOptions[0].parentElement.querySelector('.multi-select-label').textContent;
            display.textContent = selectedLabel;
            display.style.display = 'inline';
            count.style.display = 'none';
        } else if (selectedCount <= 3) {
            // Show up to 3 selected items
            const selectedLabels = Array.from(selectedOptions).map(option =>
                option.parentElement.querySelector('.multi-select-label').textContent
            );
            display.textContent = selectedLabels.join(', ');
            display.style.display = 'inline';
            count.style.display = 'none';
        } else {
            display.style.display = 'none';
            count.textContent = `${selectedCount} selected`;
            count.style.display = 'inline';
        }
    }

    getMultiSelectValues(containerId) {
        const dropdown = document.getElementById(`${containerId}-dropdown`);
        if (!dropdown) return [];

        const selectedOptions = dropdown.querySelectorAll('.multi-select-checkbox:checked');
        return Array.from(selectedOptions).map(checkbox =>
            checkbox.parentElement.getAttribute('data-value')
        );
    }

    handleMultiSelectKeyboard(e, dropdown) {
        const visibleOptions = dropdown.querySelectorAll('.multi-select-option:not([style*="display: none"])');
        let currentFocus = dropdown.querySelector('.multi-select-option.keyboard-focus');
        let currentIndex = currentFocus ? Array.from(visibleOptions).indexOf(currentFocus) : -1;

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                if (currentFocus) currentFocus.classList.remove('keyboard-focus');
                currentIndex = Math.min(currentIndex + 1, visibleOptions.length - 1);
                if (visibleOptions[currentIndex]) {
                    visibleOptions[currentIndex].classList.add('keyboard-focus');
                    visibleOptions[currentIndex].scrollIntoView({ block: 'nearest' });
                }
                break;

            case 'ArrowUp':
                e.preventDefault();
                if (currentFocus) currentFocus.classList.remove('keyboard-focus');
                currentIndex = Math.max(currentIndex - 1, 0);
                if (visibleOptions[currentIndex]) {
                    visibleOptions[currentIndex].classList.add('keyboard-focus');
                    visibleOptions[currentIndex].scrollIntoView({ block: 'nearest' });
                }
                break;

            case 'Enter':
            case ' ':
                e.preventDefault();
                if (currentFocus) {
                    const checkbox = currentFocus.querySelector('.multi-select-checkbox');
                    checkbox.checked = !checkbox.checked;
                    this.updateMultiSelectDisplay(dropdown.id.replace('-dropdown', ''));
                }
                break;

            case 'Escape':
                dropdown.style.display = 'none';
                dropdown.previousElementSibling.classList.remove('active');
                break;
        }
    }

    setupTableControls() {
        // Group by and aggregation controls
        document.getElementById('apply-grouping-btn').addEventListener('click', () => {
            this.applyGrouping();
        });

        // Column filter
        document.getElementById('column-filter-input').addEventListener('input', () => {
            this.applyColumnFilter();
        });

        document.getElementById('column-filter-select').addEventListener('change', () => {
            this.applyColumnFilter();
        });

        // Export buttons
        document.getElementById('export-csv-btn').addEventListener('click', () => {
            this.exportData('csv');
        });

        document.getElementById('export-excel-btn').addEventListener('click', () => {
            this.exportData('excel');
        });

        document.getElementById('export-json-btn').addEventListener('click', () => {
            this.exportToJSON();
        });

        // Bulk export options
        document.getElementById('export-all-formats-btn')?.addEventListener('click', (e) => {
            e.preventDefault();
            this.exportAllFormats();
        });

        document.getElementById('export-summary-only-btn')?.addEventListener('click', (e) => {
            e.preventDefault();
            this.exportSummaryOnly();
        });

        document.getElementById('export-charts-btn')?.addEventListener('click', (e) => {
            e.preventDefault();
            this.exportChartsAsImages();
        });

        // Pagination controls
        document.getElementById('prev-page-btn').addEventListener('click', () => {
            if (this.currentPage > 1) {
                this.currentPage--;
                this.updateTable();
            }
        });

        document.getElementById('next-page-btn').addEventListener('click', () => {
            const totalPages = Math.ceil((this.filteredData?.length || 0) / this.pageSize);
            if (this.currentPage < totalPages) {
                this.currentPage++;
                this.updateTable();
            }
        });

        document.getElementById('page-size-select').addEventListener('change', (e) => {
            this.pageSize = e.target.value === 'all' ? 999999 : parseInt(e.target.value);
            this.currentPage = 1;
            this.updateTable();
        });

        // Table sorting
        document.addEventListener('click', (e) => {
            if (e.target.closest('.sortable')) {
                const th = e.target.closest('.sortable');
                const column = th.getAttribute('data-column');
                this.sortTable(column);
            }
        });
    }

    setupComparisonControls() {
        // Comparison chart tab navigation
        const comparisonTabButtons = document.querySelectorAll('#comparison-chart-tabs .nav-link');
        const comparisonTabPanes = document.querySelectorAll('#comparison-chart-content .tab-pane');

        comparisonTabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();

                // Remove active class from all tabs and panes
                comparisonTabButtons.forEach(btn => btn.classList.remove('active'));
                comparisonTabPanes.forEach(pane => {
                    pane.classList.remove('show', 'active');
                });

                // Add active class to clicked tab
                button.classList.add('active');

                // Show corresponding pane
                const targetId = button.getAttribute('data-target');
                const targetPane = document.getElementById(targetId);
                if (targetPane) {
                    targetPane.classList.add('show', 'active');
                }

                // Resize charts when tab becomes visible
                setTimeout(() => {
                    this.handleChartResize();
                }, 100);
            });
        });

        // Chart control buttons
        document.getElementById('toggle-exit-count-view')?.addEventListener('click', () => {
            this.toggleChartView('exit-count-chart');
        });

        document.getElementById('toggle-pnl-view')?.addEventListener('click', () => {
            this.toggleChartView('pnl-comparison-chart');
        });

        document.getElementById('reset-pnl-zoom')?.addEventListener('click', () => {
            this.resetChartZoom('pnl-comparison-chart');
        });

        document.getElementById('holding-time-bins')?.addEventListener('change', (e) => {
            this.updateHoldingTimeChart(parseInt(e.target.value));
        });

        // Analysis controls
        document.getElementById('refresh-analysis-btn')?.addEventListener('click', () => {
            this.refreshDetailedAnalysis();
        });

        document.getElementById('analysis-type-select')?.addEventListener('change', () => {
            this.refreshDetailedAnalysis();
        });

        document.getElementById('metric-focus-select')?.addEventListener('change', () => {
            this.refreshDetailedAnalysis();
        });
    }

    async loadInitialData() {
        try {
            // Use dummy data for now - replace with API call later
            const dummyOptions = {
                segments: this.dummyDataGenerator.segments,
                strategies: this.dummyDataGenerator.strategies,
                slaves: this.dummyDataGenerator.slaves,
                exchanges: this.dummyDataGenerator.exchanges
            };

            this.populateFilterOptions(dummyOptions);

            // Load initial dashboard data with dummy data
            const initialData = this.dummyDataGenerator.generateSlippageData(30, 50);
            const summaryMetrics = this.dummyDataGenerator.generateSummaryMetrics(initialData);

            // Update summary metrics immediately
            this.updateSummaryMetrics(summaryMetrics);

            // Store initial data
            this.currentData = initialData;
            this.filteredData = [...initialData];

            console.log('Initial dummy data loaded successfully');
        } catch (error) {
            console.error('Error loading initial data:', error);
        }
    }

    populateFilterOptions(options) {
        // Update segment options
        const segmentSelect = document.getElementById('segment-select');
        const comparisonSegmentSelect = document.getElementById('comparison-segment-select');

        [segmentSelect, comparisonSegmentSelect].forEach(select => {
            if (select && options.segments) {
                select.innerHTML = '';
                options.segments.forEach(segment => {
                    const option = document.createElement('option');
                    option.value = segment;
                    option.textContent = segment;
                    if (segment === 'OPTIDX') option.selected = true;
                    select.appendChild(option);
                });
            }
        });

        // Update multi-select dropdowns
        if (options.strategies) {
            this.updateMultiSelectOptions('strategy-select', options.strategies.map(s => ({
                value: s,
                label: s
            })));
        }

        if (options.slaves) {
            this.updateMultiSelectOptions('slave-select', options.slaves.map(s => ({
                value: s,
                label: s
            })));
        }
    }

    updateMultiSelectOptions(containerId, options) {
        const optionsContainer = document.getElementById(`${containerId}-options`);
        if (optionsContainer) {
            optionsContainer.innerHTML = this.generateMultiSelectOptions(options);

            // Re-bind events for new options
            const dropdown = document.getElementById(`${containerId}-dropdown`);
            const newOptions = dropdown.querySelectorAll('.multi-select-option');

            newOptions.forEach(option => {
                const checkbox = option.querySelector('.multi-select-checkbox');
                checkbox.addEventListener('change', () => {
                    this.updateMultiSelectDisplay(containerId);
                });
            });
        }
    }

    async runSlippageAnalysis() {
        const chartsContainer = document.getElementById('charts-container');
        const dataTablesContainer = document.getElementById('data-tables-container');

        try {
            // Show enhanced loading indicator with progress
            this.showLoadingWithProgress('Analyzing slippage data...', 'Fetching records from database');
            chartsContainer.style.display = 'none';

            // Simulate loading delay with progress updates
            await this.simulateProgressiveLoading();

            // Collect filter parameters
            const filters = {
                start_date: document.getElementById('start-date').value,
                end_date: document.getElementById('end-date').value,
                segments: [document.getElementById('segment-select').value],
                exchanges: [document.getElementById('exchange-select').value],
                strategies: this.getMultiSelectValues('strategy-select'),
                slaves: this.getMultiSelectValues('slave-select')
            };

            // Generate dummy data based on filters
            const startDate = new Date(filters.start_date);
            const endDate = new Date(filters.end_date);
            const daysDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1;

            const dummyData = this.dummyDataGenerator.generateSlippageData(daysDiff, 50);
            const summaryMetrics = this.dummyDataGenerator.generateSummaryMetrics(dummyData);

            // Filter dummy data based on selected filters
            let filteredData = dummyData.filter(row => {
                const rowDate = new Date(row.date);
                const dateInRange = rowDate >= startDate && rowDate <= endDate;
                const segmentMatch = filters.segments.length === 0 || filters.segments.includes(row.segment);
                const exchangeMatch = filters.exchanges.length === 0 || filters.exchanges.includes(row.exchange);
                const strategyMatch = filters.strategies.length === 0 || filters.strategies.includes(row.strategy_name);
                const slaveMatch = filters.slaves.length === 0 || filters.slaves.includes(row.slave_name);

                return dateInRange && segmentMatch && exchangeMatch && strategyMatch && slaveMatch;
            });

            // If no data matches filters, use a subset of dummy data
            if (filteredData.length === 0) {
                filteredData = dummyData.slice(0, 100);
            }

            this.currentData = filteredData;
            await this.updateDashboard({ summary: summaryMetrics, raw_data: filteredData });

            chartsContainer.style.display = 'block';
            dataTablesContainer.style.display = 'block';

            this.showSuccess('Analysis completed successfully with ' + filteredData.length + ' records');

        } catch (error) {
            console.error('Error running slippage analysis:', error);
            this.showError('Error running analysis: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    // Enhanced loading methods
    showLoadingWithProgress(mainText, subText) {
        const loadingIndicator = document.getElementById('loading-indicator');
        const loadingTextEl = loadingIndicator.querySelector('.loading-text');
        const loadingSubtextEl = loadingIndicator.querySelector('.loading-subtext');

        loadingTextEl.textContent = mainText || 'Loading...';
        loadingSubtextEl.textContent = subText || 'Please wait';

        loadingIndicator.style.display = 'block';

        // Add entrance animation
        loadingIndicator.style.opacity = '0';
        loadingIndicator.style.transform = 'translateY(20px)';

        setTimeout(() => {
            loadingIndicator.style.transition = 'all 0.5s ease';
            loadingIndicator.style.opacity = '1';
            loadingIndicator.style.transform = 'translateY(0)';
        }, 50);
    }

    hideLoading() {
        const loadingIndicator = document.getElementById('loading-indicator');

        loadingIndicator.style.transition = 'all 0.3s ease';
        loadingIndicator.style.opacity = '0';
        loadingIndicator.style.transform = 'translateY(-20px)';

        setTimeout(() => {
            loadingIndicator.style.display = 'none';
            loadingIndicator.style.transition = '';
            loadingIndicator.style.transform = '';
        }, 300);
    }

    async simulateProgressiveLoading() {
        const steps = [
            { text: 'Analyzing slippage data...', subtext: 'Fetching records from database', delay: 500 },
            { text: 'Processing metrics...', subtext: 'Calculating summary statistics', delay: 400 },
            { text: 'Generating charts...', subtext: 'Preparing visualizations', delay: 300 },
            { text: 'Finalizing results...', subtext: 'Almost ready!', delay: 300 }
        ];

        for (const step of steps) {
            this.updateLoadingText(step.text, step.subtext);
            await new Promise(resolve => setTimeout(resolve, step.delay));
        }
    }

    updateLoadingText(mainText, subText) {
        const loadingIndicator = document.getElementById('loading-indicator');
        const loadingTextEl = loadingIndicator.querySelector('.loading-text');
        const loadingSubtextEl = loadingIndicator.querySelector('.loading-subtext');

        // Fade out
        loadingTextEl.style.opacity = '0.5';
        loadingSubtextEl.style.opacity = '0.5';

        setTimeout(() => {
            loadingTextEl.textContent = mainText;
            loadingSubtextEl.textContent = subText;

            // Fade in
            loadingTextEl.style.opacity = '1';
            loadingSubtextEl.style.opacity = '1';
        }, 150);
    }

    showComponentLoading(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.classList.add('component-loading');
        }
    }

    hideComponentLoading(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.classList.remove('component-loading');
        }
    }

    showSkeletonLoading(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const skeletonHTML = `
            <div class="skeleton-loading-container">
                <div class="skeleton skeleton-card"></div>
                <div class="skeleton skeleton-text long"></div>
                <div class="skeleton skeleton-text medium"></div>
                <div class="skeleton skeleton-text short"></div>
                <div class="skeleton skeleton-chart"></div>
                <div class="skeleton skeleton-text long"></div>
                <div class="skeleton skeleton-text medium"></div>
            </div>
        `;

        container.innerHTML = skeletonHTML;
    }

    hideSkeletonLoading(containerId) {
        const container = document.getElementById(containerId);
        if (container) {
            const skeletonContainer = container.querySelector('.skeleton-loading-container');
            if (skeletonContainer) {
                skeletonContainer.remove();
            }
        }
    }

    async updateDashboard(data) {
        // Update summary metrics
        this.updateSummaryMetrics(data.summary);

        // Load and display charts
        await this.loadCharts();

        // Update data table
        this.updateDataTable(data.raw_data);
    }

    updateSummaryMetrics(summary) {
        const elements = {
            'total-slippage': this.formatCurrency(summary.total_slippage),
            'execution-slippage': this.formatCurrency(summary.execution_slippage),
            'avg-execution-time': summary.avg_execution_time.toFixed(1) + 's',
            'total-turnover': this.formatCurrency(summary.total_turnover),
            'total-trades': summary.total_trades.toLocaleString(),
            'slip-to-turnover': summary.avg_slip_to_turnover.toFixed(2)
        };

        const changes = {
            'slippage-change': summary.slippage_change,
            'exec-slippage-change': summary.exec_slippage_change,
            'exec-time-change': summary.exec_time_change,
            'turnover-change': summary.turnover_change,
            'trades-change': summary.trades_change,
            'ratio-change': summary.ratio_change
        };

        // Update metric values with animation
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                this.animateMetricUpdate(element, value);
            }
        });

        // Update change indicators with animation
        Object.entries(changes).forEach(([id, change]) => {
            const element = document.getElementById(id);
            if (element) {
                const changeText = change > 0 ? `+${change.toFixed(1)}%` : `${change.toFixed(1)}%`;
                const changeClass = change > 0 ? 'positive' : change < 0 ? 'negative' : 'neutral';

                this.animateChangeIndicator(element, changeText, changeClass);
            }
        });

        // Add pulse animation to summary metrics container
        this.addSummaryMetricsPulse();
    }

    animateMetricUpdate(element, newValue) {
        // Store old value for comparison
        const oldValue = element.textContent;

        if (oldValue !== newValue) {
            // Add update animation class
            element.classList.add('metric-updating');

            // Animate the number change
            if (this.isNumericValue(oldValue) && this.isNumericValue(newValue)) {
                this.animateNumberChange(element, oldValue, newValue);
            } else {
                // For non-numeric values, just update with fade effect
                element.style.opacity = '0.5';
                setTimeout(() => {
                    element.textContent = newValue;
                    element.style.opacity = '1';
                    element.classList.remove('metric-updating');
                }, 300);
            }
        } else {
            element.textContent = newValue;
        }
    }

    animateNumberChange(element, oldValue, newValue) {
        const oldNum = this.extractNumber(oldValue);
        const newNum = this.extractNumber(newValue);
        const duration = 1000; // 1 second
        const steps = 30;
        const stepDuration = duration / steps;
        const increment = (newNum - oldNum) / steps;

        let currentStep = 0;
        const timer = setInterval(() => {
            currentStep++;
            const currentValue = oldNum + (increment * currentStep);

            // Format the current value similar to the final value
            let displayValue;
            if (newValue.includes('₹')) {
                displayValue = this.formatCurrency(currentValue);
            } else if (newValue.includes('s')) {
                displayValue = currentValue.toFixed(1) + 's';
            } else if (newValue.includes('.')) {
                displayValue = currentValue.toFixed(2);
            } else {
                displayValue = Math.round(currentValue).toLocaleString();
            }

            element.textContent = displayValue;

            if (currentStep >= steps) {
                clearInterval(timer);
                element.textContent = newValue;
                element.classList.remove('metric-updating');
            }
        }, stepDuration);
    }

    animateChangeIndicator(element, changeText, changeClass) {
        // Add flash animation for change indicators
        element.classList.add('change-flash');
        element.textContent = changeText;
        element.className = `metric-change ${changeClass} change-flash`;

        setTimeout(() => {
            element.classList.remove('change-flash');
        }, 600);
    }

    addSummaryMetricsPulse() {
        const summaryContainer = document.querySelector('.summary-metrics');
        if (summaryContainer) {
            summaryContainer.classList.add('metrics-updated');
            setTimeout(() => {
                summaryContainer.classList.remove('metrics-updated');
            }, 1000);
        }
    }

    isNumericValue(value) {
        const numericValue = this.extractNumber(value);
        return !isNaN(numericValue) && isFinite(numericValue);
    }

    extractNumber(value) {
        if (typeof value === 'number') return value;
        const cleanValue = String(value).replace(/[₹,\s]/g, '').replace(/[a-zA-Z]/g, '');
        return parseFloat(cleanValue) || 0;
    }

    // Real-time simulation methods
    startRealTimeSimulation() {
        // Simulate real-time updates every 30 seconds for demo purposes
        this.realTimeInterval = setInterval(() => {
            if (this.currentData && this.currentData.length > 0) {
                this.simulateDataUpdate();
            }
        }, 30000);
    }

    stopRealTimeSimulation() {
        if (this.realTimeInterval) {
            clearInterval(this.realTimeInterval);
            this.realTimeInterval = null;
        }
    }

    simulateDataUpdate() {
        // Generate slight variations in the summary metrics
        const currentSummary = this.dummyDataGenerator.generateSummaryMetrics(this.currentData);

        // Add some random variation to simulate real-time changes
        const variation = {
            total_slippage: currentSummary.total_slippage * (1 + (Math.random() - 0.5) * 0.1),
            execution_slippage: currentSummary.execution_slippage * (1 + (Math.random() - 0.5) * 0.08),
            avg_execution_time: currentSummary.avg_execution_time * (1 + (Math.random() - 0.5) * 0.15),
            total_turnover: currentSummary.total_turnover * (1 + (Math.random() - 0.5) * 0.05),
            total_trades: Math.round(currentSummary.total_trades * (1 + (Math.random() - 0.5) * 0.12)),
            avg_slip_to_turnover: currentSummary.avg_slip_to_turnover * (1 + (Math.random() - 0.5) * 0.2),
            // Generate new change percentages
            slippage_change: (Math.random() - 0.5) * 30,
            exec_slippage_change: (Math.random() - 0.5) * 25,
            exec_time_change: (Math.random() - 0.5) * 20,
            turnover_change: (Math.random() - 0.5) * 15,
            trades_change: (Math.random() - 0.5) * 25,
            ratio_change: (Math.random() - 0.5) * 35
        };

        // Update the summary metrics with animation
        this.updateSummaryMetrics(variation);

        // Show a subtle notification
        this.showToast('Metrics updated with latest data', 'info');
    }

    addRealTimeControls() {
        // Add a toggle button for real-time simulation
        const controlsContainer = document.querySelector('.filter-row:last-child .filter-group:last-child .d-flex');
        if (controlsContainer && !document.getElementById('realtime-toggle-btn')) {
            const realtimeBtn = document.createElement('button');
            realtimeBtn.id = 'realtime-toggle-btn';
            realtimeBtn.className = 'btn btn-outline-info';
            realtimeBtn.innerHTML = '<i class="fa fa-play"></i> Start Real-time';
            realtimeBtn.title = 'Toggle real-time metric simulation';

            realtimeBtn.addEventListener('click', () => {
                if (this.realTimeInterval) {
                    this.stopRealTimeSimulation();
                    realtimeBtn.innerHTML = '<i class="fa fa-play"></i> Start Real-time';
                    realtimeBtn.className = 'btn btn-outline-info';
                    this.showToast('Real-time simulation stopped', 'info');
                } else {
                    this.startRealTimeSimulation();
                    realtimeBtn.innerHTML = '<i class="fa fa-pause"></i> Stop Real-time';
                    realtimeBtn.className = 'btn btn-outline-warning';
                    this.showToast('Real-time simulation started', 'success');
                }
            });

            controlsContainer.appendChild(realtimeBtn);
        }
    }

    async loadCharts() {
        const chartTypes = ['slippage_trend', 'strategy_comparison', 'timing_histogram'];
        const chartMappings = {
            'slippage_trend': 'overview-chart',
            'strategy_comparison': 'strategy-comparison-chart',
            'timing_histogram': 'timing-histogram-chart'
        };

        // Show skeleton loading for each chart
        chartTypes.forEach(chartType => {
            const elementId = chartMappings[chartType];
            this.showSkeletonLoading(elementId);
        });

        for (let i = 0; i < chartTypes.length; i++) {
            const chartType = chartTypes[i];
            const elementId = chartMappings[chartType];

            try {
                // Add delay for progressive loading effect
                await new Promise(resolve => setTimeout(resolve, i * 300));

                // Generate chart data using dummy data generator
                const chartData = this.dummyDataGenerator.generateChartData(chartType, this.currentData);

                if (chartData) {
                    // Hide skeleton and render chart
                    this.hideSkeletonLoading(elementId);
                    this.renderChart(chartType, chartData.data, chartData.layout);
                }
            } catch (error) {
                console.error(`Error loading ${chartType} chart:`, error);
                this.hideSkeletonLoading(elementId);

                // Show error state
                this.showChartError(elementId, `Failed to load ${chartType} chart`);
            }
        }
    }

    showChartError(elementId, errorMessage) {
        const element = document.getElementById(elementId);
        if (element) {
            element.innerHTML = `
                <div class="chart-error-state">
                    <div class="error-icon">
                        <i class="fa fa-exclamation-triangle"></i>
                    </div>
                    <div class="error-message">${errorMessage}</div>
                    <button class="btn btn-sm btn-outline-primary retry-chart-btn" onclick="window.dashboard.retryChart('${elementId}')">
                        <i class="fa fa-refresh"></i> Retry
                    </button>
                </div>
            `;
        }
    }

    retryChart(elementId) {
        // Find chart type from element ID
        const chartTypeMap = {
            'overview-chart': 'slippage_trend',
            'strategy-comparison-chart': 'strategy_comparison',
            'timing-histogram-chart': 'timing_histogram'
        };

        const chartType = chartTypeMap[elementId];
        if (chartType) {
            this.showComponentLoading(elementId);

            setTimeout(() => {
                try {
                    const chartData = this.dummyDataGenerator.generateChartData(chartType, this.currentData);
                    if (chartData) {
                        this.hideComponentLoading(elementId);
                        this.renderChart(chartType, chartData.data, chartData.layout);
                    }
                } catch (error) {
                    this.hideComponentLoading(elementId);
                    this.showChartError(elementId, `Failed to load ${chartType} chart`);
                }
            }, 1000);
        }
    }

    renderChart(chartType, data, layout) {
        const chartMappings = {
            'slippage_trend': 'overview-chart',
            'strategy_comparison': 'strategy-comparison-chart',
            'timing_histogram': 'timing-histogram-chart'
        };

        const elementId = chartMappings[chartType];
        const element = document.getElementById(elementId);

        if (element) {
            // Clear placeholder content
            element.innerHTML = '';

            // Enhanced layout with SAMBA styling
            const enhancedLayout = {
                ...layout,
                font: {
                    family: 'Raleway, sans-serif',
                    size: 12,
                    color: '#2f4251'
                },
                plot_bgcolor: 'rgba(0,0,0,0)',
                paper_bgcolor: 'rgba(0,0,0,0)',
                margin: { l: 60, r: 40, t: 40, b: 60 },
                showlegend: true,
                legend: {
                    orientation: 'h',
                    x: 0,
                    y: -0.2,
                    bgcolor: 'rgba(255,255,255,0.8)',
                    bordercolor: '#e9ecef',
                    borderwidth: 1
                },
                hoverlabel: {
                    bgcolor: '#4d0000',
                    bordercolor: '#c50000',
                    font: { color: 'white', size: 12 }
                }
            };

            // Enhanced config with more interactive features
            const config = {
                responsive: true,
                displayModeBar: true,
                modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d', 'autoScale2d'],
                modeBarButtonsToAdd: [
                    {
                        name: 'Download PNG',
                        icon: Plotly.Icons.camera,
                        click: function(gd) {
                            Plotly.downloadImage(gd, {
                                format: 'png',
                                width: 1200,
                                height: 600,
                                filename: `${chartType}_${new Date().toISOString().split('T')[0]}`
                            });
                        }
                    }
                ],
                displaylogo: false,
                toImageButtonOptions: {
                    format: 'png',
                    filename: `${chartType}_chart`,
                    height: 600,
                    width: 1200,
                    scale: 1
                }
            };

            // Create the plot
            Plotly.newPlot(elementId, Array.isArray(data) ? data : [data], enhancedLayout, config);

            // Add custom event listeners for interactivity
            this.addChartEventListeners(elementId, chartType);

            this.charts[elementId] = true;

            // Add loading animation completion
            this.animateChartEntry(elementId);
        }
    }

    async runTradeComparison() {
        const comparisonResults = document.getElementById('comparison-results');

        try {
            const filters = {
                start_date: document.getElementById('comparison-start-date').value,
                end_date: document.getElementById('comparison-end-date').value,
                slave_strategy: document.getElementById('comparison-slave-input').value,
                segment: document.getElementById('comparison-segment-select').value,
                exchange: document.getElementById('comparison-exchange-select').value
            };

            if (!filters.slave_strategy) {
                this.showError('Please enter a slave/strategy name for comparison');
                return;
            }

            // Simulate loading delay
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Generate dummy comparison data
            const comparisonData = this.dummyDataGenerator.generateComparisonData();

            // Generate comparison charts
            const exitCountChart = this.dummyDataGenerator.generateChartData('exit_count_comparison');
            const pnlChart = this.dummyDataGenerator.generateChartData('pnl_comparison');
            const holdingTimeChart = this.dummyDataGenerator.generateChartData('holding_time_distribution');

            comparisonData.charts = {
                exit_count_comparison: JSON.stringify(exitCountChart),
                pnl_comparison: JSON.stringify(pnlChart),
                holding_time_distribution: JSON.stringify(holdingTimeChart)
            };

            this.updateComparisonResults(comparisonData);
            comparisonResults.style.display = 'block';

            this.showSuccess('Trade comparison completed successfully');

        } catch (error) {
            console.error('Error running trade comparison:', error);
            this.showError('Error running comparison: ' + error.message);
        }
    }

    updateComparisonResults(data) {
        // Update comparison metrics
        this.updateComparisonMetrics(data);

        // Render comparison charts if available
        if (data.charts) {
            Object.entries(data.charts).forEach(([chartType, chartData]) => {
                try {
                    const chartJson = JSON.parse(chartData);
                    this.renderComparisonChart(chartType, chartJson);
                } catch (error) {
                    console.error(`Error rendering ${chartType} chart:`, error);
                }
            });
        }

        // Update performance summary table
        this.updatePerformanceSummaryTable(data);

        // Generate insights
        this.generatePerformanceInsights(data);

        // Add interactive features to comparison charts
        this.addComparisonChartInteractivity();

        // Update detailed analysis
        this.updateDetailedAnalysis(data);
        this.updateDetailedAnalysis(data);
    }

    updateComparisonMetrics(data) {
        const strategyClusterContainer = document.getElementById('strategy-cluster-metrics');
        const strategyBacktestContainer = document.getElementById('strategy-backtest-metrics');

        if (strategyClusterContainer) {
            strategyClusterContainer.innerHTML = this.generateComparisonMetricsHTML(data.strategy_vs_cluster, 'cluster');
        }

        if (strategyBacktestContainer) {
            strategyBacktestContainer.innerHTML = this.generateComparisonMetricsHTML(data.strategy_vs_backtest, 'backtest');
        }
    }

    generateComparisonMetricsHTML(data, comparisonType) {
        if (!data) return '';

        const metrics = [
            {
                key: 'exit_count_diff',
                label: 'Exit Count Difference',
                formatter: (val) => val > 0 ? `+${val}` : val.toString(),
                getClass: (val) => val > 0 ? 'positive' : val < 0 ? 'negative' : 'neutral'
            },
            {
                key: 'pnl_diff',
                label: 'PnL Difference',
                formatter: (val) => this.formatCurrency(val),
                getClass: (val) => val > 0 ? 'positive' : val < 0 ? 'negative' : 'neutral'
            },
            {
                key: 'avg_holding_time_diff',
                label: 'Avg Holding Time Diff',
                formatter: (val) => `${val > 0 ? '+' : ''}${val.toFixed(1)}min`,
                getClass: (val) => val > 0 ? 'positive' : val < 0 ? 'negative' : 'neutral'
            },
            {
                key: 'short_duration_strategy',
                label: `Short Duration (Strategy)`,
                formatter: (val) => val.toString(),
                getClass: () => 'neutral'
            }
        ];

        return metrics.map(metric => {
            const value = data[metric.key] || 0;
            const formattedValue = metric.formatter(value);
            const cssClass = metric.getClass(value);

            return `
                <div class="comparison-metric-card">
                    <div class="comparison-metric-value ${cssClass}">
                        ${formattedValue}
                    </div>
                    <div class="comparison-metric-label">${metric.label}</div>
                    <div class="comparison-metric-change ${cssClass}">
                        vs ${comparisonType}
                    </div>
                </div>
            `;
        }).join('');
    }

    renderComparisonChart(chartType, chartData) {
        const chartMappings = {
            'exit_count_comparison': 'exit-count-chart',
            'pnl_comparison': 'pnl-comparison-chart',
            'holding_time_distribution': 'holding-time-chart'
        };

        const elementId = chartMappings[chartType];
        const element = document.getElementById(elementId);

        if (element && chartData) {
            element.innerHTML = '';

            // Enhanced config with better interactivity
            const config = {
                responsive: true,
                displayModeBar: true,
                displaylogo: false,
                modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
                modeBarButtonsToAdd: [
                    {
                        name: 'Download PNG',
                        icon: Plotly.Icons.camera,
                        click: function(gd) {
                            Plotly.downloadImage(gd, {
                                format: 'png',
                                width: 1200,
                                height: 600,
                                filename: `${chartType}_${new Date().toISOString().split('T')[0]}`
                            });
                        }
                    }
                ]
            };

            // Enhanced layout
            const enhancedLayout = {
                ...chartData.layout,
                font: {
                    family: 'Raleway, sans-serif',
                    size: 12,
                    color: '#2f4251'
                },
                plot_bgcolor: 'rgba(0,0,0,0)',
                paper_bgcolor: 'rgba(0,0,0,0)',
                hoverlabel: {
                    bgcolor: '#4d0000',
                    bordercolor: '#c50000',
                    font: { color: 'white', size: 12 }
                }
            };

            Plotly.newPlot(elementId, chartData.data, enhancedLayout, config);
            this.charts[elementId] = true;

            // Add animation
            this.animateChartEntry(elementId);
        }
    }

    addComparisonChartInteractivity() {
        // Add event listeners for chart controls
        const toggleExitCountBtn = document.getElementById('toggle-exit-count-view');
        if (toggleExitCountBtn) {
            toggleExitCountBtn.addEventListener('click', () => {
                this.toggleChartView('exit-count-chart');
            });
        }

        const togglePnLBtn = document.getElementById('toggle-pnl-view');
        if (togglePnLBtn) {
            togglePnLBtn.addEventListener('click', () => {
                this.toggleChartView('pnl-comparison-chart');
            });
        }

        const resetPnLZoomBtn = document.getElementById('reset-pnl-zoom');
        if (resetPnLZoomBtn) {
            resetPnLZoomBtn.addEventListener('click', () => {
                this.resetChartZoom('pnl-comparison-chart');
            });
        }

        const holdingTimeBinsSelect = document.getElementById('holding-time-bins');
        if (holdingTimeBinsSelect) {
            holdingTimeBinsSelect.addEventListener('change', (e) => {
                this.updateHoldingTimeChart(parseInt(e.target.value));
            });
        }

        const refreshAnalysisBtn = document.getElementById('refresh-analysis-btn');
        if (refreshAnalysisBtn) {
            refreshAnalysisBtn.addEventListener('click', () => {
                this.refreshDetailedAnalysis();
            });
        }
    }

    updatePerformanceSummaryTable(data) {
        const tableBody = document.getElementById('performance-summary-body');
        if (!tableBody || !data.performance_summary) return;

        const summaryData = data.performance_summary;
        const metrics = [
            { key: 'total_exits', label: 'Total Exits' },
            { key: 'total_pnl', label: 'Total PnL', formatter: this.formatCurrency },
            { key: 'avg_pnl', label: 'Average PnL', formatter: this.formatCurrency },
            { key: 'avg_holding_time', label: 'Avg Holding Time (min)', formatter: (val) => val.toFixed(1) },
            { key: 'max_daily_exits', label: 'Max Daily Exits' },
            { key: 'pnl_volatility', label: 'PnL Volatility', formatter: this.formatCurrency }
        ];

        tableBody.innerHTML = metrics.map(metric => {
            const strategyData = summaryData.find(d => d.type === 'Strategy') || {};
            const clusterData = summaryData.find(d => d.type === 'Cluster') || {};
            const backtestData = summaryData.find(d => d.type === 'Backtest') || {};

            const strategyVal = strategyData[metric.key] || 0;
            const clusterVal = clusterData[metric.key] || 0;
            const backtestVal = backtestData[metric.key] || 0;

            const formatter = metric.formatter || ((val) => val.toLocaleString());

            const strategyVsCluster = strategyVal - clusterVal;
            const strategyVsBacktest = strategyVal - backtestVal;

            return `
                <tr>
                    <td><strong>${metric.label}</strong></td>
                    <td>${formatter(strategyVal)}</td>
                    <td>${formatter(clusterVal)}</td>
                    <td>${formatter(backtestVal)}</td>
                    <td class="${strategyVsCluster > 0 ? 'text-success' : strategyVsCluster < 0 ? 'text-danger' : ''}">
                        ${strategyVsCluster > 0 ? '+' : ''}${formatter(strategyVsCluster)}
                    </td>
                    <td class="${strategyVsBacktest > 0 ? 'text-success' : strategyVsBacktest < 0 ? 'text-danger' : ''}">
                        ${strategyVsBacktest > 0 ? '+' : ''}${formatter(strategyVsBacktest)}
                    </td>
                </tr>
            `;
        }).join('');
    }

    generatePerformanceInsights(data) {
        const insightsContainer = document.getElementById('performance-insights-content');
        if (!insightsContainer) return;

        const insights = [];
        const strategyVsCluster = data.strategy_vs_cluster || {};
        const strategyVsBacktest = data.strategy_vs_backtest || {};

        // Generate insights based on data
        if (strategyVsCluster.exit_count_diff > 0) {
            insights.push({
                type: 'positive',
                text: `Strategy has ${strategyVsCluster.exit_count_diff} more exits than cluster, indicating higher activity.`
            });
        } else if (strategyVsCluster.exit_count_diff < 0) {
            insights.push({
                type: 'negative',
                text: `Strategy has ${Math.abs(strategyVsCluster.exit_count_diff)} fewer exits than cluster.`
            });
        }

        if (strategyVsCluster.pnl_diff > 0) {
            insights.push({
                type: 'positive',
                text: `Strategy outperforms cluster by ${this.formatCurrency(strategyVsCluster.pnl_diff)} in PnL.`
            });
        } else if (strategyVsCluster.pnl_diff < 0) {
            insights.push({
                type: 'negative',
                text: `Strategy underperforms cluster by ${this.formatCurrency(Math.abs(strategyVsCluster.pnl_diff))} in PnL.`
            });
        }

        if (strategyVsBacktest.pnl_diff < 0) {
            insights.push({
                type: 'neutral',
                text: `Strategy shows ${this.formatCurrency(Math.abs(strategyVsBacktest.pnl_diff))} gap vs backtest - consider optimization.`
            });
        }

        if (insights.length === 0) {
            insights.push({
                type: 'neutral',
                text: 'Performance metrics are closely aligned across all comparison types.'
            });
        }

        insightsContainer.innerHTML = insights.map(insight => `
            <div class="insight-item ${insight.type}">
                ${insight.text}
            </div>
        `).join('');
    }

    updateDetailedAnalysis(data) {
        const resultsContainer = document.getElementById('detailed-analysis-results');
        if (!resultsContainer) return;

        const analysisType = document.getElementById('analysis-type-select')?.value || 'daily';
        const metricFocus = document.getElementById('metric-focus-select')?.value || 'all';

        resultsContainer.innerHTML = `
            <div class="analysis-summary">
                <h6>Detailed ${analysisType.charAt(0).toUpperCase() + analysisType.slice(1)} Analysis</h6>
                <p class="text-muted">Focus: ${metricFocus === 'all' ? 'All Metrics' : metricFocus.replace('_', ' ').toUpperCase()}</p>

                <div class="row">
                    <div class="col-md-4">
                        <div class="analysis-stat">
                            <div class="stat-value">${data.data_availability?.strategy_range || 'N/A'}</div>
                            <div class="stat-label">Strategy Data Range</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="analysis-stat">
                            <div class="stat-value">${data.data_availability?.cluster_range || 'N/A'}</div>
                            <div class="stat-label">Cluster Data Range</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="analysis-stat">
                            <div class="stat-value">${data.data_availability?.backtest_range || 'N/A'}</div>
                            <div class="stat-label">Backtest Data Range</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    toggleChartView(chartId) {
        const element = document.getElementById(chartId);
        if (!element || !element.data) return;

        // Toggle between line and bar chart for exit count
        if (chartId === 'exit-count-chart') {
            const currentType = element.data[0].type;
            const newType = currentType === 'scatter' ? 'bar' : 'scatter';

            const update = {
                type: [newType, newType, newType],
                mode: newType === 'scatter' ? ['lines+markers', 'lines+markers', 'lines+markers'] : [undefined, undefined, undefined]
            };

            Plotly.restyle(chartId, update);
        }
    }

    resetChartZoom(chartId) {
        const element = document.getElementById(chartId);
        if (!element) return;

        Plotly.relayout(chartId, {
            'xaxis.autorange': true,
            'yaxis.autorange': true
        });
    }

    updateHoldingTimeChart(bins) {
        // Re-render holding time chart with new bin count
        const chartId = 'holding-time-chart';
        const element = document.getElementById(chartId);

        if (element && element.data) {
            const update = { nbinsx: bins };
            Plotly.restyle(chartId, update);
        }
    }

    refreshDetailedAnalysis() {
        // Re-run the comparison with current settings
        this.runTradeComparison();
    }

    updateDataTable(rawData) {
        if (!rawData) return;

        this.currentData = rawData;
        this.filteredData = [...rawData];
        this.currentPage = 1;

        this.updateSummaryStats();
        this.updateTable();
    }

    updateTable() {
        const tableBody = document.getElementById('analysis-table-body');
        if (!tableBody || !this.filteredData) return;

        // Calculate pagination
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = this.pageSize === 999999 ? this.filteredData.length : startIndex + this.pageSize;
        const pageData = this.filteredData.slice(startIndex, endIndex);

        // Clear table
        tableBody.innerHTML = '';

        // Populate table with enhanced formatting
        pageData.forEach((row, index) => {
            const tr = document.createElement('tr');
            tr.className = 'table-row';
            tr.setAttribute('data-row-index', startIndex + index);

            // Add row highlighting based on slippage values
            const slippageClass = this.getSlippageClass(row.total_slippage);
            if (slippageClass) {
                tr.classList.add(slippageClass);
            }

            tr.innerHTML = `
                <td class="strategy-cell" title="${row.strategy_name || 'N/A'}">
                    <span class="strategy-name">${this.truncateText(row.strategy_name || 'N/A', 20)}</span>
                </td>
                <td class="slave-cell" title="${row.slave_name || 'N/A'}">
                    <span class="slave-name">${this.truncateText(row.slave_name || 'N/A', 15)}</span>
                </td>
                <td class="date-cell">
                    <span class="date-value">${this.formatDate(row.date)}</span>
                </td>
                <td class="slippage-cell ${this.getSlippageClass(row.total_slippage)}">
                    <span class="slippage-value">${this.formatCurrency(row.total_slippage || 0)}</span>
                    <div class="slippage-bar" style="width: ${this.getSlippageBarWidth(row.total_slippage)}%"></div>
                </td>
                <td class="execution-slippage-cell">
                    <span class="execution-slippage-value">${this.formatCurrency(row.execution_slippage || 0)}</span>
                </td>
                <td class="turnover-cell">
                    <span class="turnover-value">${this.formatCurrency(row.turnover || 0)}</span>
                </td>
                <td class="trade-count-cell">
                    <span class="trade-count-value">${(row.trade_count || 0).toLocaleString()}</span>
                    <div class="trade-count-indicator" style="width: ${this.getTradeCountBarWidth(row.trade_count)}%"></div>
                </td>
                <td class="ratio-cell">
                    <span class="ratio-value">${(row.total_slip_to_turnover || 0).toFixed(2)}</span>
                    <span class="ratio-unit">BPS</span>
                </td>
            `;

            // Add click event for row details
            tr.addEventListener('click', () => {
                this.showRowDetails(row, startIndex + index);
            });

            // Add hover effects
            tr.addEventListener('mouseenter', () => {
                tr.classList.add('row-hover');
            });

            tr.addEventListener('mouseleave', () => {
                tr.classList.remove('row-hover');
            });

            tableBody.appendChild(tr);
        });

        this.updatePaginationControls();
        this.addTableAnimations();
    }

    updatePaginationControls() {
        const totalRows = this.filteredData?.length || 0;
        const totalPages = Math.ceil(totalRows / this.pageSize);
        const startIndex = (this.currentPage - 1) * this.pageSize + 1;
        const endIndex = Math.min(startIndex + this.pageSize - 1, totalRows);

        // Update pagination info
        document.getElementById('showing-start').textContent = totalRows > 0 ? startIndex : 0;
        document.getElementById('showing-end').textContent = totalRows > 0 ? endIndex : 0;
        document.getElementById('total-rows').textContent = totalRows;

        // Update pagination buttons
        document.getElementById('prev-page-btn').disabled = this.currentPage <= 1;
        document.getElementById('next-page-btn').disabled = this.currentPage >= totalPages;
    }

    updateSummaryStats() {
        if (!this.filteredData) return;

        const totalRecords = this.currentData?.length || 0;
        const filteredRecords = this.filteredData.length;
        const sumTotalSlippage = this.filteredData.reduce((sum, row) => sum + (row.total_slippage || 0), 0);
        const avgSlipRatio = this.filteredData.length > 0 ?
            this.filteredData.reduce((sum, row) => sum + (row.total_slip_to_turnover || 0), 0) / this.filteredData.length : 0;

        document.getElementById('total-records').textContent = totalRecords.toLocaleString();
        document.getElementById('filtered-records').textContent = filteredRecords.toLocaleString();
        document.getElementById('sum-total-slippage').textContent = this.formatCurrency(sumTotalSlippage);
        document.getElementById('avg-slip-ratio').textContent = avgSlipRatio.toFixed(2) + ' BPS';
    }

    applyGrouping() {
        if (!this.currentData) return;

        const groupBy = document.getElementById('group-by-select').value;
        const aggregation = document.getElementById('aggregation-select').value;

        // Group data
        const grouped = this.groupData(this.currentData, groupBy, aggregation);
        this.filteredData = grouped;
        this.currentPage = 1;

        this.updateSummaryStats();
        this.updateTable();
    }

    groupData(data, groupBy, aggregation) {
        const groups = {};

        data.forEach(row => {
            const key = row[groupBy] || 'Unknown';
            if (!groups[key]) {
                groups[key] = [];
            }
            groups[key].push(row);
        });

        return Object.entries(groups).map(([key, rows]) => {
            const result = { [groupBy]: key };

            // Calculate aggregations
            const numericFields = ['total_slippage', 'execution_slippage', 'turnover', 'trade_count', 'total_slip_to_turnover'];

            numericFields.forEach(field => {
                const values = rows.map(row => row[field] || 0).filter(val => !isNaN(val));

                switch (aggregation) {
                    case 'sum':
                        result[field] = values.reduce((sum, val) => sum + val, 0);
                        break;
                    case 'avg':
                        result[field] = values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0;
                        break;
                    case 'count':
                        result[field] = values.length;
                        break;
                    case 'max':
                        result[field] = values.length > 0 ? Math.max(...values) : 0;
                        break;
                    case 'min':
                        result[field] = values.length > 0 ? Math.min(...values) : 0;
                        break;
                    default:
                        result[field] = values.reduce((sum, val) => sum + val, 0);
                }
            });

            // Add other fields from first row
            const firstRow = rows[0];
            Object.keys(firstRow).forEach(key => {
                if (!result.hasOwnProperty(key) && !numericFields.includes(key)) {
                    result[key] = firstRow[key];
                }
            });

            return result;
        });
    }

    applyColumnFilter() {
        if (!this.currentData) return;

        const filterColumn = document.getElementById('column-filter-select').value;
        const filterValue = document.getElementById('column-filter-input').value.toLowerCase();

        if (!filterColumn || !filterValue) {
            this.filteredData = [...this.currentData];
        } else {
            this.filteredData = this.currentData.filter(row => {
                const cellValue = String(row[filterColumn] || '').toLowerCase();
                return cellValue.includes(filterValue);
            });
        }

        this.currentPage = 1;
        this.updateSummaryStats();
        this.updateTable();
    }

    sortTable(column) {
        if (!this.filteredData) return;

        // Update sort direction
        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = column;
            this.sortDirection = 'asc';
        }

        // Sort data
        this.filteredData.sort((a, b) => {
            let aVal = a[column];
            let bVal = b[column];

            // Handle numeric values
            if (!isNaN(aVal) && !isNaN(bVal)) {
                aVal = parseFloat(aVal) || 0;
                bVal = parseFloat(bVal) || 0;
            } else {
                aVal = String(aVal || '').toLowerCase();
                bVal = String(bVal || '').toLowerCase();
            }

            let result = 0;
            if (aVal < bVal) result = -1;
            if (aVal > bVal) result = 1;

            return this.sortDirection === 'desc' ? -result : result;
        });

        // Update sort indicators
        document.querySelectorAll('.sortable').forEach(th => {
            th.classList.remove('sorted-asc', 'sorted-desc');
        });

        const currentTh = document.querySelector(`[data-column="${column}"]`);
        if (currentTh) {
            currentTh.classList.add(this.sortDirection === 'asc' ? 'sorted-asc' : 'sorted-desc');
        }

        this.updateTable();
    }

    exportData(format) {
        if (!this.filteredData || this.filteredData.length === 0) {
            this.showError('No data to export');
            return;
        }

        // Show export progress
        this.showExportProgress();

        const timestamp = new Date().toISOString().split('T')[0];
        const filename = `slippage_analysis_${timestamp}`;

        const headers = [
            'Strategy', 'Slave', 'Date', 'Segment', 'Exchange',
            'Total Slippage (₹)', 'Execution Slippage (₹)', 'Turnover (₹)',
            'Trade Count', 'Slip-to-Turnover (BPS)', 'Avg Execution Time (s)'
        ];

        const data = this.filteredData.map(row => [
            row.strategy_name || 'N/A',
            row.slave_name || 'N/A',
            row.date || 'N/A',
            row.segment || 'N/A',
            row.exchange || 'N/A',
            (row.total_slippage || 0).toFixed(2),
            (row.execution_slippage || 0).toFixed(2),
            (row.turnover || 0).toFixed(2),
            row.trade_count || 0,
            (row.total_slip_to_turnover || 0).toFixed(4),
            (row.avg_execution_time || 0).toFixed(3)
        ]);

        // Add summary row
        const summaryRow = this.generateSummaryRow();
        data.push(['', '', '', '', '', '', '', '', '', '', '']); // Empty row
        data.push(['SUMMARY', '', '', '', '',
            summaryRow.totalSlippage, summaryRow.totalExecutionSlippage,
            summaryRow.totalTurnover, summaryRow.totalTrades,
            summaryRow.avgRatio, summaryRow.avgExecutionTime]);

        setTimeout(() => {
            if (format === 'csv') {
                this.downloadCSV(headers, data, `${filename}.csv`);
            } else if (format === 'excel') {
                this.downloadExcel(headers, data, `${filename}.xlsx`);
            }
            this.hideExportProgress();
            this.showSuccess(`Data exported successfully as ${format.toUpperCase()}`);
        }, 1000);
    }

    generateSummaryRow() {
        const totalSlippage = this.filteredData.reduce((sum, row) => sum + (row.total_slippage || 0), 0);
        const totalExecutionSlippage = this.filteredData.reduce((sum, row) => sum + (row.execution_slippage || 0), 0);
        const totalTurnover = this.filteredData.reduce((sum, row) => sum + (row.turnover || 0), 0);
        const totalTrades = this.filteredData.reduce((sum, row) => sum + (row.trade_count || 0), 0);
        const avgRatio = this.filteredData.length > 0 ?
            this.filteredData.reduce((sum, row) => sum + (row.total_slip_to_turnover || 0), 0) / this.filteredData.length : 0;
        const avgExecutionTime = this.filteredData.length > 0 ?
            this.filteredData.reduce((sum, row) => sum + (row.avg_execution_time || 0), 0) / this.filteredData.length : 0;

        return {
            totalSlippage: totalSlippage.toFixed(2),
            totalExecutionSlippage: totalExecutionSlippage.toFixed(2),
            totalTurnover: totalTurnover.toFixed(2),
            totalTrades: totalTrades,
            avgRatio: avgRatio.toFixed(4),
            avgExecutionTime: avgExecutionTime.toFixed(3)
        };
    }

    showExportProgress() {
        const progressModal = document.createElement('div');
        progressModal.id = 'export-progress-modal';
        progressModal.className = 'export-progress-modal';
        progressModal.innerHTML = `
            <div class="modal-content">
                <div class="export-progress-content">
                    <div class="spinner"></div>
                    <h5>Preparing Export...</h5>
                    <p>Processing ${this.filteredData.length} records</p>
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(progressModal);

        // Animate progress bar
        setTimeout(() => {
            const progressFill = progressModal.querySelector('.progress-fill');
            progressFill.style.width = '100%';
        }, 100);
    }

    hideExportProgress() {
        const progressModal = document.getElementById('export-progress-modal');
        if (progressModal) {
            progressModal.remove();
        }
    }

    downloadCSV(headers, data, filename) {
        // Enhanced CSV generation with proper escaping and formatting
        const escapeCsvCell = (cell) => {
            const cellStr = String(cell || '');
            // Escape quotes and wrap in quotes if contains comma, quote, or newline
            if (cellStr.includes(',') || cellStr.includes('"') || cellStr.includes('\n')) {
                return `"${cellStr.replace(/"/g, '""')}"`;
            }
            return cellStr;
        };

        // Add metadata header
        const timestamp = new Date().toISOString();
        const metadata = [
            `# Advanced Slippage Analysis Export`,
            `# Generated on: ${timestamp}`,
            `# Total Records: ${data.length - 2}`, // Subtract header and summary rows
            `# Filters Applied: ${this.getActiveFiltersDescription()}`,
            `#`,
            ``
        ];

        const csvContent = [
            ...metadata,
            headers.map(escapeCsvCell).join(','),
            ...data.map(row => row.map(escapeCsvCell).join(','))
        ].join('\n');

        // Add BOM for proper UTF-8 encoding in Excel
        const BOM = '\uFEFF';
        const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Cleanup
        setTimeout(() => URL.revokeObjectURL(link.href), 100);
    }

    downloadExcel(headers, data, filename) {
        // Enhanced Excel export with proper formatting and styling
        const timestamp = new Date().toISOString();
        const activeFilters = this.getActiveFiltersDescription();

        const excelHTML = `
            <html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
            <head>
                <meta charset="utf-8">
                <meta name="ProgId" content="Excel.Sheet">
                <meta name="Generator" content="Advanced Slippage Dashboard">
                <!--[if gte mso 9]>
                <xml>
                    <x:ExcelWorkbook>
                        <x:ExcelWorksheets>
                            <x:ExcelWorksheet>
                                <x:Name>Slippage Analysis</x:Name>
                                <x:WorksheetOptions>
                                    <x:DisplayGridlines/>
                                </x:WorksheetOptions>
                            </x:ExcelWorksheet>
                        </x:ExcelWorksheets>
                    </x:ExcelWorkbook>
                </xml>
                <![endif]-->
                <style>
                    .header { background-color: #4d0000; color: white; font-weight: bold; text-align: center; }
                    .summary { background-color: #f8f9fa; font-weight: bold; }
                    .positive { color: #28a745; }
                    .negative { color: #dc3545; }
                    .currency { text-align: right; }
                    .number { text-align: right; }
                    .metadata { font-style: italic; color: #6c757d; }
                </style>
            </head>
            <body>
                <h2>Advanced Slippage Analysis Report</h2>
                <p class="metadata">Generated on: ${timestamp}</p>
                <p class="metadata">Active Filters: ${activeFilters}</p>
                <p class="metadata">Total Records: ${data.length - 2}</p>
                <br>

                <table border="1" cellpadding="5" cellspacing="0">
                    <thead>
                        <tr class="header">
                            ${headers.map(header => `<th>${header}</th>`).join('')}
                        </tr>
                    </thead>
                    <tbody>
                        ${data.map((row) => {
                            const isSummaryRow = row[0] === 'SUMMARY' || row[0] === '';
                            const rowClass = isSummaryRow ? 'summary' : '';

                            return `<tr class="${rowClass}">
                                ${row.map((cell, cellIndex) => {
                                    let cellClass = '';
                                    let cellValue = cell;

                                    // Apply formatting based on content
                                    if (typeof cell === 'string' && cell.includes('₹')) {
                                        cellClass = 'currency';
                                    } else if (!isNaN(cell) && cell !== '' && cellIndex > 4) {
                                        cellClass = 'number';
                                        if (parseFloat(cell) > 0 && cellIndex > 4) {
                                            cellClass += ' positive';
                                        } else if (parseFloat(cell) < 0) {
                                            cellClass += ' negative';
                                        }
                                    }

                                    return `<td class="${cellClass}">${cellValue}</td>`;
                                }).join('')}
                            </tr>`;
                        }).join('')}
                    </tbody>
                </table>

                <br>
                <p class="metadata">
                    <small>
                        Report generated by Advanced Slippage Dashboard<br>
                        Data includes slippage metrics, execution statistics, and performance analysis
                    </small>
                </p>
            </body>
            </html>
        `;

        const blob = new Blob([excelHTML], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Cleanup
        setTimeout(() => URL.revokeObjectURL(link.href), 100);
    }

    getActiveFiltersDescription() {
        const filters = [];

        const startDate = document.getElementById('start-date')?.value;
        const endDate = document.getElementById('end-date')?.value;
        if (startDate && endDate) {
            filters.push(`Date Range: ${startDate} to ${endDate}`);
        }

        const segment = document.getElementById('segment-select')?.value;
        if (segment) {
            filters.push(`Segment: ${segment}`);
        }

        const exchange = document.getElementById('exchange-select')?.value;
        if (exchange) {
            filters.push(`Exchange: ${exchange}`);
        }

        const strategies = this.getMultiSelectValues('strategy-select');
        if (strategies.length > 0) {
            filters.push(`Strategies: ${strategies.join(', ')}`);
        }

        const slaves = this.getMultiSelectValues('slave-select');
        if (slaves.length > 0) {
            filters.push(`Slaves: ${slaves.join(', ')}`);
        }

        return filters.length > 0 ? filters.join(' | ') : 'No filters applied';
    }

    // Add PDF export functionality
    exportToPDF() {
        this.showToast('PDF export functionality coming soon!', 'info');
        // TODO: Implement PDF export using libraries like jsPDF or Puppeteer
    }

    // Add JSON export functionality
    exportToJSON() {
        if (!this.filteredData || this.filteredData.length === 0) {
            this.showError('No data to export');
            return;
        }

        const timestamp = new Date().toISOString();
        const exportData = {
            metadata: {
                exportDate: timestamp,
                totalRecords: this.filteredData.length,
                activeFilters: this.getActiveFiltersDescription(),
                generatedBy: 'Advanced Slippage Dashboard'
            },
            summary: this.generateSummaryRow(),
            data: this.filteredData
        };

        const jsonContent = JSON.stringify(exportData, null, 2);
        const blob = new Blob([jsonContent], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `slippage_analysis_${timestamp.split('T')[0]}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        setTimeout(() => URL.revokeObjectURL(link.href), 100);
        this.showSuccess('Data exported successfully as JSON');
    }

    async exportAllFormats() {
        if (!this.filteredData || this.filteredData.length === 0) {
            this.showError('No data to export');
            return;
        }

        this.showExportProgress();

        try {
            // Export all three formats with a small delay between each
            await new Promise(resolve => setTimeout(resolve, 500));
            this.exportData('csv');

            await new Promise(resolve => setTimeout(resolve, 500));
            this.exportData('excel');

            await new Promise(resolve => setTimeout(resolve, 500));
            this.exportToJSON();

            this.hideExportProgress();
            this.showSuccess('All formats exported successfully (CSV, Excel, JSON)');
        } catch (error) {
            this.hideExportProgress();
            this.showError('Error during bulk export: ' + error.message);
        }
    }

    exportSummaryOnly() {
        if (!this.filteredData || this.filteredData.length === 0) {
            this.showError('No data to export');
            return;
        }

        const summaryData = this.generateSummaryRow();
        const headers = ['Metric', 'Value'];
        const data = [
            ['Total Slippage', summaryData.totalSlippage],
            ['Total Execution Slippage', summaryData.totalExecutionSlippage],
            ['Total Turnover', summaryData.totalTurnover],
            ['Total Trades', summaryData.totalTrades],
            ['Average Slip-to-Turnover Ratio', summaryData.avgRatio],
            ['Average Execution Time', summaryData.avgExecutionTime],
            ['Total Records', this.filteredData.length],
            ['Export Date', new Date().toISOString()],
            ['Active Filters', this.getActiveFiltersDescription()]
        ];

        const timestamp = new Date().toISOString().split('T')[0];
        this.downloadCSV(headers, data, `slippage_summary_${timestamp}.csv`);
        this.showSuccess('Summary exported successfully');
    }

    async exportChartsAsImages() {
        const chartIds = ['overview-chart', 'strategy-comparison-chart', 'timing-histogram-chart'];
        const exportedCharts = [];

        this.showExportProgress();

        try {
            for (const chartId of chartIds) {
                const element = document.getElementById(chartId);
                if (element && this.charts[chartId]) {
                    try {
                        // Use Plotly's built-in image export
                        const imgData = await Plotly.toImage(element, {
                            format: 'png',
                            width: 1200,
                            height: 600,
                            scale: 2
                        });

                        // Create download link
                        const link = document.createElement('a');
                        link.href = imgData;
                        link.download = `${chartId}_${new Date().toISOString().split('T')[0]}.png`;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);

                        exportedCharts.push(chartId);

                        // Small delay between downloads
                        await new Promise(resolve => setTimeout(resolve, 500));
                    } catch (chartError) {
                        console.warn(`Failed to export chart ${chartId}:`, chartError);
                    }
                }
            }

            this.hideExportProgress();

            if (exportedCharts.length > 0) {
                this.showSuccess(`${exportedCharts.length} chart(s) exported as PNG images`);
            } else {
                this.showError('No charts available for export');
            }
        } catch (error) {
            this.hideExportProgress();
            this.showError('Error exporting charts: ' + error.message);
        }
    }

    async loadAvailableSlaves() {
        try {
            // Simulate loading delay
            await new Promise(resolve => setTimeout(resolve, 800));

            // Get available slaves from dummy data generator
            const availableSlaves = this.dummyDataGenerator.slaves;

            // Update the slave multi-select dropdown with available slaves
            this.updateMultiSelectOptions('slave-select', availableSlaves.map(slave => ({
                value: slave,
                label: slave,
                selected: false
            })));

            this.showSuccess(`Loaded ${availableSlaves.length} available slaves successfully`);
        } catch (error) {
            console.error('Error loading slaves:', error);
            this.showError('Error loading available slaves');
        }
    }

    addChartEventListeners(elementId, chartType) {
        const element = document.getElementById(elementId);
        if (!element) return;

        // Add click event for data points
        element.on('plotly_click', (data) => {
            if (data.points && data.points.length > 0) {
                const point = data.points[0];
                this.handleChartClick(chartType, point);
            }
        });

        // Add hover events for enhanced tooltips
        element.on('plotly_hover', (data) => {
            if (data.points && data.points.length > 0) {
                const point = data.points[0];
                this.handleChartHover(chartType, point, true);
            }
        });

        element.on('plotly_unhover', () => {
            this.handleChartHover(chartType, null, false);
        });

        // Add double-click to reset zoom
        element.on('plotly_doubleclick', () => {
            Plotly.relayout(elementId, {
                'xaxis.autorange': true,
                'yaxis.autorange': true
            });
        });
    }

    handleChartClick(chartType, point) {
        // Show detailed information about the clicked data point
        const info = this.getDataPointInfo(chartType, point);
        this.showDataPointModal(info);
    }

    handleChartHover(chartType, point, isHovering) {
        if (isHovering && point) {
            // Could add custom hover effects here
            console.log(`Hovering over ${chartType}:`, point);
        }
    }

    getDataPointInfo(chartType, point) {
        const info = {
            chartType: chartType,
            x: point.x,
            y: point.y,
            curveNumber: point.curveNumber,
            pointNumber: point.pointNumber
        };

        switch (chartType) {
            case 'slippage_trend':
                info.title = 'Slippage Trend Data Point';
                info.details = [
                    `Date: ${point.x}`,
                    `Value: ${this.formatCurrency(point.y)}`,
                    `Series: ${point.data.name || 'Unknown'}`
                ];
                break;
            case 'strategy_comparison':
                info.title = 'Strategy Performance';
                info.details = [
                    `Strategy: ${point.x}`,
                    `Average Slippage: ${this.formatCurrency(point.y)}`,
                    `Click to view detailed analysis`
                ];
                break;
            case 'timing_histogram':
                info.title = 'Execution Time Distribution';
                info.details = [
                    `Execution Time: ${point.x}s`,
                    `Frequency: ${point.y}`,
                    `Bin Range: ${point.x - 0.1}s - ${point.x + 0.1}s`
                ];
                break;
        }

        return info;
    }

    showDataPointModal(info) {
        // Create a simple modal for data point details
        const modal = document.createElement('div');
        modal.className = 'chart-data-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h5>${info.title}</h5>
                    <button class="modal-close" onclick="this.parentElement.parentElement.parentElement.remove()">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    ${info.details.map(detail => `<p>${detail}</p>`).join('')}
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (modal.parentElement) {
                modal.remove();
            }
        }, 5000);
    }

    animateChartEntry(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';
            element.style.transition = 'all 0.6s ease';

            setTimeout(() => {
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, 100);
        }
    }

    handleChartResize() {
        Object.keys(this.charts).forEach(chartId => {
            const element = document.getElementById(chartId);
            if (element && element.data) {
                try {
                    Plotly.Plots.resize(element);
                } catch (error) {
                    console.warn(`Failed to resize chart ${chartId}:`, error);
                }
            }
        });
    }

    // Enhanced table utility functions
    getSlippageClass(slippage) {
        const value = parseFloat(slippage) || 0;
        if (value > 50000) return 'high-slippage';
        if (value > 10000) return 'medium-slippage';
        if (value < -10000) return 'negative-slippage';
        return 'low-slippage';
    }

    getSlippageBarWidth(slippage) {
        const value = Math.abs(parseFloat(slippage) || 0);
        const maxSlippage = 100000; // Adjust based on your data range
        return Math.min((value / maxSlippage) * 100, 100);
    }

    getTradeCountBarWidth(tradeCount) {
        const value = parseFloat(tradeCount) || 0;
        const maxTrades = 500; // Adjust based on your data range
        return Math.min((value / maxTrades) * 100, 100);
    }

    truncateText(text, maxLength) {
        if (!text || text.length <= maxLength) return text;
        return text.substring(0, maxLength - 3) + '...';
    }

    formatDate(dateStr) {
        if (!dateStr) return 'N/A';
        const date = new Date(dateStr);
        return date.toLocaleDateString('en-US', {
            month: 'short',
            day: '2-digit',
            year: '2-digit'
        });
    }

    showRowDetails(row, rowIndex) {
        const modal = document.createElement('div');
        modal.className = 'row-details-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h5><i class="fa fa-info-circle"></i> Row Details - ${row.strategy_name}</h5>
                    <button class="modal-close" onclick="this.parentElement.parentElement.parentElement.remove()">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row-details-grid">
                        <div class="detail-item">
                            <label>Strategy:</label>
                            <span>${row.strategy_name || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Slave:</label>
                            <span>${row.slave_name || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Date:</label>
                            <span>${row.date || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Segment:</label>
                            <span>${row.segment || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Exchange:</label>
                            <span>${row.exchange || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Total Slippage:</label>
                            <span class="${this.getSlippageClass(row.total_slippage)}">${this.formatCurrency(row.total_slippage || 0)}</span>
                        </div>
                        <div class="detail-item">
                            <label>Execution Slippage:</label>
                            <span>${this.formatCurrency(row.execution_slippage || 0)}</span>
                        </div>
                        <div class="detail-item">
                            <label>Turnover:</label>
                            <span>${this.formatCurrency(row.turnover || 0)}</span>
                        </div>
                        <div class="detail-item">
                            <label>Trade Count:</label>
                            <span>${(row.trade_count || 0).toLocaleString()}</span>
                        </div>
                        <div class="detail-item">
                            <label>Slip-to-Turnover Ratio:</label>
                            <span>${(row.total_slip_to_turnover || 0).toFixed(4)} BPS</span>
                        </div>
                        <div class="detail-item">
                            <label>Avg Execution Time:</label>
                            <span>${(row.avg_execution_time || 0).toFixed(2)}s</span>
                        </div>
                        <div class="detail-item">
                            <label>Row Index:</label>
                            <span>#${rowIndex + 1}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (modal.parentElement) {
                modal.remove();
            }
        }, 10000);
    }

    addTableAnimations() {
        const rows = document.querySelectorAll('#analysis-table-body tr');
        rows.forEach((row, index) => {
            row.style.opacity = '0';
            row.style.transform = 'translateY(20px)';
            row.style.transition = 'all 0.3s ease';

            setTimeout(() => {
                row.style.opacity = '1';
                row.style.transform = 'translateY(0)';
            }, index * 50);
        });
    }

    // Utility functions
    parseCommaSeparated(value) {
        return value ? value.split(',').map(s => s.trim()).filter(s => s) : [];
    }

    formatNumber(num) {
        const n = parseFloat(num);
        if (isNaN(n)) return '0';
        if (Math.abs(n) >= 1e6) return (n / 1e6).toFixed(1) + 'M';
        if (Math.abs(n) >= 1e3) return (n / 1e3).toFixed(1) + 'K';
        return n.toFixed(2);
    }

    formatCurrency(num) {
        const n = parseFloat(num);
        if (isNaN(n)) return '₹0';
        return '₹' + this.formatNumber(n);
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    showError(message) {
        this.showToast(message, 'error');
    }

    showSuccess(message) {
        this.showToast(message, 'success');
    }

    showToast(message, type = 'info') {
        // Create toast container if it doesn't exist
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'toast-container';
            document.body.appendChild(toastContainer);
        }

        // Create toast element
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fa fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span class="toast-message">${message}</span>
                <button class="toast-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fa fa-times"></i>
                </button>
            </div>
        `;

        // Add toast to container
        toastContainer.appendChild(toast);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 5000);

        // Add slide-in animation
        setTimeout(() => {
            toast.classList.add('toast-show');
        }, 100);
    }
}

/**
 * Dummy Data Generator Class
 * Provides realistic sample data for dashboard components
 */
class DummyDataGenerator {
    constructor() {
        this.strategies = [
            'Alpha_Momentum_V1', 'Beta_Arbitrage_V2', 'Gamma_MeanReversion_V3',
            'Delta_Scalping_V1', 'Epsilon_Pairs_V2', 'Zeta_Volatility_V1',
            'Eta_Breakout_V2', 'Theta_Grid_V1', 'Iota_Swing_V3', 'Kappa_News_V1'
        ];

        this.slaves = [
            'slave_alpha_prod_01', 'slave_alpha_prod_02', 'slave_beta_test_01',
            'slave_gamma_prod_01', 'slave_delta_prod_01', 'slave_epsilon_test_01',
            'slave_zeta_prod_01', 'slave_eta_prod_01', 'slave_theta_test_01',
            'slave_iota_prod_01', 'slave_kappa_prod_01', 'slave_lambda_test_01'
        ];

        this.segments = ['OPTIDX', 'OPTIDX_BSE', 'OPTIDX_US', 'OPTSTK', 'FUTSTK'];
        this.exchanges = ['IND', 'NSE', 'BSE', 'US'];
    }

    generateSlippageData(days = 30, recordsPerDay = 50) {
        const data = [];
        const endDate = new Date();

        for (let i = 0; i < days; i++) {
            const date = new Date(endDate);
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];

            for (let j = 0; j < recordsPerDay; j++) {
                const strategy = this.getRandomElement(this.strategies);
                const slave = this.getRandomElement(this.slaves);
                const segment = this.getRandomElement(this.segments);
                const exchange = this.getRandomElement(this.exchanges);

                // Generate realistic slippage values
                const baseSlippage = this.getRandomFloat(-50000, 150000);
                const executionSlippage = baseSlippage * this.getRandomFloat(0.6, 0.9);
                const turnover = this.getRandomFloat(1000000, 50000000);
                const tradeCount = this.getRandomInt(10, 500);
                const slipToTurnoverBps = (baseSlippage / turnover) * 10000;

                data.push({
                    strategy_name: strategy,
                    slave_name: slave,
                    segment: segment,
                    exchange: exchange,
                    date: dateStr,
                    total_slippage: baseSlippage,
                    execution_slippage: executionSlippage,
                    turnover: turnover,
                    trade_count: tradeCount,
                    total_slip_to_turnover: slipToTurnoverBps,
                    avg_execution_time: this.getRandomFloat(0.5, 5.0)
                });
            }
        }

        return data;
    }

    generateSummaryMetrics(data) {
        const totalSlippage = data.reduce((sum, row) => sum + row.total_slippage, 0);
        const totalTurnover = data.reduce((sum, row) => sum + row.turnover, 0);
        const totalTrades = data.reduce((sum, row) => sum + row.trade_count, 0);
        const avgSlipToTurnover = data.length > 0 ?
            data.reduce((sum, row) => sum + row.total_slip_to_turnover, 0) / data.length : 0;
        const avgExecutionTime = data.length > 0 ?
            data.reduce((sum, row) => sum + row.avg_execution_time, 0) / data.length : 0;

        return {
            total_slippage: totalSlippage,
            execution_slippage: totalSlippage * 0.75, // Mock calculation
            avg_execution_time: avgExecutionTime,
            total_turnover: totalTurnover,
            total_trades: totalTrades,
            avg_slip_to_turnover: avgSlipToTurnover,
            // Add change percentages for metrics
            slippage_change: this.getRandomFloat(-15, 25),
            exec_slippage_change: this.getRandomFloat(-10, 20),
            exec_time_change: this.getRandomFloat(-5, 15),
            turnover_change: this.getRandomFloat(-8, 30),
            trades_change: this.getRandomFloat(-12, 18),
            ratio_change: this.getRandomFloat(-20, 15)
        };
    }

    generateChartData(chartType, data) {
        switch (chartType) {
            case 'slippage_trend':
                return this.generateSlippageTrendChart(data);
            case 'strategy_comparison':
                return this.generateStrategyComparisonChart(data);
            case 'timing_histogram':
                return this.generateTimingHistogramChart(data);
            case 'exit_count_comparison':
                return this.generateExitCountComparisonChart();
            case 'pnl_comparison':
                return this.generatePnLComparisonChart();
            case 'holding_time_distribution':
                return this.generateHoldingTimeChart();
            default:
                return null;
        }
    }

    generateSlippageTrendChart(data) {
        // Group data by date
        const dailyData = {};
        data.forEach(row => {
            if (!dailyData[row.date]) {
                dailyData[row.date] = {
                    total_slippage: 0,
                    execution_slippage: 0,
                    turnover: 0,
                    count: 0,
                    trades: 0
                };
            }
            dailyData[row.date].total_slippage += row.total_slippage;
            dailyData[row.date].execution_slippage += row.execution_slippage;
            dailyData[row.date].turnover += row.turnover;
            dailyData[row.date].trades += row.trade_count;
            dailyData[row.date].count += 1;
        });

        const dates = Object.keys(dailyData).sort();
        const slippageValues = dates.map(date => dailyData[date].total_slippage);
        const executionSlippageValues = dates.map(date => dailyData[date].execution_slippage);
        const turnoverValues = dates.map(date => dailyData[date].turnover);

        return {
            data: [
                {
                    x: dates,
                    y: slippageValues,
                    type: 'scatter',
                    mode: 'lines+markers',
                    name: 'Total Slippage',
                    line: { color: '#c50000', width: 3, shape: 'spline' },
                    marker: {
                        size: 8,
                        color: '#c50000',
                        line: { color: '#4d0000', width: 2 }
                    },
                    hovertemplate: '<b>%{fullData.name}</b><br>' +
                                   'Date: %{x}<br>' +
                                   'Slippage: ₹%{y:,.0f}<br>' +
                                   '<extra></extra>'
                },
                {
                    x: dates,
                    y: executionSlippageValues,
                    type: 'scatter',
                    mode: 'lines+markers',
                    name: 'Execution Slippage',
                    line: { color: '#D52027', width: 2, shape: 'spline' },
                    marker: { size: 6, color: '#D52027' },
                    hovertemplate: '<b>%{fullData.name}</b><br>' +
                                   'Date: %{x}<br>' +
                                   'Execution Slippage: ₹%{y:,.0f}<br>' +
                                   '<extra></extra>'
                },
                {
                    x: dates,
                    y: turnoverValues,
                    type: 'scatter',
                    mode: 'lines',
                    name: 'Turnover',
                    yaxis: 'y2',
                    line: { color: '#28a745', width: 2, dash: 'dot' },
                    hovertemplate: '<b>%{fullData.name}</b><br>' +
                                   'Date: %{x}<br>' +
                                   'Turnover: ₹%{y:,.0f}<br>' +
                                   '<extra></extra>'
                }
            ],
            layout: {
                title: {
                    text: 'Daily Slippage & Turnover Trend',
                    font: { size: 18, color: '#2f4251' }
                },
                xaxis: {
                    title: 'Date',
                    gridcolor: '#e9ecef',
                    showgrid: true
                },
                yaxis: {
                    title: 'Slippage (₹)',
                    side: 'left',
                    gridcolor: '#e9ecef',
                    showgrid: true,
                    zeroline: true,
                    zerolinecolor: '#c50000',
                    zerolinewidth: 1
                },
                yaxis2: {
                    title: 'Turnover (₹)',
                    side: 'right',
                    overlaying: 'y',
                    showgrid: false
                },
                hovermode: 'x unified',
                showlegend: true
            }
        };
    }

    generateStrategyComparisonChart(data) {
        // Group by strategy
        const strategyData = {};
        data.forEach(row => {
            if (!strategyData[row.strategy_name]) {
                strategyData[row.strategy_name] = {
                    total_slippage: 0,
                    execution_slippage: 0,
                    turnover: 0,
                    trades: 0,
                    count: 0
                };
            }
            strategyData[row.strategy_name].total_slippage += row.total_slippage;
            strategyData[row.strategy_name].execution_slippage += row.execution_slippage;
            strategyData[row.strategy_name].turnover += row.turnover;
            strategyData[row.strategy_name].trades += row.trade_count;
            strategyData[row.strategy_name].count += 1;
        });

        const strategies = Object.keys(strategyData);
        const avgSlippage = strategies.map(strategy =>
            strategyData[strategy].total_slippage / strategyData[strategy].count
        );
        const avgExecutionSlippage = strategies.map(strategy =>
            strategyData[strategy].execution_slippage / strategyData[strategy].count
        );
        const totalTurnover = strategies.map(strategy => strategyData[strategy].turnover);

        return {
            data: [
                {
                    x: strategies,
                    y: avgSlippage,
                    type: 'bar',
                    name: 'Total Slippage',
                    marker: {
                        color: avgSlippage.map(val => val > 0 ? '#c50000' : '#28a745'),
                        line: { color: '#4d0000', width: 1 },
                        opacity: 0.8
                    },
                    text: avgSlippage.map(val => this.formatNumber(val)),
                    textposition: 'outside',
                    hovertemplate: '<b>%{x}</b><br>' +
                                   'Avg Total Slippage: ₹%{y:,.0f}<br>' +
                                   'Turnover: ₹%{customdata:,.0f}<br>' +
                                   '<extra></extra>',
                    customdata: totalTurnover
                },
                {
                    x: strategies,
                    y: avgExecutionSlippage,
                    type: 'bar',
                    name: 'Execution Slippage',
                    marker: {
                        color: avgExecutionSlippage.map(val => val > 0 ? '#D52027' : '#17a2b8'),
                        line: { color: '#4d0000', width: 1 },
                        opacity: 0.6
                    },
                    text: avgExecutionSlippage.map(val => this.formatNumber(val)),
                    textposition: 'outside',
                    hovertemplate: '<b>%{x}</b><br>' +
                                   'Avg Execution Slippage: ₹%{y:,.0f}<br>' +
                                   '<extra></extra>'
                }
            ],
            layout: {
                title: {
                    text: 'Strategy Performance Comparison',
                    font: { size: 18, color: '#2f4251' }
                },
                xaxis: {
                    title: 'Strategy',
                    tickangle: -45,
                    gridcolor: '#e9ecef',
                    showgrid: true
                },
                yaxis: {
                    title: 'Average Slippage (₹)',
                    gridcolor: '#e9ecef',
                    showgrid: true,
                    zeroline: true,
                    zerolinecolor: '#c50000',
                    zerolinewidth: 2
                },
                showlegend: true,
                barmode: 'group',
                bargap: 0.15,
                bargroupgap: 0.1
            }
        };
    }

    generateTimingHistogramChart(data) {
        const executionTimes = data.map(row => row.avg_execution_time);

        // Calculate statistics
        const mean = executionTimes.reduce((sum, val) => sum + val, 0) / executionTimes.length;
        const sortedTimes = [...executionTimes].sort((a, b) => a - b);
        const median = sortedTimes[Math.floor(sortedTimes.length / 2)];
        const p95 = sortedTimes[Math.floor(sortedTimes.length * 0.95)];

        return {
            data: [
                {
                    x: executionTimes,
                    type: 'histogram',
                    nbinsx: 25,
                    name: 'Execution Times',
                    marker: {
                        color: '#4d0000',
                        opacity: 0.7,
                        line: { color: '#c50000', width: 1 }
                    },
                    hovertemplate: 'Execution Time: %{x:.2f}s<br>' +
                                   'Count: %{y}<br>' +
                                   '<extra></extra>'
                },
                {
                    x: [mean, mean],
                    y: [0, Math.max(...executionTimes) * 0.8],
                    type: 'scatter',
                    mode: 'lines',
                    name: `Mean (${mean.toFixed(2)}s)`,
                    line: { color: '#c50000', width: 3, dash: 'dash' },
                    hovertemplate: 'Mean: %{x:.2f}s<extra></extra>'
                },
                {
                    x: [median, median],
                    y: [0, Math.max(...executionTimes) * 0.6],
                    type: 'scatter',
                    mode: 'lines',
                    name: `Median (${median.toFixed(2)}s)`,
                    line: { color: '#28a745', width: 2, dash: 'dot' },
                    hovertemplate: 'Median: %{x:.2f}s<extra></extra>'
                },
                {
                    x: [p95, p95],
                    y: [0, Math.max(...executionTimes) * 0.4],
                    type: 'scatter',
                    mode: 'lines',
                    name: `95th Percentile (${p95.toFixed(2)}s)`,
                    line: { color: '#ffc107', width: 2, dash: 'dashdot' },
                    hovertemplate: '95th Percentile: %{x:.2f}s<extra></extra>'
                }
            ],
            layout: {
                title: {
                    text: 'Execution Time Distribution & Statistics',
                    font: { size: 18, color: '#2f4251' }
                },
                xaxis: {
                    title: 'Execution Time (seconds)',
                    gridcolor: '#e9ecef',
                    showgrid: true
                },
                yaxis: {
                    title: 'Frequency',
                    gridcolor: '#e9ecef',
                    showgrid: true
                },
                showlegend: true,
                annotations: [
                    {
                        x: mean,
                        y: Math.max(...executionTimes) * 0.9,
                        text: `μ = ${mean.toFixed(2)}s`,
                        showarrow: true,
                        arrowhead: 2,
                        arrowcolor: '#c50000',
                        font: { color: '#c50000', size: 12 }
                    }
                ]
            }
        };
    }

    generateExitCountComparisonChart() {
        const dates = this.generateDateRange(30);
        const strategyExits = dates.map(() => this.getRandomInt(50, 200));
        const clusterExits = dates.map(() => this.getRandomInt(40, 180));
        const backtestExits = dates.map(() => this.getRandomInt(45, 190));

        return {
            data: [
                {
                    x: dates,
                    y: strategyExits,
                    type: 'scatter',
                    mode: 'lines+markers',
                    name: 'Strategy',
                    line: { color: '#c50000', width: 3 }
                },
                {
                    x: dates,
                    y: clusterExits,
                    type: 'scatter',
                    mode: 'lines+markers',
                    name: 'Cluster',
                    line: { color: '#4d0000', width: 2 }
                },
                {
                    x: dates,
                    y: backtestExits,
                    type: 'scatter',
                    mode: 'lines+markers',
                    name: 'Backtest',
                    line: { color: '#28a745', width: 2, dash: 'dash' }
                }
            ],
            layout: {
                title: 'Daily Exit Count Comparison',
                xaxis: { title: 'Date' },
                yaxis: { title: 'Exit Count' },
                hovermode: 'x unified'
            }
        };
    }

    generatePnLComparisonChart() {
        const dates = this.generateDateRange(30);
        let strategyCumPnL = 0;
        let clusterCumPnL = 0;
        let backtestCumPnL = 0;

        const strategyPnL = [];
        const clusterPnL = [];
        const backtestPnL = [];

        dates.forEach(() => {
            strategyCumPnL += this.getRandomFloat(-50000, 100000);
            clusterCumPnL += this.getRandomFloat(-40000, 80000);
            backtestCumPnL += this.getRandomFloat(-30000, 90000);

            strategyPnL.push(strategyCumPnL);
            clusterPnL.push(clusterCumPnL);
            backtestPnL.push(backtestCumPnL);
        });

        return {
            data: [
                {
                    x: dates,
                    y: strategyPnL,
                    type: 'scatter',
                    mode: 'lines',
                    name: 'Strategy',
                    line: { color: '#c50000', width: 3 },
                    fill: 'tonexty'
                },
                {
                    x: dates,
                    y: clusterPnL,
                    type: 'scatter',
                    mode: 'lines',
                    name: 'Cluster',
                    line: { color: '#4d0000', width: 2 }
                },
                {
                    x: dates,
                    y: backtestPnL,
                    type: 'scatter',
                    mode: 'lines',
                    name: 'Backtest',
                    line: { color: '#28a745', width: 2, dash: 'dash' }
                }
            ],
            layout: {
                title: 'Cumulative PnL Comparison',
                xaxis: { title: 'Date' },
                yaxis: { title: 'Cumulative PnL (₹)' },
                hovermode: 'x unified'
            }
        };
    }

    generateHoldingTimeChart() {
        const holdingTimes = [];
        for (let i = 0; i < 1000; i++) {
            holdingTimes.push(this.getRandomFloat(0.5, 120)); // 0.5 to 120 minutes
        }

        return {
            data: [{
                x: holdingTimes,
                type: 'histogram',
                nbinsx: 30,
                marker: {
                    color: '#4d0000',
                    opacity: 0.7,
                    line: { color: '#c50000', width: 1 }
                }
            }],
            layout: {
                title: 'Holding Time Distribution',
                xaxis: { title: 'Holding Time (minutes)' },
                yaxis: { title: 'Frequency' },
                showlegend: false
            }
        };
    }

    generateComparisonData() {
        return {
            strategy_vs_cluster: {
                exit_count_diff: this.getRandomInt(-50, 100),
                pnl_diff: this.getRandomFloat(-100000, 200000),
                avg_holding_time_diff: this.getRandomFloat(-15, 30),
                short_duration_strategy: this.getRandomInt(20, 80)
            },
            strategy_vs_backtest: {
                exit_count_diff: this.getRandomInt(-30, 80),
                pnl_diff: this.getRandomFloat(-80000, 150000),
                avg_holding_time_diff: this.getRandomFloat(-10, 25),
                short_duration_strategy: this.getRandomInt(15, 70)
            },
            performance_summary: [
                {
                    type: 'Strategy',
                    total_exits: this.getRandomInt(800, 1200),
                    total_pnl: this.getRandomFloat(50000, 500000),
                    avg_pnl: this.getRandomFloat(100, 800),
                    avg_holding_time: this.getRandomFloat(5, 45),
                    max_daily_exits: this.getRandomInt(50, 150),
                    pnl_volatility: this.getRandomFloat(10000, 50000)
                },
                {
                    type: 'Cluster',
                    total_exits: this.getRandomInt(700, 1100),
                    total_pnl: this.getRandomFloat(40000, 450000),
                    avg_pnl: this.getRandomFloat(80, 750),
                    avg_holding_time: this.getRandomFloat(4, 40),
                    max_daily_exits: this.getRandomInt(45, 140),
                    pnl_volatility: this.getRandomFloat(8000, 45000)
                },
                {
                    type: 'Backtest',
                    total_exits: this.getRandomInt(750, 1150),
                    total_pnl: this.getRandomFloat(60000, 480000),
                    avg_pnl: this.getRandomFloat(120, 780),
                    avg_holding_time: this.getRandomFloat(6, 42),
                    max_daily_exits: this.getRandomInt(48, 145),
                    pnl_volatility: this.getRandomFloat(9000, 47000)
                }
            ],
            data_availability: {
                strategy_range: '30 days',
                cluster_range: '30 days',
                backtest_range: '30 days'
            }
        };
    }

    // Utility methods
    getRandomElement(array) {
        return array[Math.floor(Math.random() * array.length)];
    }

    getRandomInt(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    getRandomFloat(min, max) {
        return Math.random() * (max - min) + min;
    }

    generateDateRange(days) {
        const dates = [];
        const endDate = new Date();

        for (let i = days - 1; i >= 0; i--) {
            const date = new Date(endDate);
            date.setDate(date.getDate() - i);
            dates.push(date.toISOString().split('T')[0]);
        }

        return dates;
    }

    formatNumber(num) {
        const n = parseFloat(num);
        if (isNaN(n)) return '0';
        if (Math.abs(n) >= 1e6) return (n / 1e6).toFixed(1) + 'M';
        if (Math.abs(n) >= 1e3) return (n / 1e3).toFixed(1) + 'K';
        return n.toFixed(2);
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new AdvancedSlippageDashboard();
});
