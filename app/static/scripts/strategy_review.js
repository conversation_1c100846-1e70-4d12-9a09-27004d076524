const textarea = document.getElementsByTagName("textarea");
accepted_popup = document.getElementById("accepted_popup");
accept_close = document.getElementById("accept_close");
rejected_popup = document.getElementById("rejected_popup");
reject_close = document.getElementById("reject_close");
accept_strat = document.getElementById("accept_strat");
reworked_strat = document.getElementById("reworked_strat");
cluster_list_reworked = document.getElementById("cluster_list_reworked");
reject_strat = document.getElementById("reject_strat");
strat_review_form = document.getElementById("strat_review_form");
accept_btn = document.getElementById("accept_btn");
accept_test_btn = document.getElementById("accept_test_btn");
save_btn = document.getElementById("save_btn");
reject_btn = document.getElementById("reject_btn");
strat_date_btn = document.getElementById("strat_date_btn")
test_confirmation_btn = document.getElementById("test_confirmation_btn")
strat_date_popup = document.getElementById("strat_date_popup")
test_confirmation_popup = document.getElementById("test_confirmation_popup")
cancel_popup_btn = document.getElementById("cancel_popup_btn")
cancel_test_popup_btn = document.getElementById("cancel_test_popup_btn")
rework_start_div = document.getElementById("rework_start")
strat_date = document.getElementById('strat_date')
rework_start_check = document.getElementById('is_reworked_start')

let action_taken = "";
let rework_strat_display = rework_start_div.style.display;
let rework_start_date = strat_date.value

function validateDate(event) {
    const selectedDate = new Date(event.target.value);
    const today = new Date();
    
    if (selectedDate > today) {
      alert('Please select a date that is not greater than today.');
      strat_date.valueAsDate=null;
    } 
  }

strat_date.addEventListener('change', validateDate);

rework_start_check.addEventListener("change", function(){
    if (this.checked){
        strat_date.value=rework_start_date;
    }else{
        strat_date.valueAsDate=null;
    }
});

accept_btn.onclick = function () {
    action_taken = "accept the strategy to live?";
}
accept_test_btn.onclick = function () {
    action_taken = "accept the strategy to test?";
}
save_btn.onclick = function () {
    action_taken = "save";
}
reject_btn.onclick = function () {
    action_taken = "reject the strategy?";
}
strat_date_btn.onclick = function() {
    strat_date_popup.style.visibility = 'visible';
    rework_start_div.style.display = rework_strat_display;
}
test_confirmation_btn.onclick = function() {
    test_confirmation_popup.style.visibility = 'visible';
}
cancel_popup_btn.onclick = function() {
    strat_date_popup.style.visibility = 'hidden';
    rework_start_div.style.display = 'none';
}
cancel_test_popup_btn.onclick = function() {
    test_confirmation_popup.style.visibility = 'hidden';
}

window.onload = function(){
    rework_start_div.style.visibility = 'none';
    strat_date.valueAsDate=null;
}

strat_review_form.onsubmit = function () {
    if (action_taken != "reject the strategy?") {
        return true;
    }
    return confirm("Are you sure you want to " + action_taken);
}
for (let i = 0; i < textarea.length; i++) {
    textarea[i].setAttribute("style", "height:" + (textarea[i].scrollHeight) + "px;overflow-y:hidden;");
    textarea[i].addEventListener("input", OnInput, false);
    textarea[i].addEventListener("keydown", function(e) {
        if (e.key == 'Tab') {
          e.preventDefault();
          var start = this.selectionStart;
          var end = this.selectionEnd;
      
          // set textarea value to: text before caret + tab + text after caret
          this.value = this.value.substring(0, start) +
            "\t" + this.value.substring(end);
      
          // put caret at right position again
          this.selectionStart =
            this.selectionEnd = start + 1;
        }
      });
}

function OnInput() {
    this.style.height = 0;
    this.style.height = (this.scrollHeight) + "px";
}

accept_close.onclick = function () {
    let a = document.createElement('a');
    document.body.appendChild(a);
    a.setAttribute('style', 'display: none');
    a.href = "/strat_review";
    a.click();

}

reject_close.onclick = function () {
    let a = document.createElement('a');
    document.body.appendChild(a);
    a.setAttribute('style', 'display: none');
    a.href = "/strat_review";
    a.click();
}