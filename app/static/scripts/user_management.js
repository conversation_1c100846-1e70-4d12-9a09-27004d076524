edit_btns = document.getElementsByName("edit_buttons");
delete_btns = document.getElementsByName("delete_buttons");
edit_user_popup = document.getElementById("edit_user_popup");
edit_close = document.getElementById("edit_close");
edit_btn_username = document.getElementById("edit_btn_username");
pass_change_username = document.getElementById("pass_change_username");
role = document.getElementById("role");
edit_confirm = document.getElementById("edit_confirm");
sucess_popup = document.getElementById("sucess_popup");
reason_success = document.getElementById("reason_success");
success_popup_close = document.getElementById("success_popup_close");
failure_popup = document.getElementById("failure_popup");
reason_failure = document.getElementById("reason_failure");
failure_popup_close = document.getElementById("failure_popup_close");
edit_toggler_popup = document.getElementById("edit_toggler_popup");
role_changer_btn = document.getElementById("role_changer_btn");
pass_changer_btn = document.getElementById("pass_changer_btn");
password_change_popup = document.getElementById("password_change_popup");
username_edit = document.getElementById("username_edit");
pass_change_close = document.getElementById("pass_change_close");
pass_change_confirm = document.getElementById("pass_change_confirm");
password = document.getElementById("password");
manager = document.getElementById("manager");
manager_div = document.getElementById("manager_div");
confirm_password = document.getElementById("confirm_password");
edit_user_close = document.getElementById("edit_user_close");
let global_username;

edit_user_close.onclick = function () {
    edit_toggler_popup.style.visibility = "hidden";
};
pass_change_confirm.onclick = function () {
    password_change_popup.style.visibility = "hidden";
    $.ajax({
        url: "/user_management/edit_user_password",
        type: "POST",
        data: {
            username: global_username,
            password: password.value,
            confirm_password: confirm_password.value,
        },
        success: function (response) {
            if (response == "success") {
                reason_success.innerHTML = "Password changed successfully";
                sucess_popup.style.visibility = "visible";
            } else {
                reason_failure.innerHTML = "Password could not be changed";
                failure_popup.style.visibility = "visible";
            }
        },
        error: function (response) {
            reason_failure.innerHTML = "Password could not be changed";
            failure_popup.style.visibility = "visible";
        },
    });
};
pass_change_close.onclick = function () {
    password_change_popup.style.visibility = "hidden";
};
role_changer_btn.onclick = function () {
    edit_toggler_popup.style.visibility = "hidden";
    edit_user_popup.style.visibility = "visible";
};
pass_changer_btn.onclick = function () {
    edit_toggler_popup.style.visibility = "hidden";
    password_change_popup.style.visibility = "visible";
};

success_popup_close.onclick = function () {
    window.location.reload();
};

failure_popup_close.onclick = function () {
    failure_popup.style.visibility = "hidden";
};

role.onclick = function () {
    if (role.value == "DEVELOPER") {
        manager_div.style.display = "flex";
    } else {
        manager_div.style.display = "none";
    }
};

edit_confirm.onclick = function () {
    edit_user_popup.style.visibility = "hidden";
    $.ajax({
        url: "/user_management/edit_user",
        type: "POST",
        data: {
            username: global_username,
            role: role.value,
            manager: manager.value,
        },
        success: function (response) {
            if (response == "success") {
                reason_success.innerHTML = "Profile editted successfully";
                sucess_popup.style.visibility = "visible";
            } else {
                reason_failure.innerHTML = "Profile could not be editted";
                failure_popup.style.visibility = "visible";
            }
        },
        error: function (response) {
            reason_failure.innerHTML = "Profile could not be editted";
            failure_popup.style.visibility = "visible";
        },
    });
};

function edit_user() {
    username_edit.innerHTML = "Username: " + this.value;
    edit_btn_username.innerHTML = "Username: " + this.value;
    pass_change_username.innerHTML = "Username: " + this.value;
    global_username = this.value;
    edit_toggler_popup.style.visibility = "visible";
}

edit_close.onclick = function () {
    edit_user_popup.style.visibility = "hidden";
};

for (let index = 0; index < edit_btns.length; index++) {
    edit_btns[index].addEventListener("click", edit_user);
}

function delete_user() {
    let confirmation = confirm("Are you sure you want to delete this user?");
    if (confirmation == true) {
        $.ajax({
            url: "/user_management/delete_user",
            type: "POST",
            data: {
                username: this.value,
            },
            success: function (response) {
                if (response == "success") {
                    reason_success.innerHTML = "User deleted successfully";
                    sucess_popup.style.visibility = "visible";
                } else {
                    reason_failure.innerHTML = "User could not be deleted";
                    failure_popup.style.visibility = "visible";
                }
            },
            error: function (response) {
                reason_failure.innerHTML = "User could not be deleted";
                failure_popup.style.visibility = "visible";
            },
        });
    }
}

for (let index = 0; index < delete_btns.length; index++) {
    delete_btns[index].addEventListener("click", delete_user);
}
