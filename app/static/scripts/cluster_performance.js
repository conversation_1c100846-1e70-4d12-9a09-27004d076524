pre_performance = document.getElementById("pre_performance");
post_performance = document.getElementById("post_performance");
full = document.getElementById("full");
wait_popup = document.getElementById("wait_popup");
document.onreadystatechange = function () {
    var state = document.readyState;
    if (state == "complete") {
        wait_popup.style.visibility = "hidden";
        full.style.visibility = "visible";
    }
};

function htmlDecode(input) {
    var doc = new DOMParser().parseFromString(input, "text/html");
    return doc.documentElement.textContent;
}
$("#tag1").append(htmlDecode(pre_performance.innerHTML));
$("#tag2").append(htmlDecode(post_performance.innerHTML));
