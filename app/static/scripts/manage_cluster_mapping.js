$('.select2-strategies').select2();
$('.select2-cluster-mapping').select2();

form = document.getElementById("manage_cluster_mapping");

function selectMappingType(evt, mappingType, strategiesList) {
    var i, tablinks;
    tablinks = document.getElementsByClassName("tablinks");

    for (i = 0; i < tablinks.length; i++) {
        tablinks[i].className = tablinks[i].className.replace(" active", "");
    }
    evt.currentTarget.className += " active";

    // Reset strategies select input
    form.strategies.selectedIndex = 0;
    form.strategies.dispatchEvent(new Event('change'));

    // Reset cluster_mapping select input
    const options = form.cluster_mapping.options;
    for (var i = 0; i < options.length; i++) {
        options[i].selected = false;
    }
    form.cluster_mapping.dispatchEvent(new Event('change'));

    var mappingENV = document.getElementById("ENV");
    var infoString = document.getElementById("mappingtype");

    // Update the mapping value based on passed mappingType value
    if (mappingType === 'test') {
        mappingENV.value = "TEST_ENV";  // Mark as test environment
        infoString.textContent = "TEST";
    } else {
        mappingENV.value = "LIVE_ENV";  // Mark as live environment
        infoString.textContent = "LIVE";
    }

    updateStrategyChoice(strategiesList);
}

function updateStrategyChoice(strategiesList) {
    let optionHTML = '<option value="">-- Select a strategy --</option>';
    for (let strat of strategiesList) {
        optionHTML += '<option value="' + strat + '">' + strat + '</option>';
    }
    strategy_select = document.getElementById('strategies');
    strategy_select.innerHTML = optionHTML;
}

form.strategies.onchange = function get_current_clusters() {
    if (form.strategies.selectedIndex === 0) {
        return; // Prevent AJAX call if no strategy is selected 
    }
    form.strategies.data=$(this).val();
    var mappingENV = document.getElementById("ENV").value;
    $.ajax({
        url: "/get_current_clusters",
        type: "POST",
        data: {
          selected_strategy: form.strategies.data,
          mapping_state: mappingENV
        },
        success: function (response) {
            const options = form.cluster_mapping.options;
            for (let i = 0; i < options.length; i++) {
                options[i].selected = false;
            }
            response.forEach(function (cluster) {
                for (let i = 0; i < options.length; i++) {
                    if (options[i].value === cluster) {
                        options[i].selected = true;

                    }
                }
            });
            form.cluster_mapping.dispatchEvent(new Event('change'));

        },
        error: function () {
            console.log("Error fetching clusters");
        }
    });
   
};  

form.cluster_mapping.onchange = function update_cluster_mapping() {
    form.cluster_mapping.data = $(this).val();
};