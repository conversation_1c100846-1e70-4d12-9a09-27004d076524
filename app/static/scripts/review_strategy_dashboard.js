multiple_download_btn = document.getElementById("btn-download");
select_all_button = document.getElementById("checkbox_all");
fail_close = document.getElementById("fail_close");
wait_popup = document.getElementById("wait_popup");
fail_popup = document.getElementById("fail_popup");
encryption_popup = document.getElementById("encryption_popup");
encryption_close = document.getElementById("encryption_close");
encryption_submit = document.getElementById("encryption_submit");
file = document.getElementById("file");
fail_reason = document.getElementById("fail_reason");
backtest_result_btn = document.getElementsByName("backtest_result_btn");
backtest_result_popup = document.getElementsByName("backtest_result_popup");
backtest_popup_close = document.getElementsByName("backtest_popup_close");
backtest_added_popup = document.getElementById("backtest_added_popup");
backtest_added_popup_close = document.getElementById(
    "backtest_added_popup_close"
);
backtest_failed_popup = document.getElementById("backtest_failed_popup");
backtest_failed_popup_close = document.getElementById(
    "backtest_failed_popup_close"
);
reason_backtest_fail = document.getElementById("reason_backtest_fail");
svg_fig = document.getElementsByTagName("svg");
strategy_list = document.getElementsByName("strat_name");
let checkBoxes = document.getElementsByName("checkbox_input");

sortable_headers_review = document.getElementsByName("sortable_headers_review");
table_live = document.getElementById("strats_body");
table_live_header = document.getElementById("strats");
rows_live = table_live.querySelectorAll("tr");
cols_live = table_live_header.getElementsByTagName("th");
table_body_live = table_live.querySelector("tbody");
allow_modification_button = document.getElementsByName("modify_button");

for (let index = 0; index < allow_modification_button.length; index++) {
    allow_modification_button[index].addEventListener('click', toggleModificationState);
}
function toggleModificationState() {
    btn_clicked = this;
    $.ajax({
        url: "/control_modification",
        type: "POST",
        data: {
          strategy:btn_clicked.value
        },
        success: function (response) {
            if (response != "failed") {
                if ( btn_clicked.innerText === "Disabled") {
                    btn_clicked.style.backgroundColor = "blue";
                    btn_clicked.innerText  = "Enabled";
                   
                } else {
                    btn_clicked.style.backgroundColor = "red";
                    btn_clicked.innerText  = "Disabled";
                }
            } 
        },
        error: function () {
            console.log("Error changing the state of allow modification");
        }
    });
  
    
}
let compare_func_map = {
    "date": compareDates,
    "strategy name": compareStrings,
    "developer name": compareStrings,
    "segment name": compareStrings,
    "cluster": compareStrings,
}
const directions_live = Array.from(sortable_headers_review).map(function (header) {
    return '';
});

for (let index = 0; index < sortable_headers_review.length; index++) {
    let header_value = sortable_headers_review[index].innerHTML.toLowerCase().trim();
    let compare_func  = header_value in compare_func_map ? compare_func_map[header_value] : compareFloats;
    sortable_headers_review[index].addEventListener('click', () => {
        sortTable(index+1, directions_live, rows_live, table_body_live, compare_func)
    });
}

backtest_failed_popup_close.onclick = function () {
    backtest_failed_popup.style.visibility = "hidden";
    window.location.reload();
};

backtest_added_popup_close.onclick = function () {
    backtest_added_popup.style.visibility = "hidden";
    window.location.reload();
};

function display_backtest_result(index) {
    for (let index = 0; index < backtest_popup_close.length; index++) {
        backtest_result_popup[index].style.visibility = "hidden";
    }
    backtest_result_popup[index].style.visibility = "visible";
}

function close_backtest_result(index) {
    backtest_result_popup[index].style.visibility = "hidden";
}

for (let index = 0; index < backtest_result_btn.length; index++) {
    backtest_result_btn[index].addEventListener("click", () => {
        display_backtest_result(index);
    });
}

for (let index = 0; index < backtest_popup_close.length; index++) {
    backtest_popup_close[index].addEventListener("click", () => {
        close_backtest_result(index);
    });
}

function call_backend_service(strategy_name, service_index, strat_index) {
    let confirmation = confirm("Are you sure you want to trigger process?");
    if (confirmation === true) {
        close_backtest_result(strat_index);
        $.ajax({
            url: "/run_backtest",
            type: "POST",
            data: {
                strategy: strategy_name,
                service_index: service_index,
            },
            success: function (response) {
                if (response === "added") {
                    backtest_added_popup.style.visibility = "visible";
                } else {
                    if (response === "already_submitted") {
                        reason_backtest_fail.innerHTML =
                            "Strategy is already pending";
                    } else if (response === "unavailable") {
                        reason_backtest_fail.innerHTML =
                            "Requested service does not exist";
                    } else if (response === "upstream_error") {
                        reason_backtest_fail.innerHTML =
                            "Upstream service is not completed";
                    } else {
                        reason_backtest_fail.innerHTML =
                            "Please try again later!";
                    }
                    backtest_failed_popup.style.visibility = "visible";
                }
            },
            error: function (response) {
                backtest_failed_popup.style.visibility = "visible";
            },
        });
    }
}

for (let index = 0; index < svg_fig.length; index++) {
    let rect_nodes = svg_fig[index].getElementsByTagName("rect");
    for (let index2 = 0; index2 < rect_nodes.length; index2++) {
        rect_nodes[index2].addEventListener("click", () => {
            call_backend_service(
                svg_fig[index].getAttribute("value"),
                index2,
                index
            );
        });
    }
}

encryption_submit.onclick = function () {
    if (file.files.length === 0) {
        alert("Please select a file!");
    } else {
        let selected_file = file.files[0];
        let formData = new FormData();
        formData.append("file", selected_file);
        formData.append(
            "strategy_list",
            JSON.stringify(encryption_submit.strategy_list)
        );
        formData.append(
            "status",
            "PENDING"
        )
        fetch("/strat_download", {
            method: "POST",
            body: formData,
        })
            .then((response) => {
                if (!response.ok) {
                    throw new Error("Validation request failed");
                }
                return response.blob();
            })
            .then((blob) => {
                const a = document.createElement("a");
                document.body.appendChild(a);
                a.setAttribute("style", "display: none");
                const url = window.URL.createObjectURL(blob);
                a.href = url;
                a.download = "samba.zip";
                a.click();
                window.URL.revokeObjectURL(url);
            })
            .catch((error) => {
                fail_popup.style.visibility = "visible";
            });
        encryption_popup.style.visibility = "hidden";
    }
};

encryption_close.onclick = function () {
    encryption_popup.style.visibility = "hidden";
};

fail_close.onclick = function () {
    fail_popup.style.visibility = "hidden";
};

let listener = function () {
    if (!this.checked) {
        select_all_button.checked = false;
    }
};

for (let i = 0; i < checkBoxes.length; i++) {
    checkBoxes[i].addEventListener("click", listener, false);
}

// submit_button.onclick = function () {
//     wait_popup.style.visibility = "visible";
//     let strats_for_review = [];
//     for (let i = 0; i < checkBoxes.length; i++) {
//         if (checkBoxes[i].checked && checkBoxes[i] !== select_all_button) {
//             strats_for_review.push(strategy_list[i].getAttribute("value"));
//         }
//     }
//     if (strats_for_review.length !== 0) {
//         strats_for_review = JSON.stringify(strats_for_review);
//         $.ajax({
//             url: "/strat_review",
//             type: "POST",
//             data: {
//                 strategies: strats_for_review,
//             },
//             success: function (response) {
//                 if (response === "results_not_available") {
//                     wait_popup.style.visibility = "hidden";
//                     fail_reason.innerHTML = "Backtest results not available";
//                     fail_popup.style.visibility = "visible";
//                 } else {
//                     const a = document.createElement("a");
//                     document.body.appendChild(a);
//                     a.setAttribute("style", "display: none");
//                     a.href = "/strat_review/result/" + strats_for_review;
//                     a.click();
//                 }
//             },
//             error: function (response) {
//                 wait_popup.style.visibility = "hidden";
//                 fail_popup.style.visibility = "visible";
//             },
//         });
//     } else {
//         wait_popup.style.visibility = "hidden";
//         alert("Please select at least 1 strategy");
//     }
// };

multiple_download_btn.onclick = function () {
    encryption_popup.style.visibility = "visible";
    let strats_for_review = [];
    for (let i = 0; i < checkBoxes.length; i++) {
        if (checkBoxes[i].checked && checkBoxes[i] !== select_all_button) {
            strats_for_review.push(strategy_list[i].getAttribute("value"));
        }
    }
    encryption_submit.strategy_list = strats_for_review;
};

$(window).bind("pageshow", function (event) {
    if (event.originalEvent.persisted) {
        wait_popup.style.visibility = "hidden";
    }
});

select_all_button.onclick = function () {
    if (select_all_button.checked) {
        for (let i = 0; i < checkBoxes.length; i++) {
            checkBoxes[i].checked = true;
        }
    } else {
        for (let i = 0; i < checkBoxes.length; i++) {
            checkBoxes[i].checked = false;
        }
    }
};
strategy_list.forEach(item => {
    item.addEventListener('click', event => {
        const a = document.createElement('a');
        document.body.appendChild(a);
        a.setAttribute('style', 'display: none');
        console.log(event.target.getAttribute("value"),"this")
        a.href = event.target.getAttribute("value") + "/expand";
        a.target = "_blank";
        a.click();
    })
});


//  Adding filter
no_filter_cols = ['', 'Backtest Result', 'Modification Allowed', 'Correlation']
cols_list = cols_live
cols_to_none = []
col_names = []
for(var index = 0; index < cols_list.length; index++){
        col_names.push(cols_list[index].textContent.trim());
    }
cols_list = col_names
for(var i in no_filter_cols){
    index = cols_list.indexOf(no_filter_cols[i])
    if(index != -1){
        cols_to_none.push("col_" + index)
    }
}
config = {
    base_path: "static/scripts/tablefilter/",
    auto_filter: {    
                  delay: 100 //milliseconds
                 },
    filters_row_index: 0,
    alternate_rows: true,
    sticky_headers: true,	
  
};
for(var col of cols_to_none){
    config[col] = 'none';
}
tf = new TableFilter("strats_body", 0, config);
tf.init()
