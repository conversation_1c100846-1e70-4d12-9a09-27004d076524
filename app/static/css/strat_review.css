.form-control {
    padding-top: 10px;
    border: 1px solid black;
    font-family: system-ui, sans-serif;
    font-size: 20px;
    font-weight: 600;
    line-height: 1.1;
    display: flex;
    justify-content: center;
    grid-template-columns: 1em auto;
    gap: 0.5em;
}

th input[type="checkbox"]:hover {
    cursor: pointer;
}
.styled-table th:hover{
    cursor: pointer;
}

input[type="checkbox"] {
    align-content: center;
    -webkit-appearance: none;
    appearance: none;
    background-color: #fff;
    margin: 0;
    font: inherit;
    color: currentColor;
    width: 1.15em;
    height: 1.15em;
    border: 0.15em solid currentColor;
    border-radius: 0.15em;
    transform: translateY(-0.075em);
    display: grid;
    place-content: center;
}

input[type="checkbox"]::before {
    content: "";
    width: 0.65em;
    height: 0.65em;
    transform: scale(0);
    transition: 120ms transform ease-in-out;
    box-shadow: inset 1em 1em var(--form-control-color);
    transform-origin: bottom left;
    clip-path: polygon(14% 44%, 0 65%, 50% 100%, 100% 16%, 80% 0%, 43% 62%);
    background-color: CanvasText;
}

input[type="checkbox"]:checked::before {
    transform: scale(1);
}

.form-control+.form-control {
    margin-top: 1em;
}
.styled-table1 {
    border-collapse: collapse;
    width: 100%;
    font-size: 1em;
    min-width: 400px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
    text-align: center;
}

.styled-table caption {
    caption-side: top;
    font-size: 14px;
    font-family: sans-serif;
}

.styled-table thead tr {
    background-color: #557A95;
    color: #ffffff;
}

.styled-table th,
.styled-table td {
    padding: 12px 12px;
}

.checkbox-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%; 
}

.styled-table tbody tr {
    border-bottom: 1px solid #dddddd;
}

.styled-table tbody tr:nth-of-type(even) {
    background-color: #f3f3f3;
}

.styled-table tbody tr:last-of-type {
    border-bottom: 2px solid #557A95;
}

.styled-table tbody tr.active-row {
    font-weight: bold;
    color: #0073e6;
}

.styled-table tbody tr a {
    font-weight: 600;
    color: #000;
    text-decoration: none;
}

button {
    border-radius: 5px;
    font-size: 16px;
    font-family: system-ui, sans-serif;
    font-weight: 600;
}

button:hover {
    cursor: pointer;
}

h3 {
    font-size: 20px;
    font-weight: 700;
}

h3 span {
    color: rgb(112, 94, 94);
    font-weight: 500;
}

.popup {
    background: #fff;
    border-radius: 6px;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    padding: 0 30px 30px;
    color: #333;
    visibility: hidden;
    border: 1px solid black;
}

.popup img {
    width: 100px;
    margin-top: -50px;
    border-radius: 50%;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.popup h2 {
    font-size: 38px;
    font-weight: 500;
    margin: 30px 0 10px;
}

.popup button {
    width: 100%;
    margin-top: 0px;
    padding: 10px 0;
    background: #d64949;
    color: #fff;
    border: 0;
    outline: none;
    font-size: 18px;
    border-radius: 4px;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.popup span {
    display: flex;
}

.popup input {
    margin-left: 5em;
}

label {
    font-weight: 600;
}

select {
    border-radius: 4px 4px;
    font-size: 15px;
    text-align: center;
    padding: 8px 4px;
    border: 2px solid black;
    font-weight: 600;
    margin-top: 1em;
}

.col_input {
    border-radius: 4px 4px;
    font-size: 14px;
    text-align: center;
    padding: 4px 4px 4px;
    border: 2px solid black;
    font-weight: 600;
    margin-top: 1em;
    margin-left: 1.5em;
    width: 260px;
    display: block;
    background: transparent url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' class='bi bi-search' viewBox='0 0 16 16'%3E%3Cpath d='M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z'%3E%3C/path%3E%3C/svg%3E") no-repeat 13px center;
}

.numberCircle {
    border-radius: 40%;
    width: 10px;
    height: 10px;
    padding: 4px;
    margin-left: 7px;
    background: #fff;
    border: 2px solid black;
    text-align: center;
    font: 11px Arial, sans-serif;
}
.clickable_cell {
    cursor: pointer;
  }
table {
    width: 100%;
    table-layout: fixed;
}
  
.styled-table {
    margin-top: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    font-size: 1em;
    font-family: sans-serif;
    font-weight: 500;
    text-align: center;
 }
.tbl-content tr:nth-child(even) {
    background-color: #E8E8E8;
}
td {
    padding: 10px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
    font-size: 15px;
    font-weight: 400;
    border-bottom: solid 1px rgba(255, 255, 255, 0.1);
 }

 /* Style the switch container */
.switch {
    display: flex;
    position: relative;
    width: 60px; 
    height: 30px; 
    align-items: center;
}
  
 
.switch .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc; 
    -webkit-transition: 0.4s;
    transition: 0.4s;
    border-radius: 30px; 
}
  
 
.switch .slider:before {
    position: absolute;
    content: "";
    height: 26px; 
    width: 26px; 
    left: 2px; 
    bottom: 2px; 
    background-color: white;
    -webkit-transition: 0.4s;
    transition: 0.4s;
    border-radius: 50%; 
 }
  
 
.switch input[type="checkbox"] {
    opacity: 0;
    width: 0;
    height: 0;
}
  
.switch input[type="checkbox"]:checked + .slider {
    background-color: #2196F3; 
}
  
.switch input[type="checkbox"]:checked + .slider:before {
    -webkit-transform: translateX(30px); 
    -ms-transform: translateX(30px);
    transform: translateX(30px);
}
  
.legend-container {
    float: right;
    margin-bottom: -40px;
}
  
.color-item {
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-right: 5px;
    border-radius: 50%;
}

.legend li {
    display: inline-block;
    margin-right: 10px;
    font-size: 14px;
}  