h1 {
    position: relative;
    padding: 0;
    margin: 0;
    font-family: "Raleway", sans-serif;
    font-weight: 300;
    font-size: 40px;
    color: #080808;
    -webkit-transition: all 0.4s ease 0s;
    -o-transition: all 0.4s ease 0s;
    transition: all 0.4s ease 0s;
}

.page_heading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 35px;
}

.page_heading h1 {
    font-size: 26px;
    font-weight: 700;
    letter-spacing: 1px;
    text-transform: uppercase;
    white-space: nowrap;
    padding-bottom: 13px;
    margin: 0;
}

.page_heading h1:before {
    background-color: #c50000;
    content: '';
    display: block;
    height: 3px;
    width: 110px;
    margin-bottom: 5px;
}

.page_heading h1:after {
    background-color: #c50000;
    content: '';
    display: block;
    position: relative;
    left: 200px;
    bottom: 0;
    height: 3px;
    width: 100px;
    margin-bottom: 0.25em;
}

.dropdown-div {
    display: flex;
    justify-content: center;
    max-width: 400px;
    margin: 0 auto;
    margin-top: 20px;
}
#developer_portfolio {
    background: #4d0000;
    color: white;
    margin-left: 100px;
}

h3 {
    font-size: 30px;
    font-weight: 350;
    margin-bottom: 15px;
}

.clickable_cell {
    cursor: pointer;
}

:root {
    --primary-color: #f7e9e9;
    --secondary-color: #555;
    --button-hover-color: #777;
}

.custom-card {
    width: 100%;
    /* Set a fixed width for the card */
    height: 350px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 20px;
    background-color: var(--primary-color);
    border-radius: 15px;
    transition: all 0.3s ease;
    overflow: hidden;
    border: 4px solid var(--secondary-color);
    margin: 0 auto;
    /* Center the card horizontally */
}

.custom-card:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.custom-title {
    color: rgb(4, 4, 4);
    margin-top: auto;
    margin-bottom: auto;
}

.point {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.area-buttons button {
    background: none;
    border: none;
    font-size: 14px;
    cursor: pointer;
    color: #777;
    margin-right: 10px;
    margin-top: 1vh;
}

.area-buttons button:hover {
    color: #333;
}

.attachment-dropdown {
    padding: 6px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: #f3f4f6;
}

#previewModal .modal-body {
    max-width: 90%;
    margin-right: auto;
    overflow-x: auto;
}

#previewModal table {
    width: 100%;
    white-space: nowrap;
}

#previewModal td,
#previewModal th {
    word-wrap: break-word;
}

#previewModal th,
#previewModal td {
    border: 1px solid #ddd;
    text-align: center;
}

#previewModal th {
    background-color: #f2f2f2;
}

.line {
    border-top: 5px solid #99334d;
    width: 40%;
    margin-top: 20px;
    margin-bottom: 20px;
}

.read-more-btn {
    background-color: #602131;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 14px;
    font-family: Arial, sans-serif;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.read-more-btn:hover {
    background-color: #b30000;
}

.heading-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.line {
    flex-grow: 1;
    height: 2px;
    background-color: black;
}

.area-heading {
    white-space: nowrap;
    margin: 5px 10px;
    font-size: 30px;
}

.popup {
    width: 400px;
    background: #fff;
    border-radius: 6px;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    padding: 0 30px 30px;
    color: #333;
    visibility: hidden;
    border: 1px solid black;
    z-index: 2000;
}

.popup img {
    width: 100px;
    margin-top: -50px;
    border-radius: 50%;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.popup h2 {
    font-size: 38px;
    font-weight: 500;
    margin: 30px 0 10px;
}

.popup button {
    width: 100%;
    margin-top: 0px;
    padding: 10px 0;
    background: #d64949;
    color: #fff;
    border: 0;
    outline: none;
    font-size: 18px;
    border-radius: 4px;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.popup a {
    width: 100%;
    margin-top: 0px;
    padding: 10px 0;
    background: #d64949;
    color: #fff;
    border: 0;
    outline: none;
    font-size: 18px;
    border-radius: 4px;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    text-decoration: none;
}

.popup span {
    display: flex;
}

.popup input {
    margin-left: 5em;
}