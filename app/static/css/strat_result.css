tr {
    display: block;
    float: left;
}

th,
td {
    display: block;
    border: 0.5px solid black;
}

.styled-table {
    margin: 25px 0;
    font-size: 1em;
    font-family: sans-serif;
    font-weight: 500;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
    text-align: center;
    margin-bottom: 2em;
}

.styled-table caption {
    caption-side: top;
    font-weight: 600;
    font-size: 16px;
    text-align: center;
}

.styled-table caption span {
    color: #000;
}

.styled-table tr th {
    text-align: center;
    background-color: #f3f3f3;
}

.styled-table th,
.styled-table td {
    padding: 12px 15px;
    font-weight: 600;
    font-size: 14px;
}

.styled-table tbody tr a {
    font-weight: bold;
    color: #000;
    text-decoration: none;
}

dl {
    margin-bottom: 1em;
}

dl dt {
    float: left;
    width: 20px;
    height: 20px;
    vertical-align: left;
    border: 1px solid black;
}

dl dd {
    float: left;
    margin: -2px 10px;
    padding-bottom: 0;
    vertical-align: left;
}

dl dt.pending {
    background: #b3e6ff;
}

dl dt.live {
    background: #DAF0CA;
}

dl dt.dead {
    background: #ffe680;
}

dl dt.rejected {
    background: #979797;
}

dl dt.flag {
    background: #AF5665;
}

.key {
    font-size: 15px;
    font-weight: 500;
}

.failed_strats {
    border: 2px solid #AF5665;
    padding: 3px;
    border-radius: 4px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
    display: grid;
    justify-content: center;
    align-items: center;
    text-align: center;
    max-width: 400px;
}

.failed_strats h2 {
    font-size: 25px;
    padding: 3px;
}

.popup {
    width: 300px;
    background: #fff;
    border-radius: 6px;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    padding: 0 15px 15px;
    color: #333;
    visibility: hidden;
    border: 1px solid black;
}

.popup img {
    width: 70px;
    margin-top: -50px;
    border-radius: 50%;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.popup h2 {
    font-size: 20px;
    font-weight: 500;
    margin: 30px 0 10px;
}

.popup button {
    width: 100%;
    margin-top: 0px;
    padding: 10px 0;
    background: #d64949;
    color: #fff;
    border: 0;
    outline: none;
    font-size: 18px;
    border-radius: 4px;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    margin-top: 1em;
}

.popup a {
    font-size: 16px;
    text-decoration: none;
    color: #000;
}