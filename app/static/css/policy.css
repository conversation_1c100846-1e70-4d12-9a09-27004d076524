:root {
    --primary-color: #f7e9e9; 
    --secondary-color: #555; 
    --button-hover-color: #777; 
}

.custom-card {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 20px;
    background-color: var(--primary-color);
    border-radius: 15px;
    transition: all 0.3s ease;
    height: 30vh;
    overflow: hidden;
    border: 4px solid var(--secondary-color);
}

.custom-card:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.custom-title {
    color: rgb(4, 4, 4);
    cursor: pointer;
    margin-top: auto;
    margin-bottom: auto;
}

.custom-btn {
    margin-top: 10px;
    margin-right: 5px;
    width: auto;
    padding: auto;
    margin-bottom: 10px;
    background-color: var(--secondary-color);
    color: #fff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.custom-btn:hover {
    background-color: var(--button-hover-color);
}

.button-group {
    justify-content: space-between;
    align-items: center;
    position: relative;
    margin-top: auto;
}

#previewModal .modal-body {
    max-width: 90%;
    margin-right: auto;
    overflow-x: auto;
}

#previewModal table {
    width: 100%;
    white-space: nowrap;
}

#previewModal td,
#previewModal th {
    word-wrap: break-word;
}

#previewModal th,
#previewModal td {
    border: 1px solid #ddd;
    text-align: center;
}

#previewModal th {
    background-color: #f2f2f2;
}
.popup {
	width: 400px;
	background: #fff;
	border-radius: 6px;
	position: fixed;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	text-align: center;
	padding: 0 30px 30px;
	color: #333;
	visibility: hidden;
	border: 1px solid black;
}

.popup img {
	width: 100px;
	margin-top: -50px;
	border-radius: 50%;
	box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.popup h2 {
	font-size: 38px;
	font-weight: 500;
	margin: 30px 0 10px;
}

.popup button {
	width: 100%;
	margin-top: 0px;
	padding: 10px 0;
	background: #d64949;
	color: #fff;
	border: 0;
	outline: none;
	font-size: 18px;
	border-radius: 4px;
	cursor: pointer;
	box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.popup a {
	width: 100%;
	margin-top: 0px;
	padding: 10px 0;
	background: #d64949;
	color: #fff;
	border: 0;
	outline: none;
	font-size: 18px;
	border-radius: 4px;
	cursor: pointer;
	box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
	text-decoration: none;
}

.popup span {
	display: flex;
}

.popup input {
	margin-left: 5em;
}