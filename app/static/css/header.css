.header {
	background-color: #101010;
	box-shadow: 0 1px 1px #ccc;
	padding: 25px 40px;
	height: 100px;
	color: #ffffff;
	box-sizing: border-box;
	overflow: hidden;
}

.header .header_navbar {
	text-align: center;
	margin-left: 3em;
}

/* Logo */

.header .header_navbar h1 {
	float: left;
	font: 40px Expletus Sans;
	margin: 0;
	transition: 0.1s ease;
	margin-left: -0.7em;
}

.header .header_navbar h1 span {
	color: #D52027;
}

/* The navigation links */

.header .header_navbar a {
	color: #ffffff;
	text-decoration: none;
}

/* Login/Sign up buttons */

.header .header_navbar .avatar {
	font: 18px Arial, Helvetica, sans-serif;
	float: right;
	margin: 5px;
}

.header .header_navbar .avatar a {
	display: inline-block;
	opacity: 0.9;
	font-weight: bold;
	background-color: #575a5c;
	padding: 10px;
	border-radius: 3px;
}

.header .header_navbar .avatar a:hover {
	opacity: 1;
}

.profile {
	max-width: 60px;
	height: auto;
	width: auto\9;
	margin-top: -1.0em;
}

/* Making the header responsive */

@media all and (max-width: 600px) {

	.header {
		padding: 25px;
		height: 85px;
	}

	.header .header_navbar h1 {
		float: none;
		margin: -8px 0 2px;
		text-align: center;
		font-size: 24px;
		line-height: 1;
	}

	.header .header_navbar nav {
		margin: 0;
		float: none;
	}

	.header .header_navbar nav li a {
		font-size: 13px;
	}

	.header .header_navbar ul {
		display: none;
	}

}

/* For the headers to look good, be sure to reset the margin and padding of the body */
body {
	margin: 0;
	padding: 0;
}

.sidebar .close {
	height: 43px;
	margin-left: 155px;
	margin-top: 1.5em;
	z-index: 200;
	transition: all 0.1s ease;
}

.sidebar .close:hover {
	opacity: 2;
	cursor: pointer;
	transition: all 0.1s ease;
}

.sidebar {
	position: fixed;
	left: -10em;
	top: 0;
	height: 100%;
	width: 0px;
	background: #101010;
	padding: 6px 14px;
	z-index: 99;
	transition: all 0.5s ease;
}

.sidebar .logo-details {
	height: 60px;
	display: flex;
	align-items: center;
	position: relative;
}

.sidebar .logo {
	margin-top: -4em;
	height: 80px;
	width: 230px;
	visibility: hidden;
	z-index: 0;
	transition: all 0.1s ease;
}

.sidebar .logo-details .icon {
	opacity: 1;
	transition: all 0.5s ease;
}

.sidebar .logo-details .logo_name {
	color: #fff;
	font-size: 20px;
	font-weight: 600;
	opacity: 1;
	transition: all 0.5s ease;
}

.sidebar .logo-details #btn {
	position: absolute;
	top: 50%;
	right: 0;
	transform: translateY(-50%);
	font-size: 22px;
	transition: all 0.4s ease;
	font-size: 23px;
	text-align: center;
	cursor: pointer;
	transition: all 0.5s ease;
}

.sidebar .img-sidebar {
	width: 2em;
	color: #fff;
	height: 50px;
	font-size: 28px;
	text-align: center;
	line-height: 60px;
	padding: 7px;
	visibility: hidden;
}

.sidebar .nav-list {
	margin-top: 10px;
	height: 100%;
	margin-left: -35px;
}

.sidebar li {
	position: relative;
	margin: 8px 0;
	list-style: none;
}

.sidebar li .tooltip {
	position: absolute;
	top: -20px;
	color: #fff;
	left: calc(100% + 15px);
	z-index: 3;
	background: #557A95;
	box-shadow: 0 5px 10px rgba(22, 22, 22, 0.3);
	padding: 6px 12px;
	border-radius: 4px;
	font-size: 15px;
	font-weight: 400;
	opacity: 0;
	white-space: nowrap;
	pointer-events: none;
	transition: 0s;
}

.sidebar li:hover .tooltip {
	opacity: 1;
	pointer-events: auto;
	transition: all 0.4s ease;
	top: 50%;
	transform: translateY(-50%);
}

.sidebar li a {
	display: flex;
	height: 100%;
	width: 100%;
	border-radius: 12px;
	align-items: center;
	text-decoration: none;
	transition: all 0.4s ease;
	background: rgba(22, 22, 22, 1);
}

.sidebar li a:hover {
	background: #557A95;
}

.sidebar li a .links_name {
	display: none;
	color: #fff;
	font-size: 15px;
	font-weight: 400;
	white-space: nowrap;
	opacity: 1;
	pointer-events: none;
	transition: 0.4s;
}

.sidebar li i {
	height: 50px;
	line-height: 50px;
	font-size: 18px;
	border-radius: 12px;
}

.logo h1 {
	float: left;
	font: normal 35px 'Expletus Sans';
	;
	margin: 0;
	transition: 0.1s ease;
	margin-top: 0.4em;
}

.logo span {
	color: #D52027;
}

/* The navigation links */

.logo a {
	color: #ffffff;
	text-decoration: none;
}

@media (max-width: 420px) {
	.sidebar li .tooltip {
		display: none;
	}
}
.bottom-sidebar {
    position: absolute;
    bottom: 0;
}
.bottom-sidebar li a:hover {
	background: #557A95;
	width: 230px;
}