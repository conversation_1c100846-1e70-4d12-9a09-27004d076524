.inline-field {
	display: inline-block;
	margin-left: 10px;
}

.popup {
	width: 400px;
	background: #fff;
	border-radius: 6px;
	position: fixed;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	text-align: center;
	padding: 0 30px 30px;
	color: #333;
	visibility: hidden;
	border: 1px solid black;
}

.popup img {
	width: 100px;
	margin-top: -50px;
	border-radius: 50%;
	box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.popup h2 {
	font-size: 38px;
	font-weight: 500;
	margin: 30px 0 10px;
}

.popup button {
	width: 100%;
	margin-top: 0px;
	padding: 10px 0;
	background: #d64949;
	color: #fff;
	border: 0;
	outline: none;
	font-size: 18px;
	border-radius: 4px;
	cursor: pointer;
	box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.popup a {
	width: 100%;
	margin-top: 0px;
	padding: 10px 0;
	background: #d64949;
	color: #fff;
	border: 0;
	outline: none;
	font-size: 18px;
	border-radius: 4px;
	cursor: pointer;
	box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
	text-decoration: none;
}

.popup span {
	display: flex;
}

.popup input {
	margin-left: 5em;
}



.styled-table {
    border-collapse: collapse;
    width: 100%;
    margin: 25px 0;
    font-size: 1em;
    font-family: sans-serif;
    font-weight: 500;
    min-width: 400px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
    text-align: center;
}

.styled-table caption {
    caption-side: top;
    font-size: 14px;
    font-family: sans-serif;
}

.styled-table thead tr {
    background-color: #557A95;
    color: #ffffff;
}

.styled-table th,
.styled-table td {
    padding: 12px 15px;
}

.styled-table tbody tr {
    border-bottom: 1px solid #dddddd;
}

.styled-table tbody tr:nth-of-type(even) {
    background-color: #f3f3f3;
}

.styled-table tbody tr:last-of-type {
    border-bottom: 2px solid #557A95;
}

.styled-table tbody tr.active-row {
    font-weight: bold;
    color: #0073e6;
}

.styled-table tbody tr a {
    font-weight: 600;
    color: #000;
    text-decoration: none;
}

button {
    border-radius: 5px;
    font-size: 16px;
    font-family: system-ui, sans-serif;
    font-weight: 600;
}

h1 {
    position: relative;
    padding: 0;
    margin: 0;
    font-family: "Raleway", sans-serif;
    font-weight: 300;
    font-size: 40px;
    color: #080808;
    -webkit-transition: all 0.4s ease 0s;
    -o-transition: all 0.4s ease 0s;
    transition: all 0.4s ease 0s;
}


.page_heading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 35px;
}

.page_heading_secondary {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 35px;
}

.page_heading h1 {
    font-size: 26px;
    font-weight: 700;
    letter-spacing: 1px;
    text-transform: uppercase;
    white-space: nowrap;
    padding-bottom: 13px;
    margin: 0;
}

.page_heading_secondary h1 {
    font-size: 26px;
    font-weight: 500;
    letter-spacing: 1px;
    white-space: nowrap;
    padding-bottom: 13px;
    margin: 0;
}

.page_heading h1:before {
    background-color: #c50000;
    content: '';
    display: block;
    height: 3px;
    width: 110px;
    margin-bottom: 5px;
}

.page_heading h1:after {
    background-color: #c50000;
    content: '';
    display: block;
    position: relative;
    left: 200px;
    bottom: 0;
    height: 3px;
    width: 100px;
    margin-bottom: 0.25em;
}
.alert-error {
    color: rgb(240, 29, 29);
    background-color: #ebdfdf;
    border-color: #ebdfdf;
    text-align: center;
}