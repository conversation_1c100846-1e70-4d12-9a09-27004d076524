tr {
    display: block;
    float: left;
}

th,
td {
    display: block;
}

.styled-table {
    border: 2px solid #000;
    border-collapse: collapse;
    margin: 25px 0;
    font-size: 1em;
    font-family: sans-serif;
    font-weight: 500;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
    text-align: center;
    margin-top: 1em;
}

.styled-table caption {
    caption-side: top;
    font-weight: 600;
    font-size: 20px;
    text-align: center;
    font-family: sans-serif;
}

.styled-table caption span {
    color: #000;
}

.styled-table tr th {
    background-color: #557A95;
    color: #ffffff;
}

.styled-table th,
.styled-table td {
    padding: 12px 15px;
    font-weight: 600;
}

.styled-table tbody tr:nth-of-type(even) {
    background-color: #f3f3f3;
}

.styled-table tbody tr a {
    font-weight: bold;
    color: #000;
    text-decoration: none;
}

.container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-top: 2em;
    margin-bottom: 3em;
    flex-wrap: wrap
}

.box {
    border: 2px solid #808080;
    box-shadow: 2px 2px 2px 2px rgb(0, 0, 0);
    padding: 0em 1em;
    border-radius: 8px;
    align-items: center;
    text-align: center;
    max-width: 700px;
    margin-top: 2em;
    margin-bottom: 2em;
}

.box-name {
    color: #000;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 0.5em;
    text-align: center;
    border-radius: 8px;
    padding: 0.3em;
    background-color: #f3f3f3;
    border: 1px solid black;
    margin-top: -1em;
}

.box img {
    width: 500px;
    height: 500px;
}

.popup {
    width: 300px;
    background: #fff;
    border-radius: 6px;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    padding: 0 15px 15px;
    color: #333;
    border: 1px solid black;
}

.popup img {
    width: 70px;
    margin-top: -50px;
    border-radius: 50%;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.popup h2 {
    font-size: 20px;
    font-weight: 500;
    margin: 30px 0 10px;
}

.popup button {
    width: 100%;
    margin-top: 0px;
    padding: 10px 0;
    background: #d64949;
    color: #fff;
    border: 0;
    outline: none;
    font-size: 18px;
    border-radius: 4px;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    margin-top: 1em;
}

.popup a {
    font-size: 16px;
    text-decoration: none;
    color: #000;
}

.sub-container {
    padding: 0.5em;
}
.user-tab {
    display: flex;
    width: 100%;
    background-color: #ddd;
    padding: 5px;
    flex-wrap: wrap;
}

.user {
    display:flex; 
    padding: 10px 10px 0px; 
    background-color: #fff; 
    border: 1px solid black; 
    margin-left: 2em;
    margin-bottom: 1em; 
    align-items:center;
    border-radius: 8px;
    margin-top: 2em;
    font-size: 14px;
    font-weight: 500;
    box-shadow:0px 0px 1px 1px rgba(0,0,0,0.5);
}

.user p {
    margin-bottom: 5px;
    margin-right: -10px;
}
.close-icon
{
  display:block;
  box-sizing:border-box;
  width:20px;
  height:20px;
  border-width:3px;
  border-style: solid;
  border-color:rgb(197, 80, 80);
  border-radius:100%;
  background: -webkit-linear-gradient(-45deg, transparent 0%, transparent 46%, white 46%,  white 56%,transparent 56%, transparent 100%), -webkit-linear-gradient(45deg, transparent 0%, transparent 46%, white 46%,  white 56%,transparent 56%, transparent 100%);
  background-color:rgb(233, 113, 113);
  box-shadow:0px 0px 3px 2px rgba(0,0,0,0.5);
  transition: all 0.3s ease;
  margin-top: -50px;
  margin-left: 10px;
  margin-right: -20px;
}
.user-tab img {
    width: 30px;
    height: 30px;
    position: relative;
    margin-top: -25px;
    left: 50%;
}
.user-tab img:hover {
    cursor: pointer;
}

.slippage_charts {
    width: 45%;
    float: left;
}
.clickable_cell {
    cursor: pointer;
}

.dropdown-menu {
    position: absolute;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1;
}

