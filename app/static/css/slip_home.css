h1 {
    position: relative;
    padding: 0;
    margin: 0;
    font-family: "Raleway", sans-serif;
    font-weight: 300;
    font-size: 40px;
    color: #080808;
    -webkit-transition: all 0.4s ease 0s;
    -o-transition: all 0.4s ease 0s;
    transition: all 0.4s ease 0s;
}

.popup {
    width: 500px;
    background: #fff;
    border-radius: 6px;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    padding: 0 15px 15px;
    color: #333;
    border: 1px solid black;
}

.popup h2 {
    font-size: 20px;
    font-weight: 500;
    margin: 30px 0 10px;
}

.popup button {
    width: 40%;
    margin-top: 0px;
    margin-left: 5%;
    padding: 10px 0;
    color: #fff;
    border: 0;
    outline: none;
    font-size: 18px;
    border-radius: 4px;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    margin-top: 1em;
}

.popup button:disabled {
    cursor: not-allowed;
}

.popup a {
    font-size: 16px;
    text-decoration: none;
    color: #000;
}

.page_heading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 35px;
}

.page_heading_secondary {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 35px;
}

.page_heading h1 {
    font-size: 26px;
    font-weight: 700;
    letter-spacing: 1px;
    text-transform: uppercase;
    white-space: nowrap;
    padding-bottom: 13px;
    margin: 0;
}

.page_heading_secondary h1 {
    font-size: 26px;
    font-weight: 500;
    letter-spacing: 1px;
    white-space: nowrap;
    padding-bottom: 13px;
    margin: 0;
}

.page_heading h1:before {
    background-color: #c50000;
    content: '';
    display: block;
    height: 3px;
    width: 110px;
    margin-bottom: 5px;
}

.page_heading h1:after {
    background-color: #c50000;
    content: '';
    display: block;
    position: relative;
    left: 200px;
    bottom: 0;
    height: 3px;
    width: 100px;
    margin-bottom: 0.25em;
}

.dropdown-div {
    display: flex;
    justify-content: center;
    max-width: 450px;
    margin: 0 auto;
}

#timewise_graph {
    width: 300px;
    background: #4d0000; 
    color: white; 
    margin-left: 100px;
}

.slippage-docs {
    padding-left: 70%;
}

.table-wrapper {
    overflow: hidden;
}

h3 {
    font-size: 30px;
    font-weight: 350;
    margin-bottom: 15px;
}

.table-child {
    width: 45%;
    float: left;
    margin: 2.5%;
}

table {
    width: 100%;
    table-layout: fixed;
    border-bottom-left-radius: 5px;
}

.tbl-header {
    background-color: #4d0000;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.tbl-content {
    height: 350px;
    overflow-x: auto;
    margin-top: 0px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.12),
        0 2px 2px rgba(0, 0, 0, 0.12),
        0 4px 4px rgba(0, 0, 0, 0.12),
        0 8px 8px rgba(0, 0, 0, 0.12),
        0 16px 16px rgba(0, 0, 0, 0.12);
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.tbl-content tr:nth-child(even) {
    background-color: #E8E8E8;
}

.tbl-content-first {
    height: 150px;
    overflow-x: auto;
    margin-top: 0px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.12),
        0 2px 2px rgba(0, 0, 0, 0.12),
        0 4px 4px rgba(0, 0, 0, 0.12),
        0 8px 8px rgba(0, 0, 0, 0.12),
        0 16px 16px rgba(0, 0, 0, 0.12);
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.tbl-content-first tr:nth-child(even) {
    background-color: #E8E8E8;
}

th {
    padding: 5px;
    text-align: left;
    font-weight: 500;
    color: #fff;
    text-transform: uppercase;
}

td {
    padding: 10px;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
    font-weight: 400;
    color: #2f4251;
    border-bottom: solid 1px rgba(255, 255, 255, 0.1);
}

tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

/* for custom scrollbar for webkit browser*/

::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

::-webkit-scrollbar-thumb {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

.styled-date {
    background-color: #e6f5ff;
    padding: 15px;
    top: 50%;
    left: 50%;
    font-family: "Roboto Mono", monospace;
    color: black;
    font-size: 18px;
    border: none;
    outline: none;
    border-radius: 5px;
}

.styled-date::-webkit-calendar-picker-indicator {
    background-color: #ffffff;
    padding: 5px;
    cursor: pointer;
    border-radius: 3px;
}

.styled-date::-webkit-inner-spin-button {
    display: none;
}

.styled-date::-webkit-clear-button {
    display: none;
}