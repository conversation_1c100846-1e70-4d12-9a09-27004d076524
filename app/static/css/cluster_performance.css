.dataframe {
    border: 2px solid #000;
    margin: 10px 0;
    font-size: 1em;
    text-align: center;
    font-family: sans-serif;
    font-weight: 500;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
    text-align: center;
    margin-top: 1em;
}

.dataframe caption {
    caption-side: top;
    font-weight: 600;
    font-size: 10px;
    text-align: center;
    font-family: sans-serif;
}

.dataframe caption span {
    color: #000;
}

.dataframe tr th {
    text-align: center;
    background-color: #557A95;
    color: #ffffff;
    border: 2px solid #000;
}

.dataframe th,
.dataframe td {
    text-align: center;
    padding: 5px 5px;
    font-weight: 600;
    border: 2px solid #000;
}

.dataframe tbody tr:nth-of-type(even) {
    background-color: #f3f3f3;
}

.dataframe tbody tr a {
    font-weight: bold;
    color: #000;
    text-decoration: none;
}

.my_container {
    margin: 3em;
    overflow: auto;
}

.popup {
    width: 400px;
    background: #fff;
    border-radius: 6px;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    padding: 0 30px 30px;
    color: #333;
    visibility: hidden;
    border: 1px solid black;
}

.popup img {
    width: 100px;
    margin-top: -50px;
    border-radius: 50%;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.popup h2 {
    font-size: 38px;
    font-weight: 500;
    margin: 30px 0 10px;
}

.popup button {
    width: 100%;
    margin-top: 0px;
    padding: 10px 0;
    background: #d64949;
    color: #fff;
    border: 0;
    outline: none;
    font-size: 18px;
    border-radius: 4px;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.popup span {
    display: flex;
}

.popup input {
    margin-left: 5em;
}

  
.tbl-header {
margin-top: 20px;
background-color: #660000;
border: 2px solid rgba(255, 255, 255, 0.3);
}

.tbl-content {
overflow-x: auto;
margin-top: 0px;
border: 1px solid rgba(255, 255, 255, 0.3);
box-shadow: 0 1px 1px rgba(0, 0, 0, 0.12),
    0 2px 2px rgba(0, 0, 0, 0.12),
    0 4px 4px rgba(0, 0, 0, 0.12),
    0 8px 8px rgba(0, 0, 0, 0.12),
    0 16px 16px rgba(0, 0, 0, 0.12);
}

.tbl-content tr:nth-child(even) {
background-color: #E8E8E8;
}

th {
padding: 10px;
text-align: left;
font-weight: 500;
color: #fff;
font-size: 16px;
}

td {
padding: 10px;
text-align: left;
overflow: hidden;
text-overflow: ellipsis;
vertical-align: middle;
font-size: 15px;
font-weight: 400;
border-bottom: solid 1px rgba(255, 255, 255, 0.1);
}
.alert-error {
    color: rgb(240, 29, 29);
    background-color: #ebdfdf;
    border-color: #ebdfdf;
    text-align: center;
}