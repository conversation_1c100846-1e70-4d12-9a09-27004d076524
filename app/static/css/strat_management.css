.tab {
  overflow: hidden;
  border: 1px solid #ccc;
  background-color: #f1f1f1;
  display: flex;
  justify-content: space-around;
}

/* Style the buttons that are used to open the tab content */
.tab button {
  background-color: inherit;
  float: left;
  border: none;
  outline: none;
  cursor: pointer;
  padding: 14px 16px;
  transition: 0.3s;
}

/* Change background color of buttons on hover */
.tab button:hover {
  background-color: #ddd;
}

/* Create an active/current tablink class */
.tab button.active {
  background-color: #ccc;
}

/* Style the tab content */
.tabcontent {
  display: none;
  padding: 6px 12px;
  border: 1px solid #ccc;
  border-top: none;
}

table {
  width: 100%;
  table-layout: fixed;
}

.tbl-header {
  margin-top: 20px;
  background-color: #660000;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.tbl-content {
  overflow-x: auto;
  margin-top: 0px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.12),
    0 2px 2px rgba(0, 0, 0, 0.12),
    0 4px 4px rgba(0, 0, 0, 0.12),
    0 8px 8px rgba(0, 0, 0, 0.12),
    0 16px 16px rgba(0, 0, 0, 0.12);
}

.tbl-content tr:nth-child(even) {
  background-color: #E8E8E8;
}

th {
  padding: 10px;
  text-align: left;
  font-weight: 500;
  color: #fff;
  text-transform: uppercase;
  cursor: pointer;
  font-size: 16px;
}

td {
  padding: 10px;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
  font-size: 15px;
  font-weight: 400;
  border-bottom: solid 1px rgba(255, 255, 255, 0.1);
}

tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.clickable_cell {
  cursor: pointer;
}

.legend-container {
  float: left;
  margin-bottom: 20px;
}

.color-item {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 5px;
  border-radius: 50%;
}

.legend li {
  display: inline-block;
  margin-right: 10px;
  font-size: 14px;
}