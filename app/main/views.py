from flask import render_template, url_for, current_app, redirect
from app.main import main
from flask_login import current_user
from flasgger import swag_from


@main.route("/")
@main.route("/home")
@swag_from(
    "/docs/strategy_management/index.yaml",
    methods=["GET"],
)
def index():
    current_app.logger.info("Home page accessed")
    if current_user.is_authenticated:
        return redirect(url_for("strat_add.strategy_home"))
    else:
        return render_template("home.html")
