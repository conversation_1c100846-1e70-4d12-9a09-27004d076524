from flask_wtf import <PERSON>laskForm
from wtforms import <PERSON><PERSON><PERSON>, PasswordField, SubmitField, BooleanField, SelectField
from wtforms.validators import (
    DataRequired,
    Email,
    EqualTo,
    length,
    ValidationError,
    Length,
    Regexp,
)
from app.models import User


# create user registration form
class RegistrationForm(FlaskForm):
    """
    Form to register a new user.
    """

    username = StringField(
        "Username",
        validators=[
            DataRequired(),
            Length(1, 64),
            Regexp(
                "^[A-Za-z][A-Za-z0-9_.]*$",
                0,
                "Usernames must have only letters, numbers, dots or " "underscores",
            ),
        ],
    )
    email = StringField("Email", validators=[Email(), DataRequired(), Length(1, 64)])
    password = PasswordField("Password", validators=[DataRequired(), length(min=6)])
    confirm_password = PasswordField(
        "Confirm Password",
        validators=[
            DataRequired(),
            EqualTo("password", message="Passwords must match"),
        ],
    )
    roles = SelectField("Role", choices=[], validators=[DataRequired()])
    manager = SelectField("Manager", choices=[], validate_choice=False)
    submit = SubmitField("Register")

    def validate_username(self, username: StringField):
        """Checks if username already exists in the database
            This is an overriden function of Form module called
            automatically whenever form validation is called.

        Args:
            username (StringField): Username field as received from form

        Raises:
            ValidationError: If user already exists in database
        """
        user = User.query.filter_by(username=username.data).first()
        if user:
            raise ValidationError("User Already Exists")

    def validate_email(self, email: StringField):
        """Checks if email already exists in the database

        Args:
            email (StringField): Email field as received from form

        Raises:
            ValidationError: If email already exists in database
        """
        email = User.query.filter_by(email=email.data).first()
        if email:
            raise ValidationError("Email Already Exists")


class LoginForm(FlaskForm):
    """
    Form for login.
    """

    email = StringField("Email or Username", validators=[DataRequired()])
    password = PasswordField("Password", validators=[DataRequired()])
    remember_me = BooleanField("Keep me logged in")
    submit = SubmitField("Login")


class ChangePasswordForm(FlaskForm):
    """
    Form for changing password.
    """

    new_password = PasswordField("Password", validators=[DataRequired(), length(min=6)])
    confirm_new_password = PasswordField(
        "Confirm Password",
        validators=[
            DataRequired(),
            EqualTo("new_password", message="Passwords must match"),
        ],
    )
    submit = SubmitField("Change Password")
