from app.auth.auth_forms import ChangePasswordForm, RegistrationForm, LoginForm
from app.auth import auth
from flask import render_template, url_for, redirect, flash, request, current_app
from app.models import User, db, Role
from werkzeug.security import check_password_hash, generate_password_hash
from flask_login import login_user, current_user, logout_user, login_required
from flasgger import swag_from


@auth.route("/register", methods=["GET", "POST"])
@swag_from("/docs/auth/register_get.yaml", methods=["GET"])
@swag_from("/docs/auth/register_post.yaml", methods=["POST"])
@login_required
def register():
    # View for registering a new user.
    if current_user.role.name not in ["ADMIN", "MANAGER"]:
        current_app.logger.warning("<PERSON><PERSON><PERSON> tried to access the registration page")
        return redirect(url_for("main.index"))
    current_app.logger.info("Registration page accessed")
    form = RegistrationForm()
    roles = Role.query.all()
    managers = Role.query.filter_by(name="MANAGER").first().users.all()
    if current_user.role.name == "MANAGER":
        roles = Role.query.filter(Role.name.in_(["MANAGER", "DEVELOPER"])).all()
    form.roles.choices = [(role.id, role.name) for role in roles]
    form.manager.choices = [manager.username for manager in managers]
    # validate the form data before submitting
    if form.validate_on_submit():
        encrypted_password = generate_password_hash(
            form.password.data, method="pbkdf2:sha1", salt_length=8
        )
        # add the user data to db
        new_user = User(
            username=form.username.data,
            email=form.email.data,
            password_enc=encrypted_password,
            role_id=form.roles.data,
            manager=form.manager.data,
        )
        db.session.add(new_user)
        db.session.commit()
        current_app.logger.info(f"Created a new user {form.username.data}")
        flash("Registered a new user successfully", "success")
        # Redirect to home page if user is registered successfully
        return redirect(url_for("auth.manage_users"))

    return render_template("auth/register.html", form=form)


@auth.route("/login", methods=["GET", "POST"])
@swag_from("/docs/auth/login_get.yaml", methods=["GET"])
@swag_from("/docs/auth/login_post.yaml", methods=["POST"])
def login():
    # Login view.
    form = LoginForm()
    current_app.logger.info("Login page accessed")
    # If user is already logged in, redirect to homepage
    if current_user.is_authenticated:
        return redirect(url_for("main.index"))
    if form.validate_on_submit():
        # query the db to get the user row with the matching email address in the form
        user = User.query.filter_by(email=form.email.data).first()
        if user is None:
            user = User.query.filter_by(username=form.email.data).first()

        if (user is not None) and (
            check_password_hash(user.password_enc, form.password.data)
        ):
            login_user(user, form.remember_me.data)
            current_app.logger.info(f"Logged in as {user.username}")
            return redirect(url_for("main.index"))
        else:
            flash("Invalid Email or Password", "warning")
    return render_template("auth/login.html", form=form)


@auth.route("/sign_out")
@swag_from("/docs/auth/sign_out.yaml", methods=["GET"])
@login_required
def sign_out():
    # Call this to sign out a logged-in user.
    current_app.logger.info("Sign out requested")
    logout_user()
    return redirect(url_for("main.index"))


@auth.route("/change_password", methods=["GET", "POST"])
@swag_from("/docs/auth/change_password_get.yaml", methods=["GET"])
@swag_from("/docs/auth/change_password_post.yaml", methods=["POST"])
@login_required
def change_password():
    # Call this to change the password of a user.
    form = ChangePasswordForm()
    current_app.logger.info("Password changing form accessed")
    if form.validate_on_submit():
        new_password = form.new_password.data
        encrypted_password = generate_password_hash(
            new_password, method="pbkdf2:sha1", salt_length=8
        )
        user = User.query.filter_by(id=current_user.id).first()
        if user is not None:
            user.password_enc = encrypted_password
            db.session.commit()
            current_app.logger.info("Password changed")
            return render_template(
                "auth/profile.html", form=form, vis_success="visible", vis_fail="hidden"
            )
        else:
            return render_template(
                "auth/profile.html", form=form, vis_success="hidden", vis_fail="visible"
            )
    return render_template(
        "auth/profile.html", form=form, vis_success="hidden", vis_fail="hidden"
    )


@auth.route("/user_management", methods=["GET"])
@swag_from("/docs/user_management/manage_users.yaml", methods=["GET"])
@login_required
def manage_users():
    # View to display user management page.
    if current_user.role.name not in ["ADMIN", "MANAGER"]:
        current_app.logger.warning(
            "Unauthorized attempt to access user management page"
        )
        return redirect(url_for("main.index"))
    current_app.logger.info("User management page accessed")
    user_list = User.query.all()
    if current_user.role.name == "MANAGER":
        user_list = current_user.developers
    roles = [role.name for role in Role.query.all()]
    managers = [
        user.username for user in User.query.all() if user.role.name == "MANAGER"
    ]
    return render_template(
        "auth/user_management.html",
        user_list=user_list,
        roles=roles,
        managers=managers,
    )


@auth.route("/user_management/edit_user", methods=["POST"])
@swag_from("/docs/user_management/edit_user.yaml", methods=["POST"])
@login_required
def edit_users():
    # View for changing role of given user.
    if current_user.role.name not in ["ADMIN", "MANAGER"]:
        current_app.logger.warning("Unauthorized attempt to access edit user option")
        return redirect(url_for("main.index"))
    try:
        username = request.form["username"]
        role = request.form["role"]
        user = User.query.filter_by(username=username).first()
        role = Role.query.filter_by(name=role).first()
        if (
            (user is None)
            or (role is None)
            or (
                (user.manager != current_user.username)
                and (current_user.role.name != "ADMIN")
            )
        ):
            current_app.logger.warning(
                "Unauthorized attempt to access edit user option"
            )
            return "failed"
        if user.role.name == "DEVELOPER":
            if role.name == "DEVELOPER":
                manager = request.form["manager"]
                user.manager = manager
            else:
                user.manager = None
        elif user.role.name == "MANAGER":
            user_list = User.query.filter_by(manager=username).all()
            if len(user_list) > 0:
                current_app.logger.info(
                    f"Failed to change role of {username} to {role.name} as role of a manager cannot be changed it they have developers under them"
                )
                return "failed"
            if role.name == "DEVELOPER":
                manager = request.form["manager"]
                if manager == username:
                    current_app.logger.info(
                        f"Failed to change role of {username} to {role.name} as a user cannot be their own manager"
                    )
                    return "failed"
                user.manager = manager
        else:
            if role.name == "DEVELOPER":
                manager = request.form["manager"]
                user.manager = manager
        user.role_id = role.id
        db.session.commit()
        current_app.logger.info(f"Role of {username} changed to {role.name}")
        return "success"
    except Exception as e:
        current_app.logger.error(f"Role changing for {username} failed with error {e}")
        return "failed"


@auth.route("/user_management/edit_user_password", methods=["POST"])
@swag_from("/docs/user_management/edit_password.yaml", methods=["POST"])
@login_required
def edit_user_password():
    # View for changing password for a given user.
    if current_user.role.name not in ["ADMIN", "MANAGER"]:
        current_app.logger.warning(
            "Unauthorized attempt to access edit user password option"
        )
        return redirect(url_for("main.index"))
    try:
        username = request.form["username"]
        password = request.form["password"]
        confirm_password = request.form["confirm_password"]
        user = User.query.filter_by(username=username).first()
        if (
            (user is None)
            or (password != confirm_password)
            or (len(password) < 6)
            or (
                (user.manager != current_user.username)
                and (current_user.role.name != "ADMIN")
            )
        ):
            current_app.logger.warning(
                "Unauthorized attempt to access edit user password option"
            )
            return "failed"
        encrypted_password = generate_password_hash(
            password, method="pbkdf2:sha1", salt_length=8
        )
        user.password_enc = encrypted_password
        db.session.commit()
        current_app.logger.info(f"Password for {username} changed")
        return "success"
    except Exception as e:
        current_app.logger.error(
            f"Password changing for {username} failed with error {e}"
        )
        return "failed"


@auth.route("/user_management/delete_user", methods=["POST"])
@swag_from("/docs/user_management/delete_user.yaml", methods=["POST"])
@login_required
def delete_user():
    # View for deleting a user.
    if current_user.role.name not in ["ADMIN", "MANAGER"]:
        current_app.logger.warning("Unauthorized attempt to access delete user option")
        return redirect(url_for("main.index"))
    try:
        username = request.form["username"]
        user = User.query.filter_by(username=username).first()
        if user is None or (
            (user.manager != current_user.username)
            and (current_user.role.name != "ADMIN")
        ):
            current_app.logger.warning(
                "Unauthorized attempt to access delete user option"
            )
            return "failed"
        db.session.delete(user)
        db.session.commit()
        current_app.logger.info(f"User {username} deleted")
        return "success"
    except Exception as e:
        current_app.logger.error(f"Deleting user failed with error {e}")
        return "failed"
