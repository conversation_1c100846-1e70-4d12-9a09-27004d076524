import pandas as pd
import datetime
import plotly
import plotly.express as px
import plotly.graph_objects as go
import numpy as np
from io import BytesIO
from app.models import minio
from flask import current_app
from flask_login import current_user
from app.models import (
    PendingBacktests,
    StrategyReview,
    Status,
    StrategyMetaData,
    Strategy,
    StrategyAccess,
    User,
    db,
    DeadSheet,
)
from base64 import b64encode
from typing import List, Set, Dict, Optional, Tuple
from app.utility.utils import (
    get_files_from_minio,
    list_files_in_folder,
    get_file_data_from_minio,
)
from sqlalchemy import func
from sqlalchemy.engine import Row
from app.utility.gandalf_util import (
    average_return,
    sharpe_ratio,
    calc_max_dd,
    calc_max_dd_days,
    calc_trading_days,
    number_of_trades,
    get_day_dict,
    get_all_dates_path,
)
import re


def get_backend_service_result(pending_strats: List[str]) -> dict:
    """Return the state of backend service
        If any one of the service fails, it returns as ERROR
        If all services are completed, it returns as DONE
        Otherwise returns as false

    Args:
        pending_strats (List[str]): A list of strategy name whose backtest results are required

    Returns:
        dict: A dictionaries of strategy with their results
    """
    backtest_result = {}
    pending_backtests = PendingBacktests.query.all()
    pending_backtests = {x.strategy_name: x for x in pending_backtests}
    for strat in pending_strats:
        result = "DONE"
        if strat in pending_backtests:
            for state in pending_backtests[strat].service_state:
                if state == "1":
                    result = "ERROR"
                    break
                elif state == "0":
                    result = "Pending"
        else:
            result = "ERROR"
        if result == "DONE":
            for state in pending_backtests[strat].service_state:
                if state == "3":
                    result = "DONE PARTIAL"
        backtest_result[strat] = result
    return backtest_result


def get_backend_service_state(pending_strats: List[str]) -> dict:
    """Returns value of service state of strategies passed
        If strategy is not found in backtest table return state of each service as failed

    Args:
        pending_strats (List[str]): A list of strategy name whose backtest results are required

    Returns:
        dict: A dictionaries of strategy with their results
    """
    service_state = {}
    pending_backtests = PendingBacktests.query.all()
    pending_backtests = {x.strategy_name: x for x in pending_backtests}
    for strat in pending_strats:
        if strat in pending_backtests:
            service_state[strat] = pending_backtests[strat].service_state
        else:
            service_state[strat] = "11111"
    return service_state


def get_review_comments(strat_list: List[Strategy]) -> dict:
    """Check if review comments are available.

    Args:
        strat_list (List[str]): List of strategies

    Returns:
        review_comments (dict): A dict indicating the presence of reviews for each strategy
    """
    review_comments = {}
    strat_review_list = [review.strategy_name for review in StrategyReview.query.all()]
    for strategy in strat_list:
        if strategy.strategy_name in strat_review_list:
            review_comments[strategy.strategy_name] = True
        else:
            review_comments[strategy.strategy_name] = False
    return review_comments


def get_strategies_dd_percent(strat_list: List[Strategy]) -> dict:
    """Get the percentage of current drawdown with max drawdown at strategy submit

    Args:
        strat_list (List[Strategy]): List of strategies

    Returns:
        dict: A dict with key as strat and value as percent of curr
    """
    meta_data_dict = {}
    meta_data_from_db = {
        meta_data.strategy_name: meta_data for meta_data in StrategyMetaData.query.all()
    }
    for strat in strat_list:
        lis = []
        if (
            (strat.strategy_name in meta_data_from_db)
            and (meta_data_from_db[strat.strategy_name].curr_backtest_dd is not None)
            and (meta_data_from_db[strat.strategy_name].max_dd_submit is not None)
        ):
            lis.append(
                (
                    meta_data_from_db[strat.strategy_name].curr_backtest_dd
                    / meta_data_from_db[strat.strategy_name].max_dd_submit
                )
            )
        else:
            lis.append(0)

        if (
            (strat.strategy_name in meta_data_from_db)
            and (meta_data_from_db[strat.strategy_name].curr_backtest_dd is not None)
            and (meta_data_from_db[strat.strategy_name].max_dd_monte is not None)
        ):
            lis.append(
                (
                    meta_data_from_db[strat.strategy_name].curr_backtest_dd
                    / meta_data_from_db[strat.strategy_name].max_dd_monte
                )
            )
        else:
            lis.append(0)

        meta_data_dict[strat] = lis

    return meta_data_dict


def get_strategies_with_metadata(strat_list: List[Strategy]) -> dict:
    """Get the percentage of current drawdown with max drawdown at strategy submit

    Args:
        strat_list (List[Strategy]): List of strategies

    Returns:
        dict: A dict with key as strat and value as percent of curr
    """
    meta_data_dict = {}
    meta_data_from_db = {
        meta_data.strategy_name: meta_data for meta_data in StrategyMetaData.query.all()
    }
    for strat in strat_list:
        if strat.strategy_name in meta_data_from_db:
            meta_data_dict[strat] = meta_data_from_db[strat.strategy_name]
        else:
            meta_data_dict[strat] = None

    return meta_data_dict


def get_charts_from_minio(strategy_name: str, status: str = "pending") -> dict:
    """Get different charts from minio generated by backtest service.

    Args:
        strategy_name (str): Name of the strategy
        status (str, optional): Status of strategy. Defaults to "pending".

    Returns:
        retrieval_url (dict): A dictionary of different chart names and their images
    """
    retrieval_url = {}
    identifiers = ["dd", "monte", "ret", "kde"]
    for identifier in identifiers:
        try:
            chart = BytesIO(
                get_files_from_minio(
                    status=status,
                    strategy_name=strategy_name,
                    post_fix=f"_{identifier}.png",
                )
            )
            dataurl = "data:image/png;base64," + b64encode(chart.getvalue()).decode(
                "ascii"
            )
            retrieval_url[identifier] = dataurl
        except Exception as e:
            current_app.logger.info(
                f"Skipping chart {identifier} for strategy {strategy_name} due to error {e}"
            )
            pass
    return retrieval_url


def check_object_exists(prefix: str) -> bool:
    """Checks if object is present in minio.

    Args:
        prefix (str): Path which is to be searched
    Returns:
        is_present(bool): Returns whether object is present or not
    """
    objects = minio.client.list_objects(
        current_app.config["DASHBOARD_DATA_BUCKET"],
        prefix=prefix,
    )
    obj_names = [obj.object_name for obj in objects]
    if len(obj_names) == 0:
        return False
    return True


def get_mtm_from_minio(strategy_name: str, status: str, week: str = "current"):
    """Retrieves mtm file from minio.

    Args:
        strategy_name (str): Name of strategy
        status (str): Current status of strategy

    Returns:
        mtm (pd.DataFrame): mtm file
    """
    if week == "current":
        post_fix = "_MTM.log"
    else:
        post_fix = f"_{week}_MTM.log"
    mtm = pd.read_csv(
        BytesIO(
            get_files_from_minio(
                status=status, strategy_name=strategy_name, post_fix=post_fix
            )
        )
    )
    mtm["date"] = pd.to_datetime(mtm["date"])
    mtm = mtm.set_index("date")["mtm"]
    return mtm


def get_strategies(state_description: str = "PENDING") -> list:
    """Extracts the required strategies for the current user.

    Args:
        state_description (str): State description of strategies asked

    Returns:
        strats (list): List of strategies with given state description
    """
    strats = (
        Status.query.filter_by(state_description=state_description.upper())
        .first()
        .strategies.all()
    )
    if current_user.role.name == "MANAGER":
        user_list = [user.username for user in current_user.developers]
        user_list.extend([current_user.username])
        strats = [strat for strat in strats if strat.developer in user_list]
    elif current_user.role.name == "DEVELOPER":
        strats = [strat for strat in strats if strat.developer == current_user.username]
    return strats


def get_strats_not_in_test_env() -> list:
    """Extracts the parent strategies of reworked strategies running in test-only mode.
    These parent strategies do not run in test environment.

    Returns:
        strats (list): List of strategies that do not run in test environment
    """
    strats = Status.query.filter_by(state_description="TEST").first().strategies.all()
    return [strat.reworked_strategy for strat in strats if strat.reworked_strategy]


def get_resetted_strats(environment: str) -> list:
    """Reads the strategy reset request file form minio

    Args:
        environment (str): balte environment to check for (TEST or LIVE env)

    Returns:
        list: List of strategies which have been set to have pickle cleared and exits made in the given environment
    """
    resetted_strats = []
    if environment not in ["TEST", "LIVE"]:
        return resetted_strats
    for exchange in current_app.config["SUPPORTED_EXCHANGES"]:
        file_name = f"submitted_requests/reworked_strategies_{environment.upper()}_{exchange.upper()}.csv"
        if check_object_exists(file_name):
            data = get_file_data_from_minio(file_name)
            df = pd.read_csv(
                BytesIO(data),
                header=None,
                dtype=str,
            )
            df.columns = ["strat_name"]
            resetted_strats.extend(df["strat_name"].tolist()[1:])
    return resetted_strats


def get_accessible_strategies(state_description: str = "PENDING") -> list:
    accessible_strats = StrategyAccess.query.filter_by(
        username=current_user.username
    ).all()
    strats = [
        access.strategy
        for access in accessible_strats
        if access.strategy.status.state_description.upper() == state_description
    ]
    return strats


def get_user_lists(strategy: Strategy):
    user_list = [
        access.username
        for access in StrategyAccess.query.filter_by(
            strategy_name=strategy.strategy_name
        ).all()
    ]
    if current_user.username in user_list:
        user_list.remove(current_user.username)
    users = [
        user.username
        for user in User.query.all()
        if (user.username not in user_list) and (user.role.name != "ADMIN")
    ]
    if current_user.role.name == "MANAGER":
        users = [
            user.username
            for user in current_user.developers
            if user.username not in user_list
        ]
    if current_user.username in users:
        users.remove(current_user.username)
    return user_list, users


def remove_backtests(strategy_name: str):
    """Removing backtests when strategy is accepted, rejected or deleted.

    Args:
        strategy_name (str): strategy name
    """
    backtests = PendingBacktests.query.filter_by(strategy_name=strategy_name).all()
    for backtest in backtests:
        db.session.delete(backtest)

    db.session.commit()


def remove_access(strategy_name: str):
    """Removing access when strategy is accepted, rejected or deleted.

    Args:
        strategy_name (str): strategy name
    """
    strat_access = StrategyAccess.query.filter_by(strategy_name=strategy_name).all()
    for access in strat_access:
        db.session.delete(access)
    db.session.commit()


def get_live_pnl_curve(
    strategy_name: str,
    status: str,
    start_day: datetime.date,
    dead_trades: List[Row],
    week: str = "current",
) -> str:
    """
    Calculates cumulative mtm and live PnL and return html code of the generated graph
    Args:
        strategy_name (str): name of the strategy
        status (str): status of the strategy either submitted, pending or rejected
        start_day (datetime.date): day on which the strategy went live

    Returns:
        fig_html (str): Return html code of the PnL curve as string
    """

    dead_trades = pd.DataFrame(dead_trades, columns=["Date", "Pnl", "Count"])
    dead_trades["Date"] = pd.to_datetime(dead_trades["Date"]).dt.date
    try:
        mtm = get_mtm_from_minio(strategy_name=strategy_name, status=status, week=week)
    except Exception:
        error_trace = go.Scatter(
            x=[0],
            y=[0],
            text=[f"mtm not present for {strategy_name} and week {week}"],
            mode="text",
        )
        error_fig = go.Figure(error_trace)
        fig_html = plotly.io.to_html(error_fig)
        return fig_html

    cum_mtm = mtm.cumsum()
    dead_trades_cum_mtm = dead_trades["Pnl"].cumsum()

    mtm_offset = cum_mtm[
        cum_mtm.index.get_level_values(0) < pd.to_datetime(dead_trades.loc[0, "Date"])
    ].iloc[-1]
    dead_trades_cum_mtm = dead_trades_cum_mtm + mtm_offset

    trace = go.Scatter(
        x=cum_mtm.index,
        y=cum_mtm,
        text=mtm,
        hovertemplate="<b>MTM:</b> %{text}<br><b>CumMTM:</b> %{y}<br><b>Date:</b> %{x}",
    )
    fig = go.Figure(trace)
    fig.update_layout(title="Live PnL Curve", width=1000, height=500)
    fig.add_scatter(
        x=dead_trades["Date"],
        y=dead_trades_cum_mtm,
        text=dead_trades["Pnl"],
        hovertemplate="<b>MTM:</b> %{text}<br><b>CumMTM:</b> %{y}<br><b>Date:</b> %{x}",
    )
    fig.add_vline(
        x=dead_trades.loc[0, "Date"],
        line_width=2,
        line_dash="dash",
        line_color="red",
    )
    fig["data"][0]["name"] = "Backtest MTM"
    fig["data"][1]["name"] = "Live MTM"
    fig.update_layout(
        title={
            "text": "Live PnL Curve",
            "x": 0.5,
            "xanchor": "center",
            "y": 0.83,
            "yanchor": "top",
        },
        xaxis_title="",
        yaxis_title="",
        legend_title="",
    )

    if start_day + datetime.timedelta(days=30) < dead_trades.loc[0, "Date"]:
        fig.add_vline(x=start_day, line_width=2, line_dash="dash", line_color="green")
    fig_html = plotly.io.to_html(fig)

    return fig_html


def get_gandalf(
    dead_trades: List[Row], exchange: str = "NSE", segment: str = "OPTIDX"
) -> pd.DataFrame:
    """Generates gandalf dataframe from dead trades list

    Args:
        dead_trades (List[Row]): List of deadtrades containing daywise PnL and tradecount
        exchange (str, optional): exchange name for the strategy. Defaults to "NSE".
        segment (str, optional): segment name for the strategy. Defaults to "OPTIDX".

    Returns:
        pd.DataFrame: Gandalf Dataframe
    """
    dead_trades = pd.DataFrame(dead_trades, columns=["Date", "Pnl", "Count"])
    dead_trades["Date"] = pd.to_datetime(dead_trades["Date"])
    dead_trades.set_index("Date", inplace=True)

    mtm = dead_trades["Pnl"]
    trade_count = dead_trades["Count"]

    all_dates_path = get_all_dates_path(exchange=exchange, segment=segment)

    day_dict = get_day_dict(exchange=exchange, all_dates_path=all_dates_path)

    gandalf = pd.DataFrame()
    gandalf["AvgMonthlyRet"] = average_return(mtm)
    gandalf["MonthSR"] = sharpe_ratio(mtm)
    gandalf["DailySR"] = sharpe_ratio(mtm, grouper="day")
    gandalf["MaxDD"] = calc_max_dd(mtm)
    gandalf["MaxDDdays"] = calc_max_dd_days(mtm, day_dict)
    gandalf["RetDD"] = gandalf["AvgMonthlyRet"] * 12 / gandalf["MaxDD"]
    gandalf["TradingDays"] = calc_trading_days(mtm)
    gandalf["TradeCount"] = number_of_trades(trade_count)

    return gandalf


def convert_date_to_filename_format(date_str: str) -> str:
    """convert date string from format 10-Nov-2023 to 2023_11_10

    Args:
        date_str (str): date string

    Returns:
        str: returns date string in required format
    """
    date_obj = datetime.datetime.strptime(date_str, "%d-%b-%Y")
    return date_obj.strftime("%Y_%m_%d")


def get_available_historical_dates(
    folders: List[str], suffix: Optional[str] = "backtest_results.html"
) -> Dict[str, Set[str]]:
    """get dates for which historical performances are present
    Args:
        folders (List[str]): List of folders
        suffix (Optional[str], optional): suffix to search for. Defaults to "backtest_results.html".

    Returns:
        Dict[str,Set[str]]: returns dictionary of set of dates
    """
    historical_dates = {}
    # Define a regular expression pattern to match dates in file names
    date_pattern = r"\d{4}_\d{2}_\d{2}"
    historical_dates = {}
    for folder in folders:
        files_in_folder = list_files_in_folder(f"submitted_strats/{folder}")
        files_in_folder = [file for file in files_in_folder if suffix in file]
        if folder not in historical_dates:
            historical_dates[folder] = set()
        for file_name in files_in_folder:
            # Extract the date from the file name
            match = re.search(date_pattern, file_name)
            if match:
                date = match.group()
                date = datetime.datetime.strptime(date, "%Y_%m_%d")
                date = date.strftime("%d-%b-%Y")
                historical_dates[folder].add(date)
        historical_dates[folder] = sorted(
            historical_dates[folder],
            key=lambda date_str: datetime.datetime.strptime(date_str, "%d-%b-%Y"),
            reverse=True,
        )
    return historical_dates


def get_all_md_files(path: str, folder_name: str) -> Dict[str, List]:
    """Fetches all policies present on minio

    Args:
        path (str): path to csv file containing path to title mapping
        folder_name (str): folder name in which policies are present

    Returns:
        Dict[str,List]: returns a dictionary from path to list of title, content and optional description
    """
    files = list_files_in_folder(folder_name=folder_name)
    if path in files:
        data = get_file_data_from_minio(path)
        df = pd.read_csv(BytesIO(data), index_col=0)
    else:
        df = pd.DataFrame(columns=["index", "path", "title"])
    policies = {}
    for key in files:
        if key.endswith(".md"):
            if len(df[df["path"] == key]):
                title = df[df["path"] == key]["title"].values[0]
                file_data = get_file_data_from_minio(key).decode("utf-8")
                if "description" in df.columns:
                    description = df[df["path"] == key]["description"].values[0]
                    policies[key] = [title, file_data, description]
                else:
                    policies[key] = [title, file_data]
    return policies
