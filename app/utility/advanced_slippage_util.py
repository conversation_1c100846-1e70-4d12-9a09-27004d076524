"""
Advanced slippage analysis utilities for Flask integration.
Converts Streamlit plotting functions to Flask-compatible JSON data generators.
"""

import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
import plotly.colors as pc
import json
import datetime
from typing import Dict, List, Tuple, Any
import plotly.utils


def enrich_dte(df):
    """
    Enrich dataframe with Days to Expiry (DTE) calculation.
    Adapted from slippage_monitoring_dashboard_v2.py
    """
    df['entry_date'] = pd.to_datetime(df['timestamp']).dt.date
    df['balte_expiry_date'] = pd.to_datetime(df['expiry_date'], dayfirst=True, format='mixed', errors='coerce').dt.normalize()
    
    # Mock ALL_DATES for now - in real implementation, import from balte.balte_config
    # This would need to be properly imported in the actual implementation
    all_dates = pd.date_range(start='2020-01-01', end='2025-12-31', freq='D')
    all_dates_dict = {date: index for index, date in enumerate(all_dates)}
    
    df['dte'] = df.apply(
        lambda x: all_dates_dict.get(pd.Timestamp(x['balte_expiry_date']), -1) - 
                 all_dates_dict.get(pd.Timestamp(x['entry_date']), -1), 
        axis=1
    )
    
    return df


def generate_slippage_trend_chart(df: pd.DataFrame) -> str:
    """
    Generate interactive slippage trend chart.
    Adapted from plot_slippage_interactive function.
    """
    # Aggregate by timestamp and slave_name
    df_agg = df.groupby(["timestamp", "slave_name"])["total_slippage"].sum().reset_index()
    df_agg['timestamp'] = pd.to_datetime(df_agg['timestamp'])
    df_agg = df_agg.sort_values(by=['timestamp', 'slave_name'])
    df_agg['slave_name'] = df_agg['slave_name'].astype(str)
    
    # Color mapping for different slaves
    colors = pc.qualitative.Alphabet
    all_slaves = sorted(df_agg['slave_name'].unique())
    color_map = {name: colors[i % len(colors)] for i, name in enumerate(all_slaves)}
    
    # Create line chart
    fig = px.line(
        df_agg, 
        x='timestamp', 
        y='total_slippage', 
        color='slave_name',
        title="Total Slippage Trend",
        line_shape="linear",
        labels={"slave_name": "Slave Name"},
        color_discrete_map=color_map
    )
    
    fig.update_layout(
        xaxis_title="Timestamp",
        yaxis_title="Total Slippage",
        legend_title="Slave Name",
        xaxis=dict(showgrid=True, rangeslider=dict(visible=True)),
        yaxis=dict(showgrid=True),
        hovermode="x unified",
        template="plotly_white",
        autosize=True,
        responsive=True
    )
    
    return json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)


def generate_daily_slippage_chart(df: pd.DataFrame) -> str:
    """Generate daily aggregated slippage chart."""
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df["date"] = df["timestamp"].dt.date
    df_day = df.groupby(["date", "slave_name"])["total_slippage"].sum().reset_index()
    df_day = df_day.sort_values(by=['date', 'slave_name'])
    df_day['slave_name'] = df_day['slave_name'].astype(str)
    
    colors = pc.qualitative.Alphabet
    all_slaves = sorted(df_day['slave_name'].unique())
    color_map = {name: colors[i % len(colors)] for i, name in enumerate(all_slaves)}
    
    fig = px.line(
        df_day, 
        x='date', 
        y='total_slippage', 
        color='slave_name',
        title="Daily Total Slippage",
        line_shape="linear",
        labels={"slave_name": "Slave Name"},
        color_discrete_map=color_map
    )
    
    fig.update_layout(
        xaxis_title="Date",
        yaxis_title="Total Slippage",
        legend_title="Slave Name",
        xaxis=dict(showgrid=True, rangeslider=dict(visible=True)),
        yaxis=dict(showgrid=True),
        hovermode="x unified",
        template="plotly_white",
        autosize=True,
        responsive=True
    )
    
    return json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)


def generate_strategy_comparison_chart(df: pd.DataFrame) -> str:
    """Generate strategy comparison bar chart."""
    strategy_summary = df.groupby('strategy_name')[
        ['total_slippage', 'execution_signal_slippage', 'execution_slippage', 'turnover']
    ].sum().reset_index().round(0)
    
    strategy_summary['tot_slip_to_turnover'] = strategy_summary['total_slippage'] / strategy_summary['turnover']
    strategy_summary['sig_slip_to_turnover'] = strategy_summary['execution_signal_slippage'] / strategy_summary['turnover']
    strategy_summary['exec_slip_to_turnover'] = strategy_summary['execution_slippage'] / strategy_summary['turnover']
    
    fig = go.Figure()
    
    metrics = {
        'total_slippage': ('Total Slippage', '#4d0000'),
        'execution_signal_slippage': ('Execution Signal Slippage', '#28a745'),
        'execution_slippage': ('Execution Slippage', '#dc3545')
    }
    
    for key, (label, color) in metrics.items():
        fig.add_trace(go.Bar(
            x=strategy_summary['strategy_name'], 
            y=strategy_summary[key], 
            name=label, 
            marker_color=color
        ))
    
    fig.update_layout(
        barmode='group', 
        title='Slippage Comparison by Strategy', 
        xaxis_title='Strategy Name', 
        yaxis_title='Slippage Value',
        template="plotly_white",
        autosize=True,
        responsive=True
    )
    
    return json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)


def generate_slave_comparison_chart(df: pd.DataFrame) -> str:
    """Generate slave comparison bar chart."""
    slave_summary = df.groupby('slave_name')[
        ['total_slippage', 'execution_signal_slippage', 'execution_slippage', 'turnover']
    ].sum().reset_index().round(0)
    
    slave_summary['tot_slip_to_turnover'] = slave_summary['total_slippage'] / slave_summary['turnover']
    slave_summary['sig_slip_to_turnover'] = slave_summary['execution_signal_slippage'] / slave_summary['turnover']
    slave_summary['exec_slip_to_turnover'] = slave_summary['execution_slippage'] / slave_summary['turnover']
    
    fig = go.Figure()
    
    metrics = {
        'total_slippage': ('Total Slippage', '#4d0000'),
        'execution_signal_slippage': ('Execution Signal Slippage', '#28a745'),
        'execution_slippage': ('Execution Slippage', '#dc3545')
    }
    
    for key, (label, color) in metrics.items():
        fig.add_trace(go.Bar(
            x=slave_summary['slave_name'], 
            y=slave_summary[key], 
            name=label, 
            marker_color=color
        ))
    
    fig.update_layout(
        barmode='group', 
        title='Slippage Comparison by Slave', 
        xaxis_title='Slave Name', 
        yaxis_title='Slippage Value',
        template="plotly_white",
        autosize=True,
        responsive=True
    )
    
    return json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)


def generate_timing_histogram(df: pd.DataFrame) -> str:
    """Generate execution timing histogram."""
    # Calculate execution timing difference
    df['e_s_diff_second'] = df['executed_second'] - df['signal_second']
    
    # Filter reasonable timing values (0-60 seconds)
    timing_data = df['e_s_diff_second'][(df['e_s_diff_second'] >= 0) & (df['e_s_diff_second'] <= 60)]
    
    fig = go.Figure(data=[go.Histogram(
        x=timing_data,
        nbinsx=30,
        name='Execution Timing Distribution',
        marker_color='#4d0000',
        opacity=0.7
    )])
    
    fig.update_layout(
        title='Execution Timing Distribution',
        xaxis_title='Execution Delay (seconds)',
        yaxis_title='Frequency',
        template="plotly_white",
        autosize=True,
        responsive=True
    )
    
    return json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)


def calculate_summary_statistics(df: pd.DataFrame) -> Dict[str, Any]:
    """Calculate summary statistics for the dashboard."""
    total_trades = len(df)
    total_turnover = df['turnover'].sum()
    total_slippage = df['total_slippage'].sum()
    avg_execution_time = df.get('e_s_diff_second', pd.Series([0])).mean()
    
    # Calculate slip-to-turnover ratio in BPS
    slip_to_turnover_bps = (total_slippage / total_turnover * 10000) if total_turnover > 0 else 0
    
    return {
        'total_trades': int(total_trades),
        'total_turnover': float(total_turnover),
        'total_slippage': float(total_slippage),
        'avg_execution_time': float(avg_execution_time),
        'slip_to_turnover_bps': float(slip_to_turnover_bps),
        'execution_slippage': float(df['execution_slippage'].sum()),
        'signal_slippage': float(df['execution_signal_slippage'].sum())
    }


def generate_dte_analysis_table(df: pd.DataFrame) -> List[Dict[str, Any]]:
    """Generate DTE (Days to Expiry) analysis table data."""
    if 'dte' not in df.columns:
        df = enrich_dte(df)
    
    dte_analysis = df.groupby(['slave_name', 'dte', 'order_type'])[
        ['total_slippage', 'execution_signal_slippage', 'execution_slippage', 'turnover']
    ].sum().reset_index().round(0)
    
    dte_analysis['tot_slip_to_turnover'] = dte_analysis['total_slippage'] / dte_analysis['turnover']
    dte_analysis['sig_slip_to_turnover'] = dte_analysis['execution_signal_slippage'] / dte_analysis['turnover']
    dte_analysis['exec_slip_to_turnover'] = dte_analysis['execution_slippage'] / dte_analysis['turnover']
    
    return dte_analysis.to_dict('records')


def process_slippage_data(raw_data: List[Dict], filters: Dict[str, Any]) -> Dict[str, Any]:
    """
    Main function to process slippage data and generate all required charts and statistics.
    """
    df = pd.DataFrame(raw_data)
    
    if df.empty:
        return {'error': 'No data available for the selected filters'}
    
    # Calculate additional fields if not present
    if 'total_slippage' not in df.columns:
        df['total_slippage'] = (df['executed_price'] - df['signal_price']) * df['quantity'] * df['buysell']
    
    if 'execution_signal_slippage' not in df.columns:
        df['execution_signal_slippage'] = (df['execution_signal_price'] - df['signal_price']) * df['quantity'] * df['buysell']
    
    if 'execution_slippage' not in df.columns:
        df['execution_slippage'] = (df['executed_price'] - df['execution_signal_price']) * df['quantity'] * df['buysell']
    
    if 'turnover' not in df.columns:
        df['turnover'] = (df['signal_price'] * df['quantity']).abs()
    
    # Generate all charts and data
    result = {
        'summary_stats': calculate_summary_statistics(df),
        'charts': {
            'slippage_trend': generate_slippage_trend_chart(df),
            'daily_slippage': generate_daily_slippage_chart(df),
            'strategy_comparison': generate_strategy_comparison_chart(df),
            'slave_comparison': generate_slave_comparison_chart(df),
            'timing_histogram': generate_timing_histogram(df)
        },
        'tables': {
            'dte_analysis': generate_dte_analysis_table(df)
        },
        'filters_applied': filters
    }
    
    return result
