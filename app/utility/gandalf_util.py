import pandas as pd
import numpy as np
from io import BytesIO
from typing import Dict
from app.utility.utils import get_file_data_from_minio
from flask import current_app


def average_return(mtm: pd.Series) -> pd.Series:
    """Calculates average monthly return for an year

    Args:
        mtm (pd.Series): Daywise mtm with date as index

    Returns:
        pd.Series: Average monthly return with year as index
    """
    monthly = mtm.groupby([mtm.index.year, mtm.index.month]).sum()
    tmp = monthly.groupby(level=0).mean()
    tmp["TOTAL"] = monthly.mean()
    return tmp


def sharpe_ratio(mtm: pd.Series, grouper: str = "month") -> pd.Series:
    """Calculates sharpe ratio of month, week, day or year

    Args:
        mtm (pd.Series): Daywise mtm with date as index
        grouper (str, optional): Group for sharpe ratio calculation. Defaults to "month".

    Returns:
        pd.Series: Sharpe ratio with year as index
    """
    if grouper == "month":
        monthly_mtm = mtm.groupby([mtm.index.year, mtm.index.month]).sum()
        tmp = np.divide(
            monthly_mtm.groupby(level=0).mean(), monthly_mtm.groupby(level=0).std()
        )
        tmp["TOTAL"] = np.divide(monthly_mtm.mean(), monthly_mtm.std())
    elif grouper == "week":
        weekly_mtm = mtm.groupby([pd.Grouper(level=0, freq="W-MON")]).sum()
        tmp = np.divide(
            weekly_mtm.groupby(weekly_mtm.index.year).mean(),
            weekly_mtm.groupby(weekly_mtm.index.year).std(),
        )
        tmp["TOTAL"] = np.divide(weekly_mtm.mean(), weekly_mtm.std())
    elif grouper == "day":
        daily_mtm = mtm.groupby([pd.Grouper(level=0, freq="1D")]).sum()
        tmp = np.divide(
            daily_mtm.groupby(daily_mtm.index.year).mean(),
            daily_mtm.groupby(daily_mtm.index.year).std(),
        )
        tmp["TOTAL"] = np.divide(daily_mtm.mean(), daily_mtm.std())
    else:
        tmp = mtm.groupby(mtm.index.year).mean() / mtm.groupby(mtm.index.year).std()
        tmp["TOTAL"] = np.divide(mtm.mean(), mtm.std())

    return tmp * 100


def calc_max_dd(mtm: pd.Series) -> pd.Series:
    """Calculates maxDD for an year

    Args:
        mtm (pd.Series): Daywise mtm with date as index

    Returns:
        pd.Series: MaxDD with year as index
    """

    def temp_calc(mtm1):
        mtm_cum = mtm1.cumsum()
        drawdown = mtm_cum - mtm_cum.cummax()
        return abs(drawdown.min())

    tmp = dict()
    for year, s in mtm.groupby(mtm.index.year):
        tmp[year] = temp_calc(s)
    tmp["TOTAL"] = temp_calc(mtm)
    return pd.Series(tmp)


def calc_max_dd_days(mtm: pd.Series, day_dict: Dict[pd.Timestamp, int]) -> pd.Series:
    """Calculates maxDD days for an year

    Args:
        mtm (pd.Series): Daywise mtm with date as index
        day_dict (Dict[pd.Datetime, int]): Dictionary with trading days mapped

    Returns:
        pd.Series: MaxDD with year as index
    """

    def temp_calc(mtm1):
        mtm_cum = mtm1.cumsum()
        drawdown = mtm_cum - mtm_cum.cummax()
        indices = [i for i, x in drawdown.items() if x == 0]
        if drawdown.values[-1] < 0:
            indices.append(drawdown.index[-1])
        max_dd_days = 0
        for i in range(len(indices) - 1):
            dd_days = day_dict[indices[i + 1]] - day_dict[indices[i]]
            if dd_days > max_dd_days:
                max_dd_days = dd_days
        return max_dd_days

    tmp = dict()
    for year, s in mtm.groupby(mtm.index.year):
        tmp[year] = temp_calc(s)
    tmp["TOTAL"] = temp_calc(mtm)
    return pd.Series(tmp)


def calc_trading_days(mtm: pd.Series) -> pd.Series:
    """Calculates trading days for an year

    Args:
        mtm (pd.Series): Daywise mtm with date as index

    Returns:
        pd.Series: Trading days with year as index
    """
    tmp = mtm.groupby(mtm.index.year).count()
    tmp["TOTAL"] = mtm.count()
    return tmp


def number_of_trades(trade_count: pd.Series) -> pd.Series:
    """Calculates number of trades for an year

    Args:
        trade_count (pd.Series): Daywise trade count with date as index

    Returns:
        pd.Series: Trade count with year as index
    """
    tmp = trade_count.groupby(trade_count.index.year).sum()
    tmp["TOTAL"] = trade_count.sum()
    return tmp


def get_day_dict(exchange: str, all_dates_path: str) -> Dict[pd.Timestamp, int]:
    """Maps trading days to integer index

    Args:
        exchange (str) : exchange name
        all_dates_path (str): Location of ALL_DATES file at minio

    Returns:
        Dict[pd.Datetime, int]: Dictionary with trading days mapped
    """
    ALL_DATES = np.load(
        BytesIO(get_file_data_from_minio(path=all_dates_path)), allow_pickle=True
    )
    if exchange.lower() in ["us"]:
        ALL_DATES = ALL_DATES[exchange.lower()]
    day_dict = {}
    for date_index, date in enumerate(ALL_DATES):
        day_dict[date] = date_index

    return day_dict


def get_all_dates_path(exchange: str = "NSE", segment: str = "OPTIDX") -> str:
    """Gets ALL DATES minio path for a certain exchange and segment

    Args:
        exchange (str, optional): exchange name of the strategy. Defaults to "NSE".
        segment (str, optional): segment name of the strategy. Defaults to "OPTIDX".

    Returns:
        str: ALL DATES minio path
    """
    all_dates_path = current_app.config["ALL_DATES_DICT"][exchange]
    if exchange == "NSE":
        if segment == "OPTCUR" or segment == "FUTCUR":
            all_dates_path = "ALL_DATES_FX.npy"

    all_dates_path = f"support_files/{all_dates_path}"
    return all_dates_path
