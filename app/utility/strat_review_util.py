import os
import pandas as pd
import numpy as np
import tempfile
from io import BytesIO
from pandas.errors import EmptyDataError
from typing import List, Tuple, Union, Dict, Any, Set
from app.models import (
    minio,
    Strategy,
    Status,
    StrategyAccess,
    StrategyReview,
    StrategyClusterMapping,
    ClusterMappingStatus,
    db,
)
from flask import current_app
from flask_login import current_user
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.fernet import Fernet
from werkzeug.datastructures import FileStorage
from minio.commonconfig import CopySource
from app.utility.strat_detail_util import get_mtm_from_minio
from app.utility.strat_manage_util import delete_from_minio
from app.utility.utils import (
    get_files_from_minio,
    upload_dataframe_to_minio,
    send_message_to_kafka,
)
import datetime as dt


def get_correlation_pending_strats(
    strategy_list: List[List[str]],
) -> <PERSON><PERSON>[pd.DataFrame, List[str]]:
    """Finds correlation between all strategies passed in the list.

    Args:
        strategy_list (List[List[str]]): A list of list. Each list contains 2 elements, strategy name and year.
    Returns:
        Tuple[pd.DataFrame, List[str]]: A tuple where first element is correlation matrix and
                                        second is a list of strategy which are not present on minio.
    """
    mtm_dict = {}
    error_strategy = []
    for strategy in strategy_list:
        try:
            mtm_dict[strategy[0]] = [
                get_mtm_from_minio(strategy_name=strategy[0], status="pending"),
                strategy[1],
            ]
        except Exception as e:
            current_app.logger.warning(
                f"Skipping correlation pending strats for {strategy[0]} due to error {e}"
            )
            error_strategy.append(strategy[0])

    corr_index = pd.DataFrame(
        index=list(mtm_dict.keys()), columns=list(mtm_dict.keys())
    )
    for strat_1, mtm_info_1 in mtm_dict.items():
        for strat_2, mtm_info_2 in mtm_dict.items():
            corr = np.nan
            if strat_1 != strat_2:
                year = max(mtm_info_1[1], mtm_info_2[1])
                mtm_1 = mtm_info_1[0].loc[mtm_info_1[0].index.year >= year]
                mtm_2 = mtm_info_2[0].loc[mtm_info_2[0].index.year >= year]
                corr = mtm_1.corr(mtm_2, method="spearman")
            corr_index.loc[strat_1, strat_2] = [round(corr, 2), "pending"]
    return corr_index, error_strategy


def get_correlation_live_strats(
    strategy_list: List[List[str]],
    submitted_strategy_list: List[List[str]],
    identifier: str,
) -> Tuple[dict, List[str]]:
    """Finds correlation of each strategy in strategy list with all submitted strategies.

    Args:
        strategy_list (List[List[str]]): A list of list. Each list contains 2 elements, pending strategy name and year.
        submitted_strategy_list (List[List[str]]): A list of list. Each list contains 2 elements, strategy name and year.
        identifier (str): Identifier to append in correlation results

    Returns:
        Tuple[dict, List[str]]: A tuple where first element is dictionary of correlation and
                                second is a list of strategy which are not present on minio
    """
    old_mtm_dict = {}
    missing_strats = []
    for strategy in submitted_strategy_list:
        try:
            old_mtm_dict[strategy[0]] = [
                get_mtm_from_minio(
                    strategy_name=strategy[0],
                    status=identifier,
                ),
                strategy[1],
            ]
        except Exception as e:
            current_app.logger.warning(
                f"Skipping correlation live strats for {strategy[0]} due to error {e}"
            )
            missing_strats.append(strategy[0])

    if len(old_mtm_dict) == 0:
        return {}, missing_strats

    corr_dict = {}
    for strat in strategy_list:
        try:
            corr_dict[strat[0]] = {}
            new_strat_mtm = get_mtm_from_minio(strategy_name=strat[0], status="pending")
            for old_strat, old_strat_mtm_info in old_mtm_dict.items():
                year = max(old_strat_mtm_info[1], strat[1])
                new_strat_mtm_temp = new_strat_mtm.loc[new_strat_mtm.index.year >= year]
                old_strat_mtm = old_strat_mtm_info[0].loc[
                    old_strat_mtm_info[0].index.year >= year
                ]
                corr_dict[strat[0]][old_strat] = [
                    round(new_strat_mtm_temp.corr(old_strat_mtm, method="spearman"), 2),
                    identifier,
                ]
        except Exception as e:
            current_app.logger.warning(
                f"Skipping correlation pending strats for {strat[0]} due to error {e}"
            )
            missing_strats.append(strat[0])

    return corr_dict, missing_strats


def _mask_correlation_df(corr_df: pd.DataFrame) -> pd.DataFrame:
    """Filter out given correlation dataframe based on certain thresholds present in configs

    Args:
        corr_df (pd.DataFrame): Dataframe to be filtered

    Returns:
        pd.DataFrame: Filtered dataframe
    """
    if len(corr_df) == 0:
        return corr_df
    mask = (corr_df["union_corr"] > current_app.config["UNION_CORR_TH"]) | (
        (corr_df["pnl_dependence"] > current_app.config["PNL_DEPENDENCE_TH"])
        & (corr_df["intersection_corr"] > current_app.config["INTERSECTION_CORR_TH"])
    )
    corr_df = corr_df[mask]
    corr_df["start_date"] = corr_df["start_date"].dt.strftime("%Y-%m-%d")
    return corr_df


def _calculate_correlation(
    base_tl: str, tl_minio: str, start_date: pd.Timestamp
) -> Dict[str, Any]:
    """Find the new correlation between passed tradelogs.

    Args:
        base_tl (str): Base strategy tradelog
        tl_minio (str): Referenced strategy tradelog
        start_date (pd.Timestamp): Start date after which tradelog to be considered

    Returns:
        Dict[str, Any]: A dictionary with different correlation metrics
    """
    tl_minio = tl_minio[tl_minio.entry_date > start_date]
    base_tl_temp = base_tl[base_tl.entry_date > start_date]
    base_tl_mtm = base_tl_temp.groupby("entry_date").pnl.sum()

    # Calculate pnl, trade dependence
    common_days_trade = base_tl_temp[base_tl_temp.entry_date.isin(tl_minio.entry_date)]
    pnl_dependence = common_days_trade.pnl.sum() / base_tl_temp.pnl.sum()
    trade_depedence = len(common_days_trade) / len(base_tl_temp)

    tl_mtm = tl_minio.groupby("entry_date").pnl.sum()

    # Get interesection correlation
    common_indexes = base_tl_mtm.index.intersection(tl_mtm.index)
    common_mtm_base = base_tl_mtm.loc[common_indexes]
    common_mtm_tl = tl_mtm.loc[common_indexes]
    intersection_corr = common_mtm_base.corr(common_mtm_tl, method="spearman")

    # Get union correlation
    combine_index = base_tl_mtm.index.union(tl_mtm.index)
    reindexed_base_tl_mtm = base_tl_mtm.reindex(combine_index).fillna(0)
    tl_mtm = tl_mtm.reindex(combine_index).fillna(0)
    union_corr = reindexed_base_tl_mtm.corr(tl_mtm, method="spearman")

    return {
        "pnl_dependence": round(pnl_dependence, 3),
        "trade_dependence": round(trade_depedence, 3),
        "union_corr": round(union_corr, 3),
        "intersection_corr": round(intersection_corr, 3),
        "start_date": start_date,
    }


def get_fresh_correlation(
    base_strategy: str,
    base_strategy_start_date: pd.Timestamp,
    strategy_infos: List[Tuple[str, dt.date]],
    refersh_live: bool = False,
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """Find different correlation metrics of given base strategy with all strategies passed in strategy infos.
        If refesh_live flag is set then it checks if any live strategy was missed during backtest service and updates it's result on minio

    Args:
        base_strategy (str): Base strategy name
        base_strategy_start_date (pd.Timestamp): Base strategy start date
        strategy_infos (List[Tuple[str, dt.date]]): A list of strategy and their backtest start date.
        refresh_live (bool): For refreshing correlation results with live strats on minio

    Returns:
        pd.DataFrame: A dataframe containing correlation info
    """

    # fetch the base_tradelog
    base_tl = pd.read_parquet(
        BytesIO(
            get_files_from_minio(
                status="pending",
                strategy_name=base_strategy,
                post_fix="_Tradelog.parquet",
            )
        ),
    )
    base_tl["entry_date"] = base_tl.entry_timestamp.dt.normalize()
    base_tl["entry_date"] = pd.to_datetime(base_tl["entry_date"])

    if refersh_live:
        # fetch the list of already correlated live strategies
        correlated_tl = pd.read_parquet(
            BytesIO(
                get_files_from_minio(
                    status="pending",
                    strategy_name=base_strategy,
                    post_fix="_correlation_info.parquet",
                )
            ),
        )
        already_correlated_tl = set(correlated_tl.index)

        # fetch the list of already reverse correlated live strategies
        reverse_correlated_tl = pd.read_parquet(
            BytesIO(
                get_files_from_minio(
                    status="pending",
                    strategy_name=base_strategy,
                    post_fix="_reverse_correlation_info.parquet",
                )
            ),
        )

    correlation_info = {}
    reverse_correlation_info = {}
    # find correlation of each strategy with base strategy and vice versa
    for strategy in strategy_infos:
        try:
            strat = strategy[0]
            if "cluster" in strat:
                continue
            # skip the ones already correlated, only if refershing_live
            if refersh_live and (strat in already_correlated_tl):
                continue
            start_date = max(base_strategy_start_date, strategy[1])
            status = "live" if refersh_live else "pending"
            data = get_files_from_minio(
                status=status, strategy_name=strat, post_fix="_Tradelog.parquet"
            )
            tl_minio = pd.read_parquet(BytesIO(data))
            tl_minio["entry_date"] = tl_minio.entry_timestamp.dt.normalize()
            tl_minio["entry_date"] = pd.to_datetime(tl_minio.entry_date)
            correlation_info[strat] = _calculate_correlation(
                base_tl.copy(), tl_minio.copy(), start_date
            )
            reverse_correlation_info[strat] = _calculate_correlation(
                tl_minio.copy(), base_tl.copy(), start_date
            )

        except Exception as e:
            current_app.logger.warning(
                f"Skipping correlation pending strats for {strat} due to error {e}"
            )

    if refersh_live:
        correlation_info_df = pd.concat(
            [pd.DataFrame(correlation_info).T, correlated_tl]
        )
        reverse_correlation_info_df = pd.concat(
            [pd.DataFrame(reverse_correlation_info).T, reverse_correlated_tl]
        )
        upload_dataframe_to_minio(
            f"pending_strats/{base_strategy}/{base_strategy}_correlation_info.parquet",
            correlation_info_df,
            True,
        )
        upload_dataframe_to_minio(
            f"pending_strats/{base_strategy}/{base_strategy}_reverse_correlation_info.parquet",
            reverse_correlation_info_df,
            True,
        )
        return (
            _mask_correlation_df(correlation_info_df),
            _mask_correlation_df(reverse_correlation_info_df),
        )
    else:
        return (
            _mask_correlation_df(pd.DataFrame(correlation_info).T),
            _mask_correlation_df(pd.DataFrame(reverse_correlation_info).T),
        )


def copy_objects_minio(
    strategy_name: str,
    source_path: str,
    dest_path: str,
    excluded_suffix: List[str] = None,
):
    """Copies data of given strategy from source path to dest path on minio.

    Args:
        strategy_name (str): Name of the strategy
        source_path (str): Base source path on minio
        dest_path (str): Base destination path on minio
        excluded_suffix (List[str], optional): Exclude objects with these suffix while copying. Defaults to None.
    """
    if excluded_suffix is None:
        excluded_suffix = []

    objects_to_be_copied = minio.client.list_objects(
        current_app.config["DASHBOARD_DATA_BUCKET"],
        prefix=source_path + "/" + strategy_name + "/",
        recursive=True,
    )

    object_source_dest_name = {}
    for objects in objects_to_be_copied:
        to_be_copied = True
        for suffix in excluded_suffix:
            if objects.object_name.endswith(suffix):
                to_be_copied = False
                break
        if to_be_copied:
            object_source_dest_name[objects.object_name] = objects.object_name.replace(
                source_path, dest_path
            )

    for key, value in object_source_dest_name.items():
        minio.client.copy_object(
            current_app.config["DASHBOARD_DATA_BUCKET"],
            value,
            CopySource(current_app.config["DASHBOARD_DATA_BUCKET"], key),
        )


def transfer_to_file_server(
    df: Union[pd.DataFrame, pd.Series], path_to_upload: str, header: bool = True
):
    """Transfers passed pandas object to file server at given path.

    Args:
        df (Union[pd.DataFrame, pd.Series]): Pandas object to upload
        path_to_upload (str): Path at server with server address
        header (bool): Set it to false if no header is present in dataframe
    """
    temp_file = tempfile.NamedTemporaryFile()
    if header is False:
        df.to_csv(temp_file.name, index=False, header=None)
    else:
        df.to_csv(temp_file.name, index=False)
    temp_file.seek(0)
    os.system(f"curl -T {temp_file.name} {path_to_upload}")
    temp_file.close()


def remove_strategy_from_limit_order_files(strategy: Strategy) -> None:
    """Removes a given strategy from the limit order files of all its associated clusters.

    Args:
        strategy (Strategy): Strategy object
    """
    if strategy.cluster_mapping is not None:
        clusters = list({cluster.cluster_name for cluster in strategy.cluster_mapping})
        for cluster in clusters:
            try:
                curr_coeff = pd.read_csv(
                    f"{current_app.config['FILE_SERVER']}/{cluster}_limit_order"
                )
                updated_coeff = curr_coeff[
                    curr_coeff.strategy != strategy.strategy_name
                ].reset_index(drop=True)
                transfer_to_file_server(
                    updated_coeff,
                    f"{current_app.config['FILE_SERVER']}/{cluster}_limit_order",
                )
            except Exception as e:
                current_app.logger.warning(
                    f"Limit order file not updated for {cluster} due to error {e}"
                )


def kill_strategy(strategy: Strategy) -> Tuple[List[str], str]:
    """Process for making a live strategy dead.

    Args:
        strategy (Strategy): Strategy object

    Returns:
        Tuple(List[str], str): A list of cluster names from which strategy is removed and teams msg
    """
    dead_state = Status.query.filter_by(state_description="DEAD").first().id
    if strategy.strat_state == dead_state:
        msg_for_teams = f"{strategy.strategy_name} is already dead"
        return [], msg_for_teams
    strategy.strat_state = dead_state
    strategy.last_run_day = pd.Timestamp.now().normalize()
    clusters_list = []
    empty_cluster_list = []
    for cluster in strategy.cluster_mapping:
        if cluster.mapping_status == ClusterMappingStatus.get_status(
            description="LIVE_ENV"
        ):
            current_app.logger.info(
                f"Removing {strategy.strategy_name} from {cluster.cluster_name}"
            )
            clusters_list.append(cluster.cluster_name)
            strategy_cluster_mapping = StrategyClusterMapping.query.filter_by(
                cluster_name=cluster.cluster_name,
                mapping_status=ClusterMappingStatus.get_status(description="LIVE_ENV"),
            ).all()
            remaining_slave_of_cluster = 0
            for mapping in strategy_cluster_mapping:
                if mapping.strategy.status.state_description == "LIVE":
                    remaining_slave_of_cluster += 1
            if remaining_slave_of_cluster == 0:
                empty_cluster_list.append(cluster.cluster_name)

    if strategy.segment == "CASH":
        remove_strategy_from_limit_order_files(strategy=strategy)
    msg_for_teams = f"{strategy.strategy_name} is removed from live"
    if len(empty_cluster_list) > 0:
        send_message_to_kafka(
            f"Following cluster are now empty in live mode: {empty_cluster_list}"
        )
    return clusters_list, msg_for_teams


def post_acceptance_process(strategy: Strategy, mode: str) -> Tuple[List[str], str]:
    """Completes any process required for pushing a strategy to the given mode.

    Args:
        strategy (Strategy): Strategy object.
        mode (str): mdoe into which given strategy needs to be accepted.

    Returns:
        Tuple[List[str], str]: A tuple consisting of the cluster list (from which the old strategy was removed) and the old strategy.
    """
    cluster_list = None
    strategy_reworked = None
    if strategy.reworked_strategy not in [None, ""]:
        strategy_reworked = Strategy.query.get_or_404(strategy.reworked_strategy)
        if mode == "live":
            cluster_list, msg_to_send = kill_strategy(strategy=strategy_reworked)
        else:
            msg_to_send = f"New Rework Strategy: {strategy.strategy_name} being pushed to test. Original strategy: {strategy_reworked.strategy_name} not killed in live."
        send_message_to_kafka(text=msg_to_send)

    if strategy.segment == "CASH" and strategy.status.state_description == "PENDING":
        if strategy.cluster_mapping is not None:
            clusters = list(
                {cluster.cluster_name for cluster in strategy.cluster_mapping}
            )
            for cluster in clusters:
                try:
                    curr_coeff = pd.read_csv(
                        f"{current_app.config['FILE_SERVER']}/{cluster}_limit_order"
                    )
                except EmptyDataError:
                    curr_coeff = pd.DataFrame()
                new_entry = {
                    "strategy": [strategy.strategy_name],
                    "trigger_coeff": [float(strategy.trigger_coeff)],
                    "limit_coeff": [float(strategy.limit_coeff)],
                    "expiration_time": [strategy.expiration_time.strftime("%H:%M")],
                }
                new_entry = pd.DataFrame(new_entry)
                updated_coeff = pd.concat([curr_coeff, new_entry], ignore_index=True)
                transfer_to_file_server(
                    updated_coeff,
                    f"{current_app.config['FILE_SERVER']}/{cluster}_limit_order",
                )
    if mode == "live":
        move_from_folder = current_app.config["STRAT_STATUS_PATH_MAPPING"][
            strategy.status.state_description.lower()
        ]
        copy_objects_minio(
            strategy.strategy_name,
            move_from_folder,
            current_app.config["STRAT_STATUS_PATH_MAPPING"]["live"],
            excluded_suffix=["ipynb", "py", "pyce", "key"],
        )
        delete_from_minio(strategy.strategy_name, move_from_folder)
    if strategy_reworked is not None:
        strategy_reworked = strategy_reworked.strategy_name
    return cluster_list, strategy_reworked


def get_all_clusters_in_hierarchy(strategy: Strategy) -> Set[Strategy]:
    """Use BFS to get a list of all clusters related to the given strategy in its LIVE cluster mapping.
    Args:
        strategy (Strategy): Strategy for which clusters to be find

    Returns:
        Set[Strategy]: Set of all related clusters
    """
    related_clusters = set()
    visited_clusters = set()
    queue = [strategy]
    visited_clusters.add(strategy)
    while queue:
        current_strategy = queue.pop(0)  # Dequeue the first element
        if current_strategy.cluster_mapping is not None:
            for cluster in current_strategy.cluster_mapping:
                if cluster.mapping_status == ClusterMappingStatus.get_status(
                    description="LIVE_ENV"
                ):
                    cluster_name = cluster.cluster_name
                    if cluster_name not in visited_clusters:
                        cluster_strat = Strategy.query.get_or_404(cluster_name)
                        visited_clusters.add(cluster_strat)
                        related_clusters.add(cluster_strat)
                        queue.append(cluster_strat)
    return related_clusters


def update_cluster_parameters(strategy: Strategy) -> None:
    """Update overlap days for all clusters in the hierarchy.
    Args:
        strategy (Strategy): strategy object
    """
    current_app.logger.info(
        f"Updating overlap days for all clusters of {strategy.strategy_name}"
    )
    related_clusters = get_all_clusters_in_hierarchy(strategy)
    for cluster_strat in related_clusters:
        cluster_strat.overlap_days = strategy.overlap_days
    db.session.commit()


def validate_download_request(
    private_key_file: FileStorage, strategy_name: str, status: str
) -> tuple:
    """Used to validate strategy download request by a user.

    Args:
        private_key_file (FileStorage): private key file provided by user.
        strategy_name (str): Name of the strategy.
        status (str): Status of the strategy.

    Returns:
        tuple: A tuple consisting of the key to decrypt files and a boolean value indicating if the request was a valid one
    """
    try:
        private_key = private_key_file.read()
        private_key_file.seek(0)
        private_key = serialization.load_pem_private_key(
            private_key, None, backend=default_backend()
        )
        key = get_files_from_minio(
            status=status, strategy_name=strategy_name, post_fix="_key"
        )
        key_to_decrypt_files = private_key.decrypt(
            key,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None,
            ),
        )
        key_to_decrypt_files = Fernet(key_to_decrypt_files)
        return key_to_decrypt_files, True
    except Exception as e:
        current_app.logger.warning(
            f"Validating download request failed for {strategy_name} due to error {e}"
        )
        return e, False


def check_authenticity(strategy_list: list, state_description: str = "PENDING") -> bool:
    """Checks if current user can access given list of strategies.

    Args:
        strategy_list (list): A list of strategy names
        state_description (str, optional): Status of the strategies. Defaults to "PENDING".

    Returns:
        bool: Authenticity of the request
    """
    if current_user.role.name == "DEVELOPER":
        return False
    strats = (
        Status.query.filter_by(state_description=state_description)
        .first()
        .strategies.all()
    )
    if current_user.role.name == "MANAGER":
        user_list = [user.username for user in current_user.developers]
        user_list.extend([current_user.username])
        strats = [strat for strat in strats if strat.developer in user_list]
    strats_list = [strat.strategy_name for strat in strats]
    for strat in strategy_list:
        if strat not in strats_list:
            return False
    return True


def check_cluster_authenticity(cluster_list: list) -> bool:
    """Checks if cluster exists in list of clusters.

    Args:
        cluster_list (list): A list of cluster names

    Returns:
        bool: Authenticity of the request
    """
    available_clusters = [
        strategy.strategy_name
        for strategy in Strategy.query.filter(
            Strategy.strategy_name.contains("cluster")
        ).all()
    ]
    for strat in cluster_list:
        if strat not in available_clusters:
            return False
    return True


def check_access(strategy_list: list) -> bool:
    strats_accessible = [
        access.strategy.strategy_name
        for access in StrategyAccess.query.filter_by(
            username=current_user.username
        ).all()
    ]
    for strat in strategy_list:
        if strat not in strats_accessible:
            return False
    return True


def accept_strategy(
    strategy: str,
    mode: str = "live",
    start_date: dt.date = dt.datetime.now().date(),
) -> Tuple[bool, dict]:
    """Process for accepting a strategy to a given mode.

    Args:
        strategy (str): Name of the strategy
        mode (str, optional): Mode into which given strategy needs to be accepted. Defaults to "live".
        start_date (dt.date, optional): live start day. Defaults to dt.datetime.now().date().

    Returns:
        Tuple[bool, dict]: A tuple indicating the success of the process, along with the cluster list
                        (from which the old strategy was removed) and old strategy which is made dead.
    """
    mode = mode.lower()
    is_authorized = check_authenticity(
        strategy_list=[strategy], state_description="PENDING"
    ) or check_authenticity(strategy_list=[strategy], state_description="TEST")
    strategy = Strategy.query.filter_by(strategy_name=strategy).first()
    if (
        (is_authorized is False)
        or (mode not in ["live", "test"])
        or (strategy is None)
        or (mode == "test" and strategy.status.state_description == "TEST")
    ):
        return False, {}
    strats_info = {}
    msg_to_send = f"Following strategy is accepted into {mode} mode: "
    try:
        cluster_list, strategy_reworked = post_acceptance_process(
            strategy=strategy, mode=mode
        )
        strats_info["cluster_list"] = cluster_list
        strats_info["reworked_strategy"] = strategy_reworked
        msg_to_send += strategy.strategy_name
    except Exception as e:
        current_app.logger.warning(
            f"Failed to accept strategy {strategy.strategy_name} due to error {e}"
        )
        return False, {}
    if not start_date:
        start_date = dt.datetime.now().date()
    if mode == "live":
        strategy.live_start_day = start_date
        strategy.strat_state = (
            Status.query.filter_by(state_description="LIVE").first().id
        )
        update_cluster_parameters(strategy)
    else:
        strategy.strat_state = (
            Status.query.filter_by(state_description="TEST").first().id
        )
    send_message_to_kafka(msg_to_send)
    return True, strats_info


def reject_strategy(strategy: str) -> bool:
    """Process for rejecting a pending/test strategy.

    Args:
        strategy (str): Name of the strategy
        mode (str): status of the strategy being rejected

    Returns:
        bool: Indicates if the process was successful or not
    """
    is_authorized = check_authenticity(
        strategy_list=[strategy], state_description="PENDING"
    ) or check_authenticity(strategy_list=[strategy], state_description="TEST")
    if is_authorized is False:
        return False
    msg_to_send = "Following strategy is rejected after review: "
    strategy = Strategy.query.filter_by(strategy_name=strategy).first()
    try:
        move_from_folder = current_app.config["STRAT_STATUS_PATH_MAPPING"][
            strategy.status.state_description.lower()
        ]
        copy_objects_minio(
            strategy.strategy_name,
            move_from_folder,
            current_app.config["STRAT_STATUS_PATH_MAPPING"]["rejected"],
        )
        delete_from_minio(strategy.strategy_name, move_from_folder)
        msg_to_send += strategy.strategy_name
    except Exception as e:
        current_app.logger.warning(
            f"Failed to reject strategy {strategy.strategy_name} due to error {e}"
        )
        return False
    if strategy.segment == "CASH" and strategy.status.state_description == "TEST":
        remove_strategy_from_limit_order_files(strategy=strategy)
    strategy.strat_state = (
        Status.query.filter_by(state_description="REJECTED").first().id
    )
    db.session.commit()
    send_message_to_kafka(msg_to_send)
    return True


def dfs(node: int, visited: List[bool], service_states: str) -> str:
    """Apply dfs on backend service graph starting from given node
        and set each nodes state as pending

    Args:
        node (int): Index of current node
        visited (_type_): A boolean list of visited nodes
        service_states (str): Current state of services

    Returns:
        str: New state of service after applying dfs on given node
    """
    visited[node] = True

    # Set the node to pending state otherwise
    service_states = service_states[0:node] + "0" + service_states[node + 1 :]
    if node not in current_app.config["SERVICE_DOWNSTREAMS"]:
        return service_states

    for x in current_app.config["SERVICE_DOWNSTREAMS"][node]:
        if visited[x] is False and service_states[x] != "3":
            service_states = dfs(x, visited, service_states)
    return service_states


def start_downstream_service(service_index: int, service_states: str) -> str:
    """Set state of all downstream services of given service as pending

    Args:
        service_index (int): Index of service
        service_states (str): Current state of all services

    Returns:
        str: New state of services after starting
    """
    visited = [False] * len(service_states)
    service_states = dfs(service_index, visited, service_states)
    return service_states


def clean_review(review: StrategyReview) -> StrategyReview:
    """Function to clean strategy review before uploading

    Args:
        review (StrategyReview): strategy review

    Returns:
        StrategyReview: cleaned strategy review
    """
    cols_to_check = [
        "timecheck",
        "correlation_check",
        "trade_distribution_check",
        "risk_analysis",
        "num_days_trading",
        "comments",
        "to_change",
        "to_do",
    ]
    for col in cols_to_check:
        if eval(f"review.{col}") is None:
            exec(f"review.{col} = ''")
    return review


def is_all_empty_review(review: StrategyReview) -> bool:
    """Function to check if the entire review is empty.

    Args:
        review (StrategyReview): strategy review

    Returns:
        bool: indicating if the review is empty or not
    """
    cols_to_check = [
        "timecheck",
        "correlation_check",
        "trade_distribution_check",
        "risk_analysis",
        "num_days_trading",
        "comments",
        "to_change",
        "to_do",
    ]
    for col in cols_to_check:
        if eval(f"review.{col}") != "":
            return False
    return True


def check_strategy_for_invalid_filters(strat_file: FileStorage) -> List[str]:
    """Check whether invalid filters are present in strategy file

    Args:
        strat_file (FileStorage): Python File to checked
    Returns:
        List[str]: List of invalid filters present in the file
    """
    invalid_filters = [
        "option_filter",
        "stock_option_filter",
        "optcur_filter",
        "optcom_filter",
        "set_trace",
        "columns_to_exclude",
        "finally",
    ]
    strat_file_string = strat_file.read().decode("utf-8")
    filters_present = []
    for filter in invalid_filters:
        if filter in strat_file_string:
            filters_present.append(filter)
    if strat_file_string.count("get_async_trades") > 1:
        filters_present.append("Multiple calls to get_async_trades")
    if "except" in strat_file_string:
        if strat_file_string.count("except") > 1:
            filters_present.append("Multiple try except blocks")
        if "except KeyError" not in strat_file_string:
            filters_present.append("Only KeyError Exception is allowed")

    strat_file.seek(0)
    return filters_present
