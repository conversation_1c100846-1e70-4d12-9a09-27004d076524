from flask import flash
from flask import current_app
import pandas as pd
from app.models import minio
from io import BytesIO
from typing import Dict, Optional, <PERSON><PERSON>


def fetch_risk_limits() -> <PERSON><PERSON>[pd.DataFrame, Optional[str]]:
    """Fetches limits CSV file from MinIO and converts it to a pandas DataFrame.

    Returns:
        Tuple[pd.DataFrame, Optional[str]]:
            - DataFrame: DataFrame containing the limits uploaded to MinIO.
            - str or None: Error message if an error occurred, otherwise None.
    """
    try:
        data = minio.get_object(
            current_app.config["DASHBOARD_DATA_BUCKET"],
            "risk_management/RiskLimits.csv",
        ).data
        df = pd.read_csv(
            BytesIO(data), header=None, names=["variable_name", "variable_value"]
        )
        return (df, None)
    except Exception as e:
        current_app.logger.error(f"Error fetching limits: {e}")
        return (
            pd.DataFrame(columns=["variable_name", "variable_value"]),
            "Failed to fetch risk limits.",
        )


def update_risk_limits(limits_dict: Dict[str, int]) -> Optional[str]:
    """Updates the risk limits CSV on MinIO with new limits.

    Args:
        limits_dict (Dict[str, int]): Dictionary containing limit name to limit value mapping.

    Returns:
        Optional[str]: Error message if an error occurred, otherwise None.
    """
    try:
        limits_df = pd.DataFrame(
            limits_dict.items(), columns=["variable_name", "variable_value"]
        )
        csv_data = limits_df.to_csv(index=False, header=False).encode("utf-8")
        minio.put_object(
            current_app.config["DASHBOARD_DATA_BUCKET"],
            "risk_management/RiskLimits.csv",
            data=BytesIO(csv_data),
            length=len(csv_data),
        )
        flash("Limits updated successfully.", "success")
        return None
    except Exception as e:
        current_app.logger.error(f"Error updating limits: {e}")
        return "Failed to update risk limits."
