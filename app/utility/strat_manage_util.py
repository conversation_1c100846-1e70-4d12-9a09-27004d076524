import io
import os
import tempfile
import py_compile
from typing import Any, List
from app.models import minio
from flask import current_app
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.fernet import Fernet
from werkzeug.datastructures import FileStorage
from pyce import encrypt_path
from tempfile import SpooledTemporaryFile
import subprocess
import ast


def generate_pyce_in_other_env(python_file_bytes: bytes, strategy_name: str):
    """Run pyce generation and upload to minio using specific conda environment.
    Args:
        python_file_bytes (bytes): Python file in bytes
        strategy_name (str): Name of the strategy whose files to be uploaded
    """
    current_file_dir = os.path.dirname(os.path.abspath(__file__))
    conda_env_name = "dashboard_env_python311"
    script_path = f"{current_file_dir}/generate_pyce_new_env.py"
    base_command = ["conda", "run", "-n", conda_env_name, "python", script_path]
    temp_python_file = tempfile.NamedTemporaryFile(suffix=".py")
    temp_python_file.write(python_file_bytes)
    temp_python_file.seek(0)

    args_to_pass = [temp_python_file.name]
    full_command = base_command + args_to_pass

    try:
        # Run the subprocess
        result = subprocess.run(
            full_command,
            capture_output=True,
        )
    except subprocess.CalledProcessError as e:
        current_app.logger.error(f"Error running Pyce Generation script: {e}")
        return
    finally:
        temp_python_file.close()

    try:
        result = result.stdout.decode("utf-8").split("\n")
        pyce_path = result[0]
        pyce_key = ast.literal_eval(result[1])
        pyce_filename = f"pending_strats/{strategy_name}/{strategy_name}_python311.pyce"

        minio.client.fput_object(
            current_app.config["DASHBOARD_DATA_BUCKET"],
            pyce_filename,
            pyce_path,
        )

        os.remove(pyce_path)
        upload_bytes_to_minio(pyce_key, strategy_name, "_python311_pyce_key")
    except Exception as e:
        current_app.logger.error(f"Error uploading pyce file and key to minio: {e}")


def upload_bytes_to_minio(
    bytes_to_upload: bytes, strategy_name: str, suffix: str = "", key: Fernet = None
):
    """Upload the given bytes to minio at pending strat path and given suffix.
    If an encryption key is passed, encrypt the bytes before uploading

    Args:
        bytes_to_upload (bytes): Bytes to upload
        strategy_name (str): Name of the strategy
        suffix (str, optional): Suffix to add to the path on minio. Default to "".
        key (Fernet): Encryption key if bytes need to be encrypted. Defaults to None.
    """
    filename = "pending_strats/" + strategy_name + "/" + strategy_name + suffix
    encrypted_bytes = bytes_to_upload
    if key is not None:
        encrypted_bytes = key.encrypt(bytes_to_upload)
    minio.client.put_object(
        current_app.config["DASHBOARD_DATA_BUCKET"],
        filename,
        io.BytesIO(encrypted_bytes),
        len(encrypted_bytes),
    )


def upload_to_minio(
    strat_python_file: FileStorage, strat_ipython_file: FileStorage, strategy_name: str
):
    """Gets encryption key from minio and use that to encrypt passed python and ipython file.
        After that uploads the encrypted files to minio.

    Args:
        strat_python_file (FileStorage): Python file to be uploaded to minio
        strat_ipython_file (FileStorage): IPython file to be uploaded to minio
        strategy_name (str): Name of the strategy whose files to be uploaded
    """
    # Generate a symmetric key for the strategy
    current_app.logger.info(f"Uploading files for strategy {strategy_name}")
    session_key = Fernet.generate_key()
    key_value = Fernet(session_key)

    # Encrypt python file with the generated key and upload it to MiniO
    python_file_bytes = strat_python_file.read()
    upload_bytes_to_minio(python_file_bytes, strategy_name, "_py", key=key_value)

    # Encrypt ipython file with the generated key and upload it to MiniO
    ipython_file_bytes = strat_ipython_file.read()
    upload_bytes_to_minio(ipython_file_bytes, strategy_name, "_ipynb", key=key_value)

    generate_pyce_in_other_env(
        python_file_bytes=python_file_bytes, strategy_name=strategy_name
    )

    # Create compiled bytecode of uploaded python file as a temporary file
    temp_python_file = tempfile.NamedTemporaryFile(suffix=".py")
    temp_python_file.write(python_file_bytes)
    temp_python_file.seek(0)
    temp_pyc_file = tempfile.NamedTemporaryFile(suffix=".pyc", delete=False)
    py_compile.compile(temp_python_file.name, temp_pyc_file.name)
    temp_python_file.close()
    temp_pyc_file.seek(0)

    # Generate pyce file from the bytecode created and upload it to minio
    result = encrypt_path(temp_pyc_file.name)
    pyce_filename = "pending_strats/" + strategy_name + "/" + strategy_name + ".pyce"
    minio.client.fput_object(
        current_app.config["DASHBOARD_DATA_BUCKET"],
        pyce_filename,
        result[0][0],
    )
    os.remove(result[0][0])

    # Upload pyce key to minio as well
    pyce_key = str.encode(result[0][1])
    upload_bytes_to_minio(pyce_key, strategy_name, "_pyce_key")

    # Encrypt the generated session key with public RSA key and upload that to MiniO
    public_key = minio.client.get_object(
        current_app.config["DASHBOARD_DATA_BUCKET"], "support_files/public_key.pem"
    ).data
    public_key = serialization.load_pem_public_key(
        public_key, backend=default_backend()
    )
    encrypted_session_key = public_key.encrypt(
        session_key,
        padding.OAEP(
            mgf=padding.MGF1(algorithm=hashes.SHA256()),
            algorithm=hashes.SHA256(),
            label=None,
        ),
    )
    upload_bytes_to_minio(encrypted_session_key, strategy_name, "_key")


def delete_from_minio(strategy_name: str, object_path: str):
    """Delete all information related to this strategy from minio pending strats.

    Args:
        strategy_name (str): Name of the strategy whose files to be deleted
        object_path (str): Base path of strategy on minio from where you want to delete this
    """
    objects_to_be_deleted = minio.client.list_objects(
        current_app.config["DASHBOARD_DATA_BUCKET"],
        prefix=object_path + "/" + strategy_name + "/",
        recursive=True,
    )
    objects_to_be_deleted = [x.object_name for x in objects_to_be_deleted]
    current_app.logger.info(
        f"Deleting items {objects_to_be_deleted} for strategy {strategy_name}"
    )
    for objects in objects_to_be_deleted:
        minio.client.remove_object(current_app.config["DASHBOARD_DATA_BUCKET"], objects)


def delete_file_from_minio(path: str) -> None:
    """Delete file at minio located at 'path'

    Args:
        path (str): location of the file
    """
    objects_to_be_deleted = minio.client.list_objects(
        current_app.config["DASHBOARD_DATA_BUCKET"],
        prefix=path,
        recursive=True,
    )
    objects_to_be_deleted = [x.object_name for x in objects_to_be_deleted]
    current_app.logger.info(f"Deleting items {objects_to_be_deleted} from minio")
    for object in objects_to_be_deleted:
        minio.client.remove_object(current_app.config["DASHBOARD_DATA_BUCKET"], object)


def update_content_on_minio(path: str, content: str):
    """This function updates the content at given path on minio

    Args:
        path (str): path where content needs to be updated
        content (Any): content to be updated

    """
    content = content.encode("utf-8")
    minio.client.put_object(
        current_app.config["DASHBOARD_DATA_BUCKET"],
        path,
        io.BytesIO(content),
        len(content),
    )


def upload_html_file_on_minio(path: str, file: SpooledTemporaryFile):
    """upload html file to minio at given path

    Args:
        path: path where html file will be uploaded
        file ; html file
    """
    file.seek(0, os.SEEK_END)
    length = file.tell()
    file.seek(0)
    minio.client.put_object(
        current_app.config["DASHBOARD_DATA_BUCKET"],
        path,
        file,
        length=length,
        content_type="text/html",
    )


def add_deleted_tradelogs_on_minio(
    strategies_to_add: List[str], accessible_strategies: List[str]
):
    """Adds given strategy names to the list of strategy tradelogs to be deleted on minio.

    Args:
        strategies_to_add (List[str]): List of strategy names to be added to the "delete_tradelogs_for_sentinel" file
        accessible_strategies (List[str]): Subset of strategies which can be modified by the current user
    """
    bucket_name = current_app.config["DASHBOARD_DATA_BUCKET"]
    object_name = "submitted_requests/delete_tradelogs_for_sentinel.txt"
    response = minio.get_object(bucket_name, object_name)
    old_content = response.data.decode("utf-8")
    old_content = response.data.decode("utf-8")

    old_strategies = {line.strip() for line in old_content.splitlines() if line.strip()}
    old_strategies = old_strategies - set(accessible_strategies)
    new_strategies = set(strategy for strategy in strategies_to_add)
    updated_strategies = old_strategies.union(new_strategies)

    updated_content = "\n".join(sorted(updated_strategies))
    updated_content_bytes = updated_content.encode("utf-8")
    updated_content_file = io.BytesIO(updated_content_bytes)
    minio.put_object(
        bucket_name,
        object_name,
        updated_content_file,
        len(updated_content_bytes),
        content_type="text/plain",
    )


def fetch_deleted_tradelogs_from_minio(accessible_strategies: List[str]) -> List[str]:
    """Fetches current list of strategy tradelogs to be deleted from minio.

    Args:
        accessible_strategies (List[str]): list of strategies that a user have access to.

    Returns:
        List[str]: strategies which are present in the delete tradelog file and are accesible to the user
    """
    bucket_name = current_app.config["DASHBOARD_DATA_BUCKET"]
    object_name = "submitted_requests/delete_tradelogs_for_sentinel.txt"
    response = minio.get_object(bucket_name, object_name)
    old_content = response.data.decode("utf-8")
    old_strategies = list(
        {line.strip() for line in old_content.splitlines() if line.strip()}
    )
    accessible_old_strategies = [
        strat for strat in old_strategies if strat in accessible_strategies
    ]
    return accessible_old_strategies
