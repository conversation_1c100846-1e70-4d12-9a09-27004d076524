import py_compile
import tempfile
from pyce import encrypt_path
import sys


def generate_pyce(python_file_path: str):
    """Generates pyce

    Args:
        strategy_name (str): name of the strategy
        python_file_path (str): source python path
    """

    temp_pyc_file = tempfile.NamedTemporaryFile(suffix=".pyc", delete=False)
    py_compile.compile(python_file_path, temp_pyc_file.name)
    temp_pyc_file.seek(0)

    # Encryption process
    result = encrypt_path(temp_pyc_file.name)
    pyce_path = result[0][0]
    print(pyce_path)

    pyce_key = str.encode(result[0][1])
    print(pyce_key)
    return


# Script to generate pyce using new python3
if __name__ == "__main__":
    if len(sys.argv) > 1:
        python_file_path = sys.argv[1]
    else:
        raise Exception("Requied arguments are missing!!")
    generate_pyce(python_file_path)
