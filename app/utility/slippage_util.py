import plotly
import json
import plotly.express as px
import pandas as pd
import numpy as np
import datetime
from typing import Tuple, List, Dict


def generate_overall_analysis_dict(
    query_res: List[Tuple[int, float, float]]
) -> List[float]:
    """Takes clickhouse database query result as input and create a list containing 4 elements:
        overall slip, buy slip, sell slip, notional.

    Args:
        query_res (List[Tuple[int, float, float]]): Result from query on clickhouse database

    Returns:
        List[float]: Overall slippage list
    """
    segment_overall_slip = {}
    for item in query_res:
        segment_overall_slip[item[0]] = {
            "signal_slip": item[1],
            "notional": item[2],
        }

    # If buy or sell both are not in query result, return overall analysis list as nan
    if (1 not in segment_overall_slip) or (-1 not in segment_overall_slip):
        return [np.nan, np.nan, np.nan, np.nan]

    segment_overall_slip[0] = {
        "signal_slip": (
            segment_overall_slip[1]["signal_slip"] * segment_overall_slip[1]["notional"]
            + segment_overall_slip[-1]["signal_slip"]
            * segment_overall_slip[-1]["notional"]
        )
        / (segment_overall_slip[1]["notional"] + segment_overall_slip[-1]["notional"]),
        "notional": (
            segment_overall_slip[1]["notional"] + segment_overall_slip[-1]["notional"]
        ),
    }

    res_lis = [
        segment_overall_slip[0]["signal_slip"],
        segment_overall_slip[1]["signal_slip"],
        segment_overall_slip[-1]["signal_slip"],
        segment_overall_slip[0]["notional"],
    ]

    return res_lis


def generate_slip_graph(
    slippage_dict: Dict[datetime.date, List[float]]
) -> Tuple[str, str]:
    """Generates slippage and exposure graphs from slippage information both containing 2 lines, first is average daily slippage/exposure
        and second is rolling average slippage/exposure of last 20 days.
        Returns a json dump of generated graph.

    Args:
        slippage_dict (Dict[datetime.date,  List[float]]): A dictionary containing daily average slippage

    Returns:
        Tuple[str, str]: Json dump of the slippage and exposure graph
    """
    slip_df = pd.DataFrame.from_dict(slippage_dict, orient="index")
    slip_df = slip_df.sort_index()
    slip_df["slip_loss"] = slip_df[0] * slip_df[1]
    slip_df["rolling_loss"] = (
        slip_df["slip_loss"].rolling(window=20, min_periods=1).sum()
    )
    slip_df["rolling_exp_sum"] = slip_df[1].rolling(window=20, min_periods=1).sum()
    slip_df["20day-rolling-mean_exposure"] = (
        slip_df[1].rolling(window=20, min_periods=1).mean()
    )
    slip_df["20day-rolling-mean_slippage"] = (
        slip_df.rolling_loss / slip_df.rolling_exp_sum
    )
    slip_df = slip_df.reset_index()
    slip_df = slip_df.rename(
        columns={0: "day_slippage", 1: "day_exposure", "index": "entry_date"}
    )
    slip_df = slip_df[
        [
            "entry_date",
            "day_slippage",
            "20day-rolling-mean_slippage",
            "day_exposure",
            "20day-rolling-mean_exposure",
        ]
    ]
    fig = px.line(
        slip_df,
        x="entry_date",
        y=["day_slippage", "20day-rolling-mean_slippage"],
        title="Slippage (bps)",
    )
    fig2 = px.line(
        slip_df,
        x="entry_date",
        y=["day_exposure", "20day-rolling-mean_exposure"],
        title="Exposure (Cr.)",
    )
    graph_json_slip = json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)
    graph_json_exp = json.dumps(fig2, cls=plotly.utils.PlotlyJSONEncoder)
    return [graph_json_slip, graph_json_exp]


def generate_time_slip_graph(
    slippage_dict: Dict[datetime.date, List[float]], buy_sell: str
) -> Tuple[str, str]:
    """Generates a slippage and exposure graph at different time's of day
        Takes only top 20 timestamps by exposure.

    Args:
        slippage_dict (Dict[datetime.date, List[float]]): A dictionary containing average slippage at different timestamps of the day
        buy_sell (str): Buy or sell side slippage graph, used for title and coloring

    Returns:
        Tuple[str, str]: Json dump of the slippage and exposure graph
    """
    slip_df = pd.DataFrame.from_dict(slippage_dict, orient="index")
    slip_df = slip_df.sort_values(by=[1], ascending=False).head(20)
    slip_df = slip_df.sort_index()
    slip_df = slip_df.reset_index()
    slip_df = slip_df.rename(
        columns={0: "slippage", 1: "exposure", "index": "time_of_day"}
    )
    if buy_sell == "Buy":
        line_color = "green"
    else:
        line_color = "red"

    fig = px.line(
        slip_df,
        x="time_of_day",
        y=["slippage"],
        title=buy_sell + " Slippage(bps)",
        markers=True,
    )
    fig.update_traces(line_color=line_color)
    fig2 = px.line(
        slip_df,
        x="time_of_day",
        y=["exposure"],
        title=buy_sell + " Exposure (Cr)",
        markers=True,
    )
    fig2.update_traces(line_color=line_color)
    graph_json_slip = json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)
    graph_json_exp = json.dumps(fig2, cls=plotly.utils.PlotlyJSONEncoder)
    return [graph_json_slip, graph_json_exp]
