"""
TradeVision comparison utilities for Flask integration.
Converts TradeVision_v5.py functionality to Flask-compatible functions.
"""

import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
import json
import datetime
from typing import Dict, List, Tuple, Any
import plotly.utils


def format_number(x):
    """Format numbers for display."""
    try:
        x = float(x)
        if x == float('inf'):
            return "∞"
        elif x == float('-inf'):
            return "-∞"
        elif abs(x) >= 1e6:
            return f"{x/1e6:.1f}M"
        elif abs(x) >= 1e3:
            return f"{x/1e3:.1f}K"
        else:
            return f"{x:.2f}"
    except:
        return "0.0"


def enrich_dte_for_comparison(df):
    """Enrich dataframe with DTE for comparison analysis."""
    df['entry_date'] = pd.to_datetime(df['exit_timestamp']).dt.normalize()
    df['balte_expiry_date'] = pd.to_datetime(df['balte_expiry_date'], dayfirst=True, format='mixed', errors='coerce').dt.normalize()
    
    # Mock ALL_DATES - in real implementation, import from balte.balte_config
    all_dates = pd.date_range(start='2020-01-01', end='2025-12-31', freq='D')
    all_dates_dict = {pd.Timestamp(date).normalize(): index for index, date in enumerate(all_dates)}
    
    df['dte'] = df.apply(
        lambda x: all_dates_dict.get(x['balte_expiry_date'], -1) - all_dates_dict.get(x['entry_date'], -1),
        axis=1
    )
    return df


def load_comparison_data(start_date, end_date, slave_strat, segment='OPTIDX', exchange='IND'):
    """
    Load and prepare data for strategy vs cluster vs backtest comparison.
    This is a mock implementation - in real use, this would integrate with actual data sources.
    """
    # Mock data structure - replace with actual data loading logic
    strategy_data = pd.DataFrame({
        'exit_date': pd.date_range(start=start_date, end=end_date, freq='D'),
        'exit_count': np.random.randint(10, 50, size=pd.date_range(start=start_date, end=end_date, freq='D').shape[0]),
        'pnl': np.random.normal(1000, 500, size=pd.date_range(start=start_date, end=end_date, freq='D').shape[0]),
        'holding_time': np.random.normal(45, 15, size=pd.date_range(start=start_date, end=end_date, freq='D').shape[0])
    })
    
    cluster_data = pd.DataFrame({
        'exit_date': pd.date_range(start=start_date, end=end_date, freq='D'),
        'exit_count': np.random.randint(8, 45, size=pd.date_range(start=start_date, end=end_date, freq='D').shape[0]),
        'pnl': np.random.normal(800, 400, size=pd.date_range(start=start_date, end=end_date, freq='D').shape[0]),
        'holding_time': np.random.normal(42, 12, size=pd.date_range(start=start_date, end=end_date, freq='D').shape[0])
    })
    
    backtest_data = pd.DataFrame({
        'exit_date': pd.date_range(start=start_date, end=end_date, freq='D'),
        'exit_count': np.random.randint(12, 55, size=pd.date_range(start=start_date, end=end_date, freq='D').shape[0]),
        'pnl': np.random.normal(1200, 600, size=pd.date_range(start=start_date, end=end_date, freq='D').shape[0]),
        'holding_time': np.random.normal(48, 18, size=pd.date_range(start=start_date, end=end_date, freq='D').shape[0])
    })
    
    return strategy_data, cluster_data, backtest_data


def calculate_comparison_metrics(strategy_data, cluster_data, backtest_data, short_holding_threshold=5):
    """Calculate comparison metrics between strategy, cluster, and backtest."""
    
    # Strategy vs Cluster metrics
    strategy_vs_cluster = {
        'exit_count_diff': int(strategy_data['exit_count'].sum() - cluster_data['exit_count'].sum()),
        'pnl_diff': float(strategy_data['pnl'].sum() - cluster_data['pnl'].sum()),
        'short_duration_strategy': int((strategy_data['holding_time'] < short_holding_threshold).sum()),
        'short_duration_cluster': int((cluster_data['holding_time'] < short_holding_threshold).sum()),
        'avg_holding_time_diff': float(strategy_data['holding_time'].mean() - cluster_data['holding_time'].mean())
    }
    
    # Strategy vs Backtest metrics
    strategy_vs_backtest = {
        'exit_count_diff': int(backtest_data['exit_count'].sum() - strategy_data['exit_count'].sum()),
        'pnl_diff': float(backtest_data['pnl'].sum() - strategy_data['pnl'].sum()),
        'short_duration_strategy': int((strategy_data['holding_time'] < short_holding_threshold).sum()),
        'short_duration_backtest': int((backtest_data['holding_time'] < short_holding_threshold).sum()),
        'avg_holding_time_diff': float(backtest_data['holding_time'].mean() - strategy_data['holding_time'].mean())
    }
    
    return strategy_vs_cluster, strategy_vs_backtest


def generate_exit_count_comparison_chart(strategy_data, cluster_data, backtest_data):
    """Generate daily exit count comparison chart."""
    fig = go.Figure()
    
    fig.add_trace(go.Scatter(
        x=strategy_data['exit_date'],
        y=strategy_data['exit_count'],
        mode='lines+markers',
        name='Strategy',
        line=dict(color='#4d0000', width=2),
        marker=dict(size=6)
    ))
    
    fig.add_trace(go.Scatter(
        x=cluster_data['exit_date'],
        y=cluster_data['exit_count'],
        mode='lines+markers',
        name='Cluster',
        line=dict(color='#28a745', width=2),
        marker=dict(size=6)
    ))
    
    fig.add_trace(go.Scatter(
        x=backtest_data['exit_date'],
        y=backtest_data['exit_count'],
        mode='lines+markers',
        name='Backtest',
        line=dict(color='#007bff', width=2),
        marker=dict(size=6)
    ))
    
    fig.update_layout(
        title='Daily Exit Count Comparison',
        xaxis_title='Date',
        yaxis_title='Exit Count',
        template="plotly_white",
        autosize=True,
        responsive=True,
        hovermode='x unified'
    )
    
    return json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)


def generate_pnl_comparison_chart(strategy_data, cluster_data, backtest_data):
    """Generate daily PnL comparison chart."""
    fig = go.Figure()
    
    # Calculate cumulative PnL
    strategy_cumulative = strategy_data['pnl'].cumsum()
    cluster_cumulative = cluster_data['pnl'].cumsum()
    backtest_cumulative = backtest_data['pnl'].cumsum()
    
    fig.add_trace(go.Scatter(
        x=strategy_data['exit_date'],
        y=strategy_cumulative,
        mode='lines',
        name='Strategy (Cumulative)',
        line=dict(color='#4d0000', width=3),
        fill='tonexty'
    ))
    
    fig.add_trace(go.Scatter(
        x=cluster_data['exit_date'],
        y=cluster_cumulative,
        mode='lines',
        name='Cluster (Cumulative)',
        line=dict(color='#28a745', width=3)
    ))
    
    fig.add_trace(go.Scatter(
        x=backtest_data['exit_date'],
        y=backtest_cumulative,
        mode='lines',
        name='Backtest (Cumulative)',
        line=dict(color='#007bff', width=3)
    ))
    
    fig.update_layout(
        title='Cumulative PnL Comparison',
        xaxis_title='Date',
        yaxis_title='Cumulative PnL',
        template="plotly_white",
        autosize=True,
        responsive=True,
        hovermode='x unified'
    )
    
    return json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)


def generate_holding_time_distribution_chart(strategy_data, cluster_data, backtest_data):
    """Generate holding time distribution comparison."""
    fig = go.Figure()
    
    fig.add_trace(go.Histogram(
        x=strategy_data['holding_time'],
        name='Strategy',
        opacity=0.7,
        marker_color='#4d0000',
        nbinsx=30
    ))
    
    fig.add_trace(go.Histogram(
        x=cluster_data['holding_time'],
        name='Cluster',
        opacity=0.7,
        marker_color='#28a745',
        nbinsx=30
    ))
    
    fig.add_trace(go.Histogram(
        x=backtest_data['holding_time'],
        name='Backtest',
        opacity=0.7,
        marker_color='#007bff',
        nbinsx=30
    ))
    
    fig.update_layout(
        title='Holding Time Distribution Comparison',
        xaxis_title='Holding Time (minutes)',
        yaxis_title='Frequency',
        barmode='overlay',
        template="plotly_white",
        autosize=True,
        responsive=True
    )
    
    return json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)


def generate_performance_summary_table(strategy_data, cluster_data, backtest_data):
    """Generate performance summary table data."""
    summary_data = []
    
    datasets = [
        ('Strategy', strategy_data),
        ('Cluster', cluster_data),
        ('Backtest', backtest_data)
    ]
    
    for name, data in datasets:
        summary_data.append({
            'type': name,
            'total_exits': int(data['exit_count'].sum()),
            'total_pnl': float(data['pnl'].sum()),
            'avg_pnl': float(data['pnl'].mean()),
            'avg_holding_time': float(data['holding_time'].mean()),
            'max_daily_exits': int(data['exit_count'].max()),
            'min_daily_exits': int(data['exit_count'].min()),
            'pnl_volatility': float(data['pnl'].std())
        })
    
    return summary_data


def process_trade_comparison(start_date, end_date, slave_strat, segment='OPTIDX', exchange='IND'):
    """
    Main function to process trade comparison data and generate all required charts and statistics.
    """
    try:
        # Load data
        strategy_data, cluster_data, backtest_data = load_comparison_data(
            start_date, end_date, slave_strat, segment, exchange
        )
        
        # Calculate metrics
        strategy_vs_cluster, strategy_vs_backtest = calculate_comparison_metrics(
            strategy_data, cluster_data, backtest_data
        )
        
        # Generate charts
        charts = {
            'exit_count_comparison': generate_exit_count_comparison_chart(strategy_data, cluster_data, backtest_data),
            'pnl_comparison': generate_pnl_comparison_chart(strategy_data, cluster_data, backtest_data),
            'holding_time_distribution': generate_holding_time_distribution_chart(strategy_data, cluster_data, backtest_data)
        }
        
        # Generate summary table
        performance_summary = generate_performance_summary_table(strategy_data, cluster_data, backtest_data)
        
        result = {
            'success': True,
            'data': {
                'strategy_vs_cluster': strategy_vs_cluster,
                'strategy_vs_backtest': strategy_vs_backtest,
                'charts': charts,
                'performance_summary': performance_summary,
                'data_availability': {
                    'strategy_range': f"{strategy_data['exit_date'].min()} to {strategy_data['exit_date'].max()}",
                    'cluster_range': f"{cluster_data['exit_date'].min()} to {cluster_data['exit_date'].max()}",
                    'backtest_range': f"{backtest_data['exit_date'].min()} to {backtest_data['exit_date'].max()}"
                }
            },
            'parameters': {
                'slave_strategy': slave_strat,
                'segment': segment,
                'exchange': exchange,
                'date_range': f"{start_date} to {end_date}"
            }
        }
        
        return result
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }
