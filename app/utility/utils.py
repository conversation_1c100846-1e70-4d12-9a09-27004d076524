import json
from urllib import request
from flask import current_app
from flask_login import current_user
from app.models import minio
import pandas as pd
from io import BytesIO
from typing import Any, Dict, List


def send_message_to_teams(text: str):
    """Sends message on teams in strategy acceptance channel.

    Args:
        text (str): Msg to send
    """
    text = current_user.username + " : " + text
    post = {
        "@context": "https://schema.org/extensions",
        "@type": "MessageCard",
        "themeColor": "0072C6",
        "title": "",
        "text": text,
    }

    try:
        json_data = json.dumps(post)
        req = request.Request(
            current_app.config["TEAMS_WEBHOOK"],
            data=json_data.encode("ascii"),
            headers={"Content-Type": "application/json"},
        )
        request.urlopen(req)
    except Exception as em:
        current_app.logger.warning(
            f"Error in sending message to teams due to error {em}"
        )
        pass


def list_files_in_folder(folder_name: str) -> List[str]:
    """Get all files in folder name

    Args:
        folder_name (str): Folder name for which files is needed

    Returns:
        List[str]: list of file names
    """
    files = []
    try:
        objects = minio.client.list_objects(
            current_app.config["DASHBOARD_DATA_BUCKET"],
            prefix=folder_name,
            recursive=True,
        )
        for obj in objects:
            if obj.object_name != folder_name:  # Exclude the folder itself
                files.append(obj.object_name)
    except Exception as err:
        current_app.logger.error(
            f"Fetching files from minio for folder {folder_name} failed with error {err}"
        )
    return files


def get_files_from_minio(
    status: str, strategy_name: str, post_fix: str, cluster_name: str = ""
):
    """Fetches files from minio.

    Args:
        status (str): Status of the strategy
        strategy_name (str): Name of the strategy
        post_fix (str): Indicates the type of file to fetch

    Raises:
        Exception: If no file with the given name is found

    Returns:
        data: The required file
    """
    try:
        minio_path = current_app.config["STRAT_STATUS_PATH_MAPPING"][status]
        if cluster_name == "":
            data = minio.client.get_object(
                current_app.config["DASHBOARD_DATA_BUCKET"],
                f"{minio_path}/{strategy_name}/{strategy_name}{post_fix}",
            ).data
        else:
            data = minio.client.get_object(
                current_app.config["DASHBOARD_DATA_BUCKET"],
                f"{minio_path}/{strategy_name}/{strategy_name}_{cluster_name}{post_fix}",
            ).data
        return data
    except Exception:
        current_app.logger.warning(f"File {strategy_name}{post_fix} not found")
        raise Exception("File not found!")


def get_file_data_from_minio(path: str) -> bytes:
    """Return bytes data of a file at specified path in minio

    Args:
        path (str): location of the file in minio

    Returns:
        bytes: bytes data of the file
    """
    try:
        data = minio.client.get_object(
            current_app.config["DASHBOARD_DATA_BUCKET"],
            path,
        ).data
        return data
    except Exception:
        current_app.logger.warning(f"File {path} not found")
        raise Exception("File not found!")


def upload_dataframe_to_minio(
    path: str, dataframe: pd.DataFrame, parquet: bool = False
) -> None:
    """Uploads the given dataframe to minio at the specified path

    Args:
        path (str): location of the file at minio where dataframe has to be uploaded
        df (pd.DataFrame): dataframe to be uploaded
        parquet (bool, optional): If True, dataframe is uploaded as parquet file. Defaults to False.
    """
    if parquet:
        bytes = dataframe.to_parquet()
    else:
        bytes = dataframe.to_csv().encode("utf-8")
    minio.client.put_object(
        current_app.config["DASHBOARD_DATA_BUCKET"],
        path,
        data=BytesIO(bytes),
        length=len(bytes),
    )


def on_send_success(record_metadata):
    pass


def on_send_error(excp):
    current_app.logger.error("Fail_to_send_msgs_to_kafka", exc_info=excp)


def send_message_to_kafka(text: str):
    """Sends message to kafka.

    Args:
        text (str): Msg to send

    """
    current_app.config["KAFKA_PRODUCER"].send(
        current_app.config["KAFKA_TOPIC"], text
    ).add_callback(on_send_success).add_errback(on_send_error)
