#   Copyright 2017-18 Soroco Americas Private Limited
#
#   Licensed under the Apache License, Version 2.0 (the "License");
#   you may not use this file except in compliance with the License.
#   You may obtain a copy of the License at
#
#       http://www.apache.org/licenses/LICENSE-2.0
#
#   Unless required by applicable law or agreed to in writing, software
#   distributed under the License is distributed on an "AS IS" BASIS,
#   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#   See the License for the specific language governing permissions and
#   limitations under the License.
#
#   Primary Author: <PERSON> <<EMAIL>>
#
#   Purpose: Provide an example file to encrypt / decrypt.
"""A simple Hello World Python 3 example script."""


def hello() -> None:
    """
    This function is just a demo function designed to be called as a test of
    the decryption import machinery.

    Returns:
        None
    """
    print('Hello World!')


if __name__ == '__main__':
    print('Hello World!')
