<div align="center">

# <center> Strategy Addition Management and Business Analytics : SAMBA </center>

</div>

A flask dashboard application for the addition, reviewing and management of strategies
### Setup

* Clone the repo
    >In order to clone the repo, you must have the SSH keys setup. Refer to [balte docs](http://192.168.0.155:15000/balte/balte/-/wikis/BaLTE/BaLTE-Developer-Setup#setup-ssh-keys) for more information

* The virtual environment can be made by running 

    ```conda env create -f analytics_dashboard.yml```

    >The above command will generate the virtual environment having all the dependencies to run SAMBA

### How to setup docker

* Clone the repo

* ```docker build -t samba:latest .```
>This should create an image tagged <b>samba:latest</b>. This can be checked using the command ```docker images```

* ```docker run -d -v "LOCAL_PATH":/samba/SAMBA_logs.log -p port:port samba:latest "MODE"```
>Use this command to run the docker in detached mode on the specified port.
By default, the service runs in DEVELOPMENT mode but you can change the MODE to "live" or "production". The default port is 9005.

* Use ```docker ps``` to make sure that the service is up and running

* To stop the service, use ```docker stop service_name```

* To remove dangling images, use ```docker system prune``` or ```docker rmi -f $(docker images -q --filter "dangling=true")```
>Dangling images are the ones which are currently not referenced by any container and their names are displayed as &#60;none&#62; when docker images command is used. It is a good idea to delete the dangling images every once in a while as it leads to wastage of space.

### Documentation
* The docs are being hosted along with the application itself at route ```/apidocs```
* They have been compiled using library ```flasgger``` and shows all the end-points of our application, along with the methods they support and the parameters they require.

### Testing and Coverage Analysis
* The tests have been written using the ```unittest``` library and can be run using the command ```python -m uniitest```

<div align="center">

#### Coverage Report
    Name                               Stmts   Miss  Cover 
    ------------------------------------------------------
    app/auth/auth_forms.py                29      0   100%
    app/auth/auth_views.py               173      1    99%
    app/main/views.py                     20      0   100%
    app/strat_add/strat_add_forms.py      56      2    96%  
    app/strat_add/strat_add_views.py     579      3    99%
    app/utility/strat_detail_util.py      75      5    93%
    app/utility/strat_review_util.py     206     14    93%
    TOTAL                               1138     25    98%
    -------------------------------------------------------

</div>

## Understanding the WorkFlow

### A closer look at the User Hierarchy

<div align="center">


```mermaid
graph TD;
ADMIN_1-->MANAGER_1-->DEVELOPER_1
ADMIN_1-->MANAGER_2-->DEVELOPER_3
ADMIN_1-->MANAGER_3-->DEVELOPER_5
ADMIN_2-->MANAGER_1-->DEVELOPER_2
ADMIN_2-->MANAGER_2-->DEVELOPER_4
ADMIN_2-->MANAGER_3-->DEVELOPER_6
```
</div>

* At the topmost level, we have the ```ADMIN``` role. This user has universal access to all strategies, and can review, accept, reject or kill any strategy. They can also register a user at any level.
* Next, we have the ```MANAGER``` role. This user has access to all the strategies developed by the developers under him. They can also register a new user as a developer or another manager.
* Finally, we have the ```DEVELOPER``` role. This user only has access to their own strategies. They can submit, delete and modify their strategy but cannot review, accept, reject or kill it.
<table>
    <tr>
        <td><b>Role</b></td>
        <td><b>ADMIN</b<</td>
        <td><b>MANAGER</b></td>
        <td><b>DEVELOPER</b></td>
    </tr>
    <tr>
        <td><b>User Registration</b></td>
        <td>Yes(manager + developer)</td>
        <td>No</td>
    </tr>
    <tr>
        <td><b>User Modification</b></td>
        <td>Yes</td>
        <td>Yes(self + developers)</td>
        <td>No</td>
    </tr>
    <tr>
        <td><b>Strategy Addition, Modification & Deletion</b></td>
        <td>Yes</td>
        <td>Yes</td>
        <td>Yes</td>
    </tr>
    <tr>
        <td><b>Strategy Reviewing</b></td>
        <td>Yes</td>
        <td>Yes (self + developers)</td>
        <td>No</td>
    </tr>
    <tr>
        <td><b>Strategy Accepting, Rejecting & Killing</b></td>
        <td>Yes</td>
        <td>Yes (self + developers)</td>
        <td>No</td>
    </tr>
    <tr>
        <td><b>Strategy Expanding</b></td>
        <td>Yes (pending, live, rejected, dead - any user)</td>
        <td>Yes (pending, live, rejected, dead - self + developers)</td>
        <td>Yes (pending, live, rejected, dead - self)</td>
    </tr>
    <tr>
        <td><b>Performance Metrics</b></td>
        <td>Yes (pending, live, rejected, dead - any user)</td>
        <td>Yes (pending, live, rejected, dead - self + developers)</td>
        <td>Yes (live, rejected, dead - self)</td>
    </tr>
    <tr>
        <td><b>Kivifolio Report</b></td>
        <td>Yes (pending, live, rejected, dead - any user)</td>
        <td>Yes (pending, live, rejected, dead - self + developers)</td>
        <td>Yes (live, rejected, dead - self)</td>
    </tr>
    <tr>
        <td><b>Cluster Performance</b></td>
        <td>Yes (pending, live, rejected, dead - any user)</td>
        <td>Yes (pending, live, rejected, dead - self + developers)</td>
        <td>No</td>
    </tr>
    <tr>
        <td><b>Trigger backtest for a strategy</b></td>
        <td>Yes (pending - any user)</td>
        <td>Yes (pending - self + developers)</td>
        <td>No</td>
    </tr>
    <tr>
        <td><b>Download Strategy</b></td>
        <td>Yes (pending - any user)</td>
        <td>Yes (pending - self + developers)</td>
        <td>No</td>
    </tr>
    <tr>
        <td><b>View strategy reviews</b></td>
        <td>Yes (pending, live, rejected, dead - any user)</td>
        <td>Yes (pending, live, rejected, dead - self + developers)</td>
        <td>Yes (pending, live, rejected, dead - self)</td>
    </tr>
    <tr>
        <td><b>Strategy reviewing (Correlation) results</b></td>
        <td>Yes (pending - any user)</td>
        <td>Yes (pending - self + developers)</td>
        <td>No</td>
    </tr>
</table>

## Available Features

```NOTE: Open the hyperlinks in a new tab to use auto-searching of function names```
### Strategy Addition
* Once satisfied, users can submit their strategies for reviewal using the [Strategy Addition Form](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/strat_add/strat_add_forms.py#:~:text=StrategySubmissionForm)
* [Template](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/templates/strat_add/submission.html)
* [add_strategy()](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/strat_add/strat_add_views.py#:~:text=add_strategy())

### Strategy Modification
* The user has an option to modify their strategy after submission using the [Strategy Modification Form](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/strat_add/strat_add_forms.py#:~:text=StrategySubmissionForm). This will also trigger a new backtest for the strategy and put it in pending state.
* [Template](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/templates/strat_add/submission.html)
* [modify_strategy(strat_name: str)](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/strat_add/strat_add_views.py#:~:text=modify_strategy(strat_name )

### Strategy Deletion
* The user can delete their submitted strategy using the [delete_strategy(strategy_name: str)](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/strat_add/strat_add_views.py#:~:text=delete_strategy(strategy_name ) function.
* This will completely erase all the associated data from the DB and minio.

### Get Correlation
* This gives correlation between the selected pending strategies and the live and dead strategies.
* [Template](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/templates/strat_add/correlation_analysis.html)
* [correlation_analysis(review_strategy_list: list)](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/strat_add/strat_add_views.py#:~:text=correlation_analysis(review_strategy_list )

### Strategy Review
* This enlists all the pending strategies to the user. It is used to send out requests for reviewing selected strategies.
* [Template](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/templates/strat_add/review_strategy_dashboard.html)
* [review_strategy_dashboard()](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/strat_add/strat_add_views.py#:~:text=review_strategy_dashboard())

### Strategy Expansion
* This gives out various details about the strategy. Some of these details are the ones entered by the user at the time of the submission, while others are meta information obtained after running backtests.
* This page also displays the Next Function profiler analysis [get_next_function_chart()](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/strat_add/strat_add_views.py#:~:text=get_next_function_chart()) for the strategy, along with the Monte Carlo, Scatter Plot of BackTest Returns and the Cumulative Distribution of DD (%) charts.
* [Template](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/templates/strat_add/expand_strat.html)
* [expand_strategy(strategy_name: str)](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/strat_add/strat_add_views.py#:~:text=expand_strategy(strategy_name )

### Download Strategy
* Allows the user to download the strategy code, along with the tradelog and mtm file.
* [download_strategy()](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/strat_add/strat_add_views.py#:~:text=download_strategy())

### Performance Metrics
* Displays the performance metric file (generated after running the backtest).
* [Template](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/templates/strat_add/performance_metric.html)
* [performance_metrics(strategy_name: str)](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/strat_add/strat_add_views.py#:~:text=performance_metrics(strategy_name )

### Kivifolio Report
* Displays the kivifolio report (generated after running the backtest).
* [Template](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/templates/strat_add/kivifolio_report.html)
* [kivifolio_report(strategy_name: str)](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/strat_add/strat_add_views.py#:~:text=kivifolio_report(strategy_name )

### Strategy Management
* Dashboard to manage all the live, dead and rejected strategies.
* [Template](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/templates/strat_add/strat_management.html)
* [strategy_manager()](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/strat_add/strat_add_views.py#:~:text=strategy_manager())

### Run backtest
* This feature allows the user to trigger a new backtest for the strategy and is only available for pending strategies.
* [run_backtest()](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/strat_add/strat_add_views.py#:~:text=run_backtest())

### Cluster Performance
* Displays the cluster performance report (generated after running the backtest).
* [Template](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/templates/strat_add/cluster_performance.html)
* [cluster_performance(strategy_name: str, cluster_mapping: str)](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/strat_add/strat_add_views.py#:~:text=cluster_performance(strategy_name )

### Make strategy dead
* This feature is used to kill a currently live strategy and also removes it from allt the linked cluster mappings and limit order lists (CASH strategies).
* [make_strategy_dead()](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/strat_add/strat_add_views.py#:~:text=make_strategy_dead())

### Strategy Review Form
* This feature allows the user to enter comments for pending strategies using the [Strategy Review Form](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/strat_add/strat_add_forms.py#:~:text=StrategyReviewForm)
* [Template](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/templates/strat_add/strategy_review.html)
* [strategy_review(strategy_name: str)](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/strat_add/strat_add_views.py#:~:text=strategy_review(strategy_name )

### View Strategy Review
* This feature displays the strategy review comments (given by the admin or manager).
* [Template](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/templates/strat_add/strategy_review_display.html)
* [show_strategy_review(strategy_name: str)](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/strat_add/strat_add_views.py#:~:text=show_strategy_review(strategy_name )

### Comments Dashboard
* A dashboard for displaying all the pending strategies along with the review comments given for them so far.
* [Template](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/templates/strat_add/comments_dashboard.html)
* [comments_dashboard()](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/strat_add/strat_add_views.py#:~:text=comments_dashboard())

### User Registration
* This feature allows the registration of the new user using the [Registration Form](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/auth/auth_forms.py#:~:text=RegistrationForm)
* [Template](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/templates/auth/register.html)
* [register()](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/auth/auth_views.py#:~:text=register())

### Login
* This allows the user to login and get access to their dashboard using the [Login Form](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/auth/auth_forms.py#:~:text=LoginForm)
* [Template](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/templates/auth/login.html)
* [login()](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/auth/auth_views.py#:~:text=login())

### Change Password
* This feature allows the user to change their own password using the [Password Change Form](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/auth/auth_forms.py#:~:text=ChangePasswordForm)
* [Template](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/templates/auth/profile.html)
* [change_password()](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/auth/auth_views.py#:~:text=change_password())

### User Management
* Displays a dashboard for managing all the users accessible to the current user.
* [Template](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/templates/auth/user_management.html)
* [manage_users()](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/auth/auth_views.py#:~:text=manage_users())

### Edit user role
* This feature allows the user to change the role of all the users accesible to him.
* [edit_users()](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/auth/auth_views.py#:~:text=edit_users())

### Edit user password
* This feature allows the user to change the password of all the users accesible to him.
* [edit_user_password()](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/auth/auth_views.py#:~:text=edit_user_password())

### Delete a user
* This feature allows the user to delte any user accesible to him.
* [delete_user()](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/auth/auth_views.py#:~:text=delete_user())

### Home page
* This page gives user the access to manage their pending strategies.
* [Template](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/templates/home.html)
* [index()](http://192.168.0.155:15000/divyansh.aggarwal/samba/-/blob/master/app/main/views.py#:~:text=index())


### A closer look at the WorkFlow

<div align="center">

```mermaid
graph TD;
A["Strategy_uploaded<br> <br>Strategy state is PENDING"]-- Backtest trigerred --> B["Run_backtest<br> <br>This process is done by the backend service"]
B-->C{"Success?"}-. No .-> D["Error displayed<br> <br>Wait for strategy to be modified to run backtest again"]
D-->Q{"Strategy modified?"}-. No .-> D
Q-. Yes .-> B
C-. Yes .-> E["Following files are generated:<br> <br>1. Performance metrics file<br> <br>2. Kivifolio report<br> <br>3. Meta information<br> <br>4. Next function Profiler<br> <br>5. Cluster Performance report (if applicable)"]
E-->F["Get correlation with pending, live and dead strategies"]-->G["Review & Analyse the reports"]-->H["Give comments"]-->I{"Strategy modified?"}-. Yes .->B
I-. No .-> J{"Decision"}-. Save .-> K["Wait for further analysis & review"]-->H
J-. Accept .-> L["Accept strategy and push it to live<br> <br>Strategy state changed to LIVE"]
J-. Reject .-> M["Reject strategy<br> <br>Strategy state is changed to REJECTED"]
L-->N["Runing in live"]-->O{"Killed?"}-. No .-> N
O-. Yes .-> P["Kill strategy and remove it from live<br> <br>Strategy state is changed to DEAD"]
```
</div>